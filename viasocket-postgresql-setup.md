# ViaSocket PostgreSQL Integration Setup for Grid2Play

## 🔗 Database Connection Configuration

### Connection Details
```json
{
  "connection_name": "grid2play_database",
  "host": "your-supabase-host.supabase.co",
  "port": 5432,
  "database": "postgres",
  "username": "postgres",
  "password": "your-supabase-password",
  "ssl_mode": "require",
  "connection_timeout": 30,
  "query_timeout": 60
}
```

### Connection String Format
```
postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require
```

## 📊 Main Query Configuration

### Primary Query for Daily Reports
```sql
SELECT 
  venue_id,
  venue_name,
  admin_phone,
  admin_name,
  report_date,
  total_bookings,
  confirmed_bookings,
  cancelled_bookings,
  gross_revenue,
  net_revenue,
  coupon_usage_count
FROM get_venue_daily_summary(CURRENT_DATE);
```

### Query Parameters
- **Query Type**: SELECT
- **Return Format**: JSON Array
- **Timeout**: 60 seconds
- **Retry Count**: 3
- **Retry Delay**: 5 seconds

### Expected Output Schema
```json
{
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "venue_id": {"type": "string", "format": "uuid"},
      "venue_name": {"type": "string"},
      "admin_phone": {"type": "string"},
      "admin_name": {"type": "string"},
      "report_date": {"type": "string", "pattern": "^\\d{2}/\\d{2}/\\d{4}$"},
      "total_bookings": {"type": "string"},
      "confirmed_bookings": {"type": "string"},
      "cancelled_bookings": {"type": "string"},
      "gross_revenue": {"type": "string"},
      "net_revenue": {"type": "string"},
      "coupon_usage_count": {"type": "string"}
    }
  }
}
```

## 🔍 Validation Queries

### Test Database Connection
```sql
SELECT 1 as connection_test;
```

### Verify Function Exists
```sql
SELECT EXISTS (
  SELECT 1 
  FROM pg_proc p 
  JOIN pg_namespace n ON p.pronamespace = n.oid 
  WHERE n.nspname = 'public' 
  AND p.proname = 'get_venue_daily_summary'
) as function_exists;
```

### Check Venue Admin Data
```sql
SELECT 
  COUNT(*) as total_venue_admins,
  COUNT(CASE WHEN p.phone IS NOT NULL AND p.phone != '' THEN 1 END) as admins_with_phone,
  COUNT(CASE WHEN p.phone_verified = true THEN 1 END) as verified_phones
FROM venue_admins va
JOIN profiles p ON va.user_id = p.id;
```

### Verify Recent Booking Data
```sql
SELECT 
  COUNT(*) as total_bookings_today,
  COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_today,
  SUM(CASE WHEN status IN ('confirmed', 'completed') THEN total_price ELSE 0 END) as revenue_today
FROM bookings 
WHERE booking_date = CURRENT_DATE;
```

## ⚙️ ViaSocket Configuration Steps

### Step 1: Add PostgreSQL App
1. Go to ViaSocket Dashboard
2. Click "Add App" → Search "PostgreSQL"
3. Click "Connect" and enter connection details
4. Test connection with validation query

### Step 2: Configure Database Trigger
```json
{
  "trigger_type": "scheduled_query",
  "schedule": {
    "type": "daily",
    "time": "23:00",
    "timezone": "Asia/Kolkata"
  },
  "query": "SELECT venue_id, venue_name, admin_phone, admin_name, report_date, total_bookings, confirmed_bookings, cancelled_bookings, gross_revenue, net_revenue, coupon_usage_count FROM get_venue_daily_summary(CURRENT_DATE);",
  "output_format": "json_array",
  "error_handling": {
    "on_empty_result": "continue",
    "on_query_error": "retry_3_times",
    "on_connection_error": "alert_admin"
  }
}
```

### Step 3: Set Up Data Validation
```javascript
// ViaSocket data validation script
function validateDatabaseOutput(data) {
  const errors = [];
  
  if (!Array.isArray(data)) {
    errors.push("Output is not an array");
    return { valid: false, errors };
  }
  
  data.forEach((record, index) => {
    // Required fields validation
    const requiredFields = ['venue_id', 'venue_name', 'admin_phone', 'report_date'];
    requiredFields.forEach(field => {
      if (!record[field]) {
        errors.push(`Record ${index}: Missing ${field}`);
      }
    });
    
    // Phone number validation
    if (record.admin_phone && !record.admin_phone.match(/^\+?91\d{10}$/)) {
      errors.push(`Record ${index}: Invalid phone format`);
    }
    
    // Date format validation
    if (record.report_date && !record.report_date.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
      errors.push(`Record ${index}: Invalid date format`);
    }
  });
  
  return {
    valid: errors.length === 0,
    errors: errors,
    record_count: data.length
  };
}
```

## 🚨 Error Handling Configuration

### Database Connection Errors
```json
{
  "error_type": "connection_timeout",
  "action": "retry",
  "max_retries": 3,
  "retry_delay": 10,
  "fallback_action": "send_admin_alert"
}
```

### Query Execution Errors
```json
{
  "error_type": "query_failed",
  "action": "log_and_continue",
  "notification": {
    "send_to": "<EMAIL>",
    "include_query": true,
    "include_error_details": true
  }
}
```

### Empty Result Handling
```json
{
  "error_type": "no_data_returned",
  "action": "continue_workflow",
  "log_message": "No venues with activity found for today",
  "send_summary": true
}
```

## 📈 Performance Optimization

### Query Optimization Tips
1. **Use Indexes**: Ensure proper indexes on booking_date, venue_id
2. **Limit Results**: Add reasonable limits if needed
3. **Connection Pooling**: Enable in ViaSocket settings
4. **Query Caching**: Cache results for 1 hour if running multiple times

### Recommended Indexes
```sql
-- Performance indexes for the daily summary function
CREATE INDEX IF NOT EXISTS idx_bookings_date_status_venue 
ON bookings(booking_date, status, court_id);

CREATE INDEX IF NOT EXISTS idx_coupon_usage_date_booking 
ON coupon_usage(created_at, booking_id);

CREATE INDEX IF NOT EXISTS idx_venue_admins_venue_user 
ON venue_admins(venue_id, user_id);
```

## 🔐 Security Configuration

### Database Permissions
```sql
-- Create read-only user for ViaSocket (recommended)
CREATE USER viasocket_reader WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE postgres TO viasocket_reader;
GRANT USAGE ON SCHEMA public TO viasocket_reader;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO viasocket_reader;
GRANT EXECUTE ON FUNCTION get_venue_daily_summary(DATE) TO viasocket_reader;
```

### Connection Security
- ✅ Use SSL/TLS encryption
- ✅ Restrict IP access if possible
- ✅ Use strong passwords
- ✅ Regular password rotation
- ✅ Monitor connection logs

## 🧪 Testing Checklist

### Pre-Production Testing
- [ ] Database connection successful
- [ ] Function returns expected data format
- [ ] Phone numbers are properly formatted
- [ ] Revenue amounts include ₹ symbol
- [ ] Date format is DD/MM/YYYY
- [ ] Only active venues with verified admins included
- [ ] Error handling works for edge cases

### Test Queries
```sql
-- Test with specific date
SELECT * FROM get_venue_daily_summary('2025-01-04');

-- Test with no data date
SELECT * FROM get_venue_daily_summary('2020-01-01');

-- Test function performance
EXPLAIN ANALYZE SELECT * FROM get_venue_daily_summary(CURRENT_DATE);
```

## 📊 Monitoring & Logging

### ViaSocket Monitoring Setup
```json
{
  "monitoring": {
    "track_execution_time": true,
    "log_query_results": true,
    "alert_on_slow_queries": 30,
    "daily_summary_report": true,
    "retention_days": 30
  }
}
```

### Key Metrics to Track
- Query execution time
- Number of records returned
- Connection success rate
- Error frequency
- Data validation failures

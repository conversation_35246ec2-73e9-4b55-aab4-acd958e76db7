# Grid2Play Daily Venue Reports - Testing & Validation Guide

## 🧪 Pre-Deployment Testing Checklist

### Database Function Testing
- [ ] **Function Deployment**: `get_venue_daily_summary()` function deployed successfully
- [ ] **Function Execution**: Function returns data without errors
- [ ] **Data Format**: Output matches expected JSON schema
- [ ] **Performance**: Query executes within 30 seconds
- [ ] **Edge Cases**: Handles dates with no bookings gracefully

### ViaSocket Integration Testing
- [ ] **PostgreSQL Connection**: Database connection established successfully
- [ ] **MSG91 Connection**: MSG91 API authentication working
- [ ] **Template Approval**: `daily_venue_report` template approved and active
- [ ] **Data Flow**: Data flows correctly from DB to MSG91
- [ ] **Error Handling**: Proper error handling for all failure scenarios

## 🔍 Step-by-Step Testing Process

### Step 1: Database Function Validation

#### Test Query Execution
```sql
-- Test with current date
SELECT * FROM get_venue_daily_summary(CURRENT_DATE);

-- Test with specific date (replace with actual date)
SELECT * FROM get_venue_daily_summary('2025-01-04');

-- Test with date having no bookings
SELECT * FROM get_venue_daily_summary('2020-01-01');
```

#### Expected Output Validation
```json
[
  {
    "venue_id": "uuid-format",
    "venue_name": "String (not empty)",
    "admin_phone": "10-digit number",
    "admin_name": "String (not empty)",
    "report_date": "DD/MM/YYYY format",
    "total_bookings": "Numeric string",
    "confirmed_bookings": "Numeric string",
    "cancelled_bookings": "Numeric string",
    "gross_revenue": "₹X,XXX format",
    "net_revenue": "₹X,XXX format",
    "coupon_usage_count": "Numeric string"
  }
]
```

#### Data Quality Checks
```sql
-- Verify venue admin relationships
SELECT 
  v.name as venue_name,
  p.full_name as admin_name,
  p.phone as admin_phone,
  p.phone_verified,
  CASE 
    WHEN p.phone ~ '^\+91[6-9]\d{9}$' THEN 'Valid'
    ELSE 'Invalid'
  END as phone_format_status
FROM venues v
JOIN venue_admins va ON v.id = va.venue_id
JOIN profiles p ON va.user_id = p.id
WHERE v.is_active = true
ORDER BY v.name;
```

### Step 2: ViaSocket Workflow Testing

#### Manual Workflow Execution
1. **Login to ViaSocket Dashboard**
2. **Navigate to Workflow**: "Grid2Play Daily Venue Reports"
3. **Click "Test Run"**: Execute workflow manually
4. **Monitor Execution**: Check each step for success/failure
5. **Verify Output**: Confirm messages sent successfully

#### Test Data Preparation
```sql
-- Insert test booking data for today
INSERT INTO bookings (
  court_id, user_id, booking_date, start_time, end_time, 
  total_price, status, booking_reference, payment_status
) VALUES (
  (SELECT id FROM courts LIMIT 1),
  (SELECT id FROM profiles LIMIT 1),
  CURRENT_DATE,
  '10:00:00',
  '11:00:00',
  1500.00,
  'confirmed',
  'TEST-' || EXTRACT(EPOCH FROM NOW())::TEXT,
  'completed'
);
```

### Step 3: MSG91 Template Validation

#### Template Variable Testing
```bash
# Test MSG91 API with sample data
curl --location --request POST \
'https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/' \
--header 'Content-Type: application/json' \
--header 'authkey: YOUR_AUTH_KEY' \
--data-raw '{
  "integrated_number": "919211848599",
  "content_type": "template",
  "payload": {
    "messaging_product": "whatsapp",
    "type": "template",
    "template": {
      "name": "daily_venue_report",
      "language": {
        "code": "en_US",
        "policy": "deterministic"
      },
      "namespace": "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
      "to_and_components": [
        {
          "to": ["+919876543210"],
          "components": {
            "header_1": {"type": "text", "value": "Test Arena"},
            "body_1": {"type": "text", "value": "Test Arena"},
            "body_2": {"type": "text", "value": "04/01/2025"},
            "body_3": {"type": "text", "value": "8"},
            "body_4": {"type": "text", "value": "7"},
            "body_5": {"type": "text", "value": "1"},
            "body_6": {"type": "text", "value": "₹3,500"},
            "body_7": {"type": "text", "value": "₹3,325"},
            "body_8": {"type": "text", "value": "2"}
          }
        }
      ]
    }
  }
}'
```

#### Expected WhatsApp Message Format
```
🏟️ Test Arena Daily Report

📅 Test Arena - 04/01/2025

📊 Today's Performance:
🎯 Total Bookings: 8
✅ Confirmed: 7
❌ Cancelled: 1

💰 Revenue Summary:
💵 Gross: ₹3,500
💸 Net Earnings: ₹3,325

🎟️ Coupons Used: 2

Tap below for detailed analytics.

Grid२Play - Sports Booking Platform

[View Dashboard]
```

### Step 4: Phone Number Validation Testing

#### Phone Format Test Cases
```javascript
// Test phone number formatting
const testPhones = [
  "9876543210",      // Should become +919876543210
  "09876543210",     // Should become +919876543210
  "+919876543210",   // Should remain +919876543210
  "919876543210",    // Should become +919876543210
  "98765",           // Should be rejected (too short)
  "12345678901",     // Should be rejected (invalid format)
  "",                // Should be rejected (empty)
  null               // Should be rejected (null)
];

testPhones.forEach(phone => {
  const formatted = formatPhoneNumber(phone);
  console.log(`${phone} -> ${formatted}`);
});
```

### Step 5: Error Handling Validation

#### Database Error Scenarios
```sql
-- Test function with invalid date
SELECT * FROM get_venue_daily_summary('invalid-date');

-- Test with future date
SELECT * FROM get_venue_daily_summary('2030-12-31');

-- Test function permissions
SET ROLE 'test_user';
SELECT * FROM get_venue_daily_summary(CURRENT_DATE);
```

#### MSG91 Error Scenarios
- **Invalid Auth Key**: Test with wrong authentication
- **Template Not Found**: Test with non-existent template
- **Invalid Phone**: Test with malformed phone numbers
- **Rate Limiting**: Test with rapid successive calls

### Step 6: End-to-End Integration Testing

#### Complete Workflow Test
1. **Setup Test Environment**
   - Create test venue with admin
   - Add test bookings for today
   - Verify admin phone number

2. **Execute Full Workflow**
   - Trigger ViaSocket workflow
   - Monitor database query execution
   - Track data processing steps
   - Verify MSG91 API calls
   - Confirm WhatsApp message delivery

3. **Validate Results**
   - Check admin receives WhatsApp message
   - Verify message content accuracy
   - Confirm all template variables populated
   - Test dashboard link functionality

## 📊 Performance Testing

### Load Testing Scenarios
```sql
-- Test with multiple venues (simulate production load)
SELECT COUNT(*) as venue_count FROM (
  SELECT * FROM get_venue_daily_summary(CURRENT_DATE)
) as results;

-- Measure query execution time
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM get_venue_daily_summary(CURRENT_DATE);
```

### ViaSocket Performance Metrics
- **Database Query Time**: < 30 seconds
- **Data Processing Time**: < 10 seconds per venue
- **MSG91 API Response Time**: < 5 seconds per message
- **Total Workflow Time**: < 5 minutes for 50 venues

## 🚨 Error Monitoring Setup

### ViaSocket Error Alerts
```json
{
  "error_monitoring": {
    "database_connection_failure": {
      "alert": true,
      "recipients": ["<EMAIL>"],
      "retry_attempts": 3
    },
    "msg91_api_failure": {
      "alert": true,
      "continue_workflow": true,
      "log_details": true
    },
    "template_variable_error": {
      "alert": true,
      "skip_record": true,
      "log_venue_details": true
    }
  }
}
```

### Success Rate Monitoring
- **Target Success Rate**: > 95%
- **Acceptable Failure Rate**: < 5%
- **Critical Failure Threshold**: > 10%

## ✅ Production Readiness Checklist

### Pre-Production Validation
- [ ] All test cases pass successfully
- [ ] Error handling works for all scenarios
- [ ] Phone number validation is accurate
- [ ] MSG91 template renders correctly
- [ ] Database performance is acceptable
- [ ] ViaSocket workflow executes reliably
- [ ] Monitoring and alerting configured
- [ ] Documentation is complete and accurate

### Go-Live Preparation
- [ ] Schedule workflow for 11:00 PM IST
- [ ] Enable production database connection
- [ ] Configure production MSG91 credentials
- [ ] Set up monitoring dashboards
- [ ] Prepare rollback plan
- [ ] Notify stakeholders of go-live date

### Post-Deployment Monitoring
- [ ] Monitor first week execution logs
- [ ] Track message delivery rates
- [ ] Collect admin feedback
- [ ] Optimize based on performance data
- [ ] Document lessons learned

## 🔧 Troubleshooting Guide

### Common Issues and Solutions

#### Database Issues
- **Connection Timeout**: Increase timeout, check network
- **Function Not Found**: Verify function deployment
- **No Data Returned**: Check venue activity for date
- **Performance Issues**: Review query execution plan

#### MSG91 Issues
- **Authentication Failed**: Verify auth key
- **Template Not Approved**: Check MSG91 dashboard
- **Delivery Failed**: Verify phone number format
- **Rate Limited**: Implement delays between calls

#### ViaSocket Issues
- **Workflow Failed**: Check step-by-step execution logs
- **Data Mapping Error**: Verify variable mappings
- **Iterator Issues**: Check array format and structure
- **Timeout Errors**: Increase step timeouts

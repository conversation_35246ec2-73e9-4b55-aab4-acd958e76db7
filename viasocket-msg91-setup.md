# ViaSocket MSG91 Integration Setup for Grid2Play Daily Reports

## 🔗 MSG91 Connection Configuration

### Authentication Setup
```json
{
  "connection_name": "grid2play_msg91",
  "auth_key": "YOUR_MSG91_AUTH_KEY",
  "integrated_number": "919211848599",
  "api_endpoint": "https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/",
  "timeout": 30,
  "retry_count": 2
}
```

### Template Configuration
```json
{
  "template_name": "daily_venue_report",
  "language_code": "en_US",
  "namespace": "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
  "template_type": "whatsapp_template",
  "approved": true
}
```

## 📱 WhatsApp Template Structure

### Complete MSG91 Payload Format
```json
{
  "integrated_number": "919211848599",
  "content_type": "template",
  "payload": {
    "messaging_product": "whatsapp",
    "type": "template",
    "template": {
      "name": "daily_venue_report",
      "language": {
        "code": "en_US",
        "policy": "deterministic"
      },
      "namespace": "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
      "to_and_components": [
        {
          "to": ["{{recipient_phone}}"],
          "components": {
            "header_1": {
              "type": "text",
              "value": "{{venue_name}}"
            },
            "body_1": {
              "type": "text",
              "value": "{{venue_name}}"
            },
            "body_2": {
              "type": "text",
              "value": "{{report_date}}"
            },
            "body_3": {
              "type": "text",
              "value": "{{total_bookings}}"
            },
            "body_4": {
              "type": "text",
              "value": "{{confirmed_bookings}}"
            },
            "body_5": {
              "type": "text",
              "value": "{{cancelled_bookings}}"
            },
            "body_6": {
              "type": "text",
              "value": "{{gross_revenue}}"
            },
            "body_7": {
              "type": "text",
              "value": "{{net_revenue}}"
            },
            "body_8": {
              "type": "text",
              "value": "{{coupon_usage_count}}"
            }
          }
        }
      ]
    }
  }
}
```

## ⚙️ ViaSocket MSG91 Action Configuration

### Action Setup
```json
{
  "action_type": "msg91_whatsapp_template",
  "action_name": "Send Daily Venue Report",
  "config": {
    "connection": "grid2play_msg91",
    "template_name": "daily_venue_report",
    "language_code": "en_US",
    "integrated_number": "919211848599",
    "batch_processing": true,
    "batch_size": 1,
    "delay_between_batches": 2000
  }
}
```

### Variable Mapping Configuration
```json
{
  "variable_mapping": {
    "recipient_phone": "{{current_item.admin_phone}}",
    "header_1": "{{current_item.venue_name}}",
    "body_1": "{{current_item.venue_name}}",
    "body_2": "{{current_item.report_date}}",
    "body_3": "{{current_item.total_bookings}}",
    "body_4": "{{current_item.confirmed_bookings}}",
    "body_5": "{{current_item.cancelled_bookings}}",
    "body_6": "{{current_item.gross_revenue}}",
    "body_7": "{{current_item.net_revenue}}",
    "body_8": "{{current_item.coupon_usage_count}}"
  }
}
```

## 📞 Phone Number Validation

### Phone Format Requirements
- **Format**: +91XXXXXXXXXX (13 characters total)
- **Pattern**: `^\\+91[6-9]\\d{9}$`
- **Examples**: 
  - ✅ +919876543210
  - ❌ 9876543210
  - ❌ +91-9876543210

### ViaSocket Phone Validation Script
```javascript
function validateAndFormatPhone(phone) {
  if (!phone) return null;
  
  // Clean the phone number
  let cleaned = phone.toString().replace(/[^\d+]/g, '');
  
  // Format to +91XXXXXXXXXX
  if (cleaned.startsWith('0')) {
    cleaned = '+91' + cleaned.substring(1);
  } else if (cleaned.startsWith('91') && cleaned.length === 12) {
    cleaned = '+' + cleaned;
  } else if (!cleaned.startsWith('+91') && cleaned.length === 10) {
    cleaned = '+91' + cleaned;
  }
  
  // Validate final format
  const phoneRegex = /^\+91[6-9]\d{9}$/;
  return phoneRegex.test(cleaned) ? cleaned : null;
}

// Apply to each venue record
output = input.processed_venues.map(venue => ({
  ...venue,
  admin_phone: validateAndFormatPhone(venue.admin_phone)
})).filter(venue => venue.admin_phone !== null);
```

## 🔄 Batch Processing Configuration

### Iterator Setup for Multiple Venues
```json
{
  "iterator_config": {
    "input_array": "processed_venues",
    "current_item_variable": "current_venue",
    "batch_size": 1,
    "delay_between_items": 2000,
    "max_concurrent": 1,
    "continue_on_error": true,
    "error_handling": {
      "max_failures": 5,
      "failure_action": "continue",
      "log_failures": true
    }
  }
}
```

### Rate Limiting Configuration
```json
{
  "rate_limiting": {
    "requests_per_minute": 30,
    "requests_per_hour": 1000,
    "delay_on_rate_limit": 60,
    "retry_on_rate_limit": true,
    "max_retries": 3
  }
}
```

## 🚨 Error Handling & Retry Logic

### MSG91 API Error Codes
```json
{
  "error_handling": {
    "200": {"action": "success", "log": true},
    "400": {"action": "skip", "log": true, "reason": "Bad request"},
    "401": {"action": "stop", "log": true, "reason": "Authentication failed"},
    "403": {"action": "skip", "log": true, "reason": "Template not approved"},
    "429": {"action": "retry", "delay": 60, "max_retries": 3},
    "500": {"action": "retry", "delay": 30, "max_retries": 2}
  }
}
```

### Retry Configuration
```json
{
  "retry_policy": {
    "max_retries": 2,
    "retry_delay": 5000,
    "exponential_backoff": true,
    "retry_on_errors": [429, 500, 502, 503, 504],
    "skip_on_errors": [400, 401, 403, 404]
  }
}
```

## 📊 Response Handling

### Success Response Format
```json
{
  "type": "success",
  "message": "Message sent successfully",
  "request_id": "req_123456789",
  "status_code": 200,
  "delivery_status": "sent"
}
```

### Error Response Format
```json
{
  "type": "error",
  "message": "Template not found",
  "error_code": 403,
  "request_id": "req_123456789",
  "details": "Template daily_venue_report not approved"
}
```

### ViaSocket Response Processing
```javascript
function processMsg91Response(response, venue) {
  const result = {
    venue_id: venue.venue_id,
    venue_name: venue.venue_name,
    admin_phone: venue.admin_phone,
    timestamp: new Date().toISOString(),
    success: false,
    error: null
  };
  
  if (response.status_code === 200) {
    result.success = true;
    result.message_id = response.request_id;
  } else {
    result.error = {
      code: response.error_code || response.status_code,
      message: response.message || 'Unknown error'
    };
  }
  
  return result;
}
```

## 🔍 Testing & Validation

### Test MSG91 Connection
```bash
curl --location --request POST \
'https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/' \
--header 'Content-Type: application/json' \
--header 'authkey: YOUR_AUTH_KEY' \
--data-raw '{
  "integrated_number": "919211848599",
  "content_type": "template",
  "payload": {
    "messaging_product": "whatsapp",
    "type": "template",
    "template": {
      "name": "daily_venue_report",
      "language": {
        "code": "en_US",
        "policy": "deterministic"
      },
      "namespace": "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
      "to_and_components": [
        {
          "to": ["+919876543210"],
          "components": {
            "header_1": {"type": "text", "value": "Test Venue"},
            "body_1": {"type": "text", "value": "Test Venue"},
            "body_2": {"type": "text", "value": "04/01/2025"},
            "body_3": {"type": "text", "value": "5"},
            "body_4": {"type": "text", "value": "4"},
            "body_5": {"type": "text", "value": "1"},
            "body_6": {"type": "text", "value": "₹2,500"},
            "body_7": {"type": "text", "value": "₹2,375"},
            "body_8": {"type": "text", "value": "2"}
          }
        }
      ]
    }
  }
}'
```

### ViaSocket Test Configuration
```json
{
  "test_mode": true,
  "test_recipients": ["+919876543210"],
  "test_data": {
    "venue_name": "Test Venue",
    "report_date": "04/01/2025",
    "total_bookings": "5",
    "confirmed_bookings": "4",
    "cancelled_bookings": "1",
    "gross_revenue": "₹2,500",
    "net_revenue": "₹2,375",
    "coupon_usage_count": "2"
  }
}
```

## 📈 Monitoring & Analytics

### Delivery Tracking
```json
{
  "tracking": {
    "track_delivery_status": true,
    "webhook_url": "https://your-domain.com/webhook/msg91-status",
    "track_read_receipts": true,
    "store_message_history": true,
    "retention_days": 30
  }
}
```

### Success Metrics
- Message delivery rate
- Template approval status
- API response times
- Error frequency by type
- Phone number validation success rate

## 🔐 Security Best Practices

### API Key Management
- ✅ Store auth key securely in ViaSocket
- ✅ Rotate keys regularly
- ✅ Monitor API usage
- ✅ Set up usage alerts
- ✅ Restrict IP access if possible

### Data Privacy
- ✅ Only send to verified phone numbers
- ✅ Log minimal personal data
- ✅ Comply with WhatsApp Business policies
- ✅ Implement opt-out mechanisms
- ✅ Regular privacy audits

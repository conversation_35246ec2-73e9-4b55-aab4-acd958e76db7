#!/usr/bin/env node

import { spawn } from 'child_process';

console.log('🔧 Testing MCP Server Connection...');

const mcpProcess = spawn('npx', [
  'mcp-remote',
  'https://mcp.viasocket.com/68670faf2d65d2a22316c270-16560/sse'
]);

let connectionEstablished = false;
let testStep = 0;

mcpProcess.stdout.on('data', (data) => {
  const output = data.toString();
  console.log('📡 MCP Output:', output);
  
  if (output.includes('Proxy established successfully') && !connectionEstablished) {
    connectionEstablished = true;
    console.log('✅ MCP Connection established!');
    console.log('🧪 Running tests...\n');
    
    runTests();
  }
});

mcpProcess.stderr.on('data', (data) => {
  const error = data.toString();
  if (!error.includes('Using automatically selected callback port') && 
      !error.includes('Connecting to remote server')) {
    console.error('❌ MCP Error:', error);
  }
});

function runTests() {
  const tests = [
    {
      name: 'List Tools',
      request: { jsonrpc: "2.0", id: 1, method: "tools/list", params: {} }
    },
    {
      name: 'List Resources',
      request: { jsonrpc: "2.0", id: 2, method: "resources/list", params: {} }
    },
    {
      name: 'Server Info',
      request: { jsonrpc: "2.0", id: 3, method: "initialize", params: { protocolVersion: "2024-11-05", clientInfo: { name: "test-client", version: "1.0.0" } } }
    }
  ];
  
  function runNextTest() {
    if (testStep < tests.length) {
      const test = tests[testStep];
      console.log(`\n🧪 Test ${testStep + 1}: ${test.name}`);
      console.log('📤 Sending:', JSON.stringify(test.request, null, 2));
      
      mcpProcess.stdin.write(JSON.stringify(test.request) + '\n');
      testStep++;
      
      setTimeout(runNextTest, 2000);
    } else {
      console.log('\n🏁 All tests completed!');
      setTimeout(() => {
        console.log('🔚 Shutting down...');
        mcpProcess.kill('SIGTERM');
      }, 2000);
    }
  }
  
  runNextTest();
}

mcpProcess.on('close', (code) => {
  console.log(`\n📊 MCP process exited with code ${code}`);
  if (connectionEstablished) {
    console.log('✅ Test completed successfully!');
  } else {
    console.log('❌ Connection failed');
  }
  process.exit(code);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  mcpProcess.kill('SIGTERM');
});

// Set timeout for the entire test
setTimeout(() => {
  console.log('\n⏰ Test timeout reached');
  mcpProcess.kill('SIGTERM');
}, 15000);
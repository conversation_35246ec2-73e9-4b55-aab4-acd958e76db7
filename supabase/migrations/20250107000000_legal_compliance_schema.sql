-- Grid२Play Legal Compliance Database Schema
-- Implements comprehensive consent tracking and legal document management

-- User Consent Tracking Table
CREATE TABLE IF NOT EXISTS user_consents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    consent_type VARCHAR(50) NOT NULL, -- 'privacy_policy', 'terms_of_service', 'cookie_policy', 'marketing', 'data_processing'
    consent_version VARCHAR(20) NOT NULL, -- Version of the document consented to
    consent_given BOOLEAN NOT NULL DEFAULT false,
    consent_timestamp TIMESTAMPTZ DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    consent_method VARCHAR(50), -- 'registration', 'explicit_popup', 'settings_update'
    withdrawal_timestamp TIMESTAMPTZ,
    withdrawal_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Legal Documents Management Table
CREATE TABLE IF NOT EXISTS legal_documents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    document_type VARCHAR(50) NOT NULL, -- 'privacy_policy', 'terms_of_service', 'cookie_policy'
    version VARCHAR(20) NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    effective_date TIMESTAMPTZ NOT NULL,
    created_by UUID REFERENCES auth.users(id),
    is_active BOOLEAN DEFAULT false,
    requires_consent BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(document_type, version)
);

-- Age Verification Table
CREATE TABLE IF NOT EXISTS age_verifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    date_of_birth DATE,
    age_at_verification INTEGER,
    verification_method VARCHAR(50), -- 'self_declared', 'document_verified'
    is_minor BOOLEAN GENERATED ALWAYS AS (age_at_verification < 18) STORED,
    parent_consent_required BOOLEAN GENERATED ALWAYS AS (age_at_verification < 18) STORED,
    parent_email VARCHAR(255),
    parent_consent_given BOOLEAN DEFAULT false,
    parent_consent_timestamp TIMESTAMPTZ,
    verification_timestamp TIMESTAMPTZ DEFAULT NOW(),
    ip_address INET,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Cookie Preferences Table
CREATE TABLE IF NOT EXISTS cookie_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id VARCHAR(255), -- For non-authenticated users
    necessary_cookies BOOLEAN DEFAULT true, -- Always true, cannot be disabled
    functional_cookies BOOLEAN DEFAULT false,
    analytics_cookies BOOLEAN DEFAULT false,
    marketing_cookies BOOLEAN DEFAULT false,
    preferences_set_at TIMESTAMPTZ DEFAULT NOW(),
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    UNIQUE(user_id),
    UNIQUE(session_id)
);

-- Data Processing Activities Log
CREATE TABLE IF NOT EXISTS data_processing_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    processing_activity VARCHAR(100) NOT NULL, -- 'booking_creation', 'profile_update', 'marketing_email'
    data_categories TEXT[], -- ['personal_data', 'financial_data', 'location_data']
    legal_basis VARCHAR(50) NOT NULL, -- 'consent', 'contract', 'legitimate_interest'
    purpose TEXT NOT NULL,
    retention_period VARCHAR(100),
    third_party_sharing BOOLEAN DEFAULT false,
    third_parties TEXT[], -- List of third parties if shared
    processing_timestamp TIMESTAMPTZ DEFAULT NOW(),
    user_consent_id UUID REFERENCES user_consents(id)
);

-- Legal Compliance Audit Trail
CREATE TABLE IF NOT EXISTS legal_compliance_audit (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action_type VARCHAR(50) NOT NULL, -- 'consent_given', 'consent_withdrawn', 'data_deleted', 'policy_updated'
    document_type VARCHAR(50),
    document_version VARCHAR(20),
    old_value JSONB,
    new_value JSONB,
    ip_address INET,
    user_agent TEXT,
    admin_user_id UUID REFERENCES auth.users(id),
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_consents_user_id ON user_consents(user_id);
CREATE INDEX IF NOT EXISTS idx_user_consents_type_version ON user_consents(consent_type, consent_version);
CREATE INDEX IF NOT EXISTS idx_legal_documents_type_active ON legal_documents(document_type, is_active);
CREATE INDEX IF NOT EXISTS idx_age_verifications_user_id ON age_verifications(user_id);
CREATE INDEX IF NOT EXISTS idx_cookie_preferences_user_id ON cookie_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_cookie_preferences_session_id ON cookie_preferences(session_id);
CREATE INDEX IF NOT EXISTS idx_data_processing_log_user_id ON data_processing_log(user_id);
CREATE INDEX IF NOT EXISTS idx_legal_compliance_audit_user_id ON legal_compliance_audit(user_id);

-- Row Level Security (RLS) Policies
ALTER TABLE user_consents ENABLE ROW LEVEL SECURITY;
ALTER TABLE age_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE cookie_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_processing_log ENABLE ROW LEVEL SECURITY;

-- Users can only access their own consent records
CREATE POLICY "Users can view own consents" ON user_consents
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own consents" ON user_consents
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own consents" ON user_consents
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can only access their own age verification
CREATE POLICY "Users can view own age verification" ON age_verifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own age verification" ON age_verifications
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can only access their own cookie preferences
CREATE POLICY "Users can view own cookie preferences" ON cookie_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own cookie preferences" ON cookie_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own cookie preferences" ON cookie_preferences
    FOR UPDATE USING (auth.uid() = user_id);

-- Admin policies for legal documents (admins can manage all legal documents)
CREATE POLICY "Admins can manage legal documents" ON legal_documents
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role IN ('admin', 'super_admin')
        )
    );

-- Public read access to active legal documents
CREATE POLICY "Public can view active legal documents" ON legal_documents
    FOR SELECT USING (is_active = true);

-- Functions for consent management
CREATE OR REPLACE FUNCTION record_user_consent(
    p_user_id UUID,
    p_consent_type VARCHAR(50),
    p_consent_version VARCHAR(20),
    p_consent_given BOOLEAN,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_consent_method VARCHAR(50) DEFAULT 'explicit_popup'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    consent_id UUID;
BEGIN
    INSERT INTO user_consents (
        user_id, consent_type, consent_version, consent_given,
        ip_address, user_agent, consent_method
    ) VALUES (
        p_user_id, p_consent_type, p_consent_version, p_consent_given,
        p_ip_address, p_user_agent, p_consent_method
    ) RETURNING id INTO consent_id;
    
    -- Log the consent action
    INSERT INTO legal_compliance_audit (
        user_id, action_type, document_type, document_version,
        new_value, ip_address, user_agent
    ) VALUES (
        p_user_id, 'consent_given', p_consent_type, p_consent_version,
        jsonb_build_object('consent_given', p_consent_given, 'method', p_consent_method),
        p_ip_address, p_user_agent
    );
    
    RETURN consent_id;
END;
$$;

-- Function to check if user has given consent for specific document
CREATE OR REPLACE FUNCTION check_user_consent(
    p_user_id UUID,
    p_consent_type VARCHAR(50),
    p_required_version VARCHAR(20) DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    has_consent BOOLEAN := false;
BEGIN
    SELECT consent_given INTO has_consent
    FROM user_consents
    WHERE user_id = p_user_id
    AND consent_type = p_consent_type
    AND (p_required_version IS NULL OR consent_version = p_required_version)
    AND withdrawal_timestamp IS NULL
    ORDER BY consent_timestamp DESC
    LIMIT 1;
    
    RETURN COALESCE(has_consent, false);
END;
$$;

-- Function to withdraw consent
CREATE OR REPLACE FUNCTION withdraw_user_consent(
    p_user_id UUID,
    p_consent_type VARCHAR(50),
    p_withdrawal_reason TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE user_consents
    SET withdrawal_timestamp = NOW(),
        withdrawal_reason = p_withdrawal_reason,
        updated_at = NOW()
    WHERE user_id = p_user_id
    AND consent_type = p_consent_type
    AND withdrawal_timestamp IS NULL;
    
    -- Log the withdrawal
    INSERT INTO legal_compliance_audit (
        user_id, action_type, document_type,
        new_value, ip_address
    ) VALUES (
        p_user_id, 'consent_withdrawn', p_consent_type,
        jsonb_build_object('reason', p_withdrawal_reason),
        p_ip_address
    );
    
    RETURN true;
END;
$$;

-- Insert initial legal document versions
INSERT INTO legal_documents (document_type, version, title, content, effective_date, is_active, requires_consent) VALUES
('privacy_policy', '1.0', 'Grid२Play Privacy Policy', 'Initial privacy policy content', NOW(), true, true),
('terms_of_service', '1.0', 'Grid२Play Terms of Service', 'Initial terms of service content', NOW(), true, true),
('cookie_policy', '1.0', 'Grid२Play Cookie Policy', 'Initial cookie policy content', NOW(), true, true)
ON CONFLICT (document_type, version) DO NOTHING;

-- Grant necessary permissions
GRANT SELECT ON legal_documents TO anon, authenticated;
GRANT ALL ON user_consents TO authenticated;
GRANT ALL ON age_verifications TO authenticated;
GRANT ALL ON cookie_preferences TO authenticated;
GRANT SELECT ON data_processing_log TO authenticated;
GRANT SELECT ON legal_compliance_audit TO authenticated;

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface BookingData {
  booking_reference: string;
  guest_name: string;
  guest_phone: string;
  venue_name: string;
  venue_location: string;
  court_name: string;
  sport_name: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  total_price: string;
  payment_reference: string;
}

/**
 * Generate .ics calendar file content for a Grid2Play booking
 */
function generateICSFile(booking: BookingData): string {
  const {
    booking_reference,
    guest_name,
    guest_phone,
    venue_name,
    venue_location,
    court_name,
    sport_name,
    booking_date,
    start_time,
    end_time,
    total_price,
    payment_reference
  } = booking;

  // Convert date and time to proper format
  const startDateTime = formatDateTimeForICS(booking_date, start_time);
  const endDateTime = formatDateTimeForICS(booking_date, end_time);
  const createdDateTime = new Date().toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

  // Generate unique UID for the event
  const uid = `${booking_reference}@grid2play.com`;

  // Create event description with all booking details
  const description = [
    `🏈 ${sport_name} Booking at ${venue_name}`,
    `🎫 Booking Reference: ${booking_reference}`,
    `👤 Player: ${guest_name}`,
    `📞 Contact: ${guest_phone}`,
    `🏟️ Court: ${court_name}`,
    `💰 Amount Paid: ₹${total_price}`,
    `💳 Payment ID: ${payment_reference}`,
    ``,
    `📍 Venue Address:`,
    `${venue_location}`,
    ``,
    `⚡ Powered by Grid२Play - India's Premier Sports Booking Platform`
  ].join('\\n');

  // Generate .ics file content
  const icsContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Grid2Play//Sports Booking Calendar//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:PUBLISH',
    'BEGIN:VEVENT',
    `UID:${uid}`,
    `DTSTAMP:${createdDateTime}`,
    `DTSTART;TZID=Asia/Kolkata:${startDateTime}`,
    `DTEND;TZID=Asia/Kolkata:${endDateTime}`,
    `SUMMARY:${sport_name} Booking - ${venue_name}`,
    `DESCRIPTION:${description}`,
    `LOCATION:${venue_name}, ${venue_location}`,
    `STATUS:CONFIRMED`,
    `TRANSP:OPAQUE`,
    `CATEGORIES:SPORTS,BOOKING,GRID2PLAY`,
    'BEGIN:VALARM',
    'TRIGGER:-PT1H',
    'ACTION:DISPLAY',
    'DESCRIPTION:Your sports booking starts in 1 hour!',
    'END:VALARM',
    'BEGIN:VALARM',
    'TRIGGER:-PT15M',
    'ACTION:DISPLAY',
    'DESCRIPTION:Your sports booking starts in 15 minutes!',
    'END:VALARM',
    'END:VEVENT',
    'END:VCALENDAR'
  ].join('\r\n');

  return icsContent;
}

/**
 * Format date and time for ICS format (YYYYMMDDTHHMMSS)
 */
function formatDateTimeForICS(date: string, time: string): string {
  const dateStr = date.replace(/-/g, '');
  const timeStr = time.replace(/:/g, '').substring(0, 6);
  return `${dateStr}T${timeStr}`;
}

/**
 * Generate calendar URLs for different platforms
 */
function generateCalendarUrls(booking: BookingData) {
  const {
    booking_reference,
    guest_name,
    venue_name,
    venue_location,
    court_name,
    sport_name,
    booking_date,
    start_time,
    end_time,
    total_price
  } = booking;

  // Format for URL encoding
  const title = encodeURIComponent(`${sport_name} Booking - ${venue_name}`);
  const location = encodeURIComponent(`${venue_name}, ${venue_location}`);
  const details = encodeURIComponent([
    `🏈 ${sport_name} at ${court_name}`,
    `🎫 Booking: ${booking_reference}`,
    `👤 Player: ${guest_name}`,
    `💰 Amount: ₹${total_price}`,
    `⚡ Powered by Grid२Play`
  ].join('\n'));

  // Convert to ISO format for URLs
  const startISO = new Date(`${booking_date}T${start_time}+05:30`).toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
  const endISO = new Date(`${booking_date}T${end_time}+05:30`).toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

  return {
    google: `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startISO}/${endISO}&location=${location}&details=${details}`,
    outlook: `https://outlook.live.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startISO}&enddt=${endISO}&location=${location}&body=${details}`,
    yahoo: `https://calendar.yahoo.com/?v=60&view=d&type=20&title=${title}&st=${startISO}&et=${endISO}&in_loc=${location}&desc=${details}`,
    office365: `https://outlook.office.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startISO}&enddt=${endISO}&location=${location}&body=${details}`
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { booking_reference, action = 'urls' } = await req.json()

    if (!booking_reference) {
      throw new Error('Booking reference is required')
    }

    // Fetch booking details from database
    const { data: bookingData, error } = await supabase
      .from('bookings')
      .select(`
        booking_reference,
        guest_name,
        guest_phone,
        booking_date,
        start_time,
        end_time,
        total_price,
        payment_reference,
        courts (
          name,
          venues (
            name,
            location
          ),
          sports (
            name
          )
        )
      `)
      .eq('booking_reference', booking_reference)
      .single()

    if (error || !bookingData) {
      throw new Error(`Booking not found: ${booking_reference}`)
    }

    // Format booking data
    const booking: BookingData = {
      booking_reference: bookingData.booking_reference,
      guest_name: bookingData.guest_name,
      guest_phone: bookingData.guest_phone,
      venue_name: bookingData.courts.venues.name,
      venue_location: bookingData.courts.venues.location,
      court_name: bookingData.courts.name,
      sport_name: bookingData.courts.sports.name,
      booking_date: bookingData.booking_date,
      start_time: bookingData.start_time,
      end_time: bookingData.end_time,
      total_price: bookingData.total_price,
      payment_reference: bookingData.payment_reference || ''
    }

    if (action === 'ics') {
      // Generate and return .ics file
      const icsContent = generateICSFile(booking)
      
      return new Response(icsContent, {
        headers: {
          ...corsHeaders,
          'Content-Type': 'text/calendar; charset=utf-8',
          'Content-Disposition': `attachment; filename="${booking_reference}.ics"`
        }
      })
    } else {
      // Return calendar URLs
      const calendarUrls = generateCalendarUrls(booking)
      
      return new Response(
        JSON.stringify({
          success: true,
          booking_reference,
          calendar_urls: calendarUrls,
          booking_details: booking
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('Calendar generation error:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

-- Daily Venue Reports Cron Job for Grid2Play
-- Automatically sends daily venue performance reports to venue administrators
-- Scheduled for 5:20 AM IST (00:30 UTC) daily

-- Function to trigger daily venue reports via Edge Function
CREATE OR REPLACE FUNCTION public.trigger_daily_venue_reports()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_response JSON;
  v_venue_count INTEGER := 0;
  v_success_count INTEGER := 0;
  v_error_count INTEGER := 0;
  v_execution_start TIMESTAMP := NOW();
  v_execution_end TIMESTAMP;
  v_supabase_url TEXT;
  v_service_role_key TEXT;
  v_edge_function_url TEXT;
BEGIN
  -- Log start of execution
  RAISE NOTICE 'Starting daily venue reports execution at %', v_execution_start;
  
  -- Get configuration from environment or use defaults
  -- Note: In production, these should be set as environment variables
  v_supabase_url := COALESCE(current_setting('app.supabase_url', true), 'https://lrtirloetmulgmdxnusl.supabase.co');
  v_service_role_key := current_setting('app.service_role_key', true);
  
  -- Construct Edge Function URL
  v_edge_function_url := v_supabase_url || '/functions/v1/send-daily-venue-reports';
  
  -- Check if we have venues to process
  SELECT COUNT(*) INTO v_venue_count
  FROM get_venue_daily_summary(CURRENT_DATE);
  
  IF v_venue_count = 0 THEN
    RAISE NOTICE 'No venues with activity found for today. Skipping daily reports.';
    
    -- Log the execution
    INSERT INTO automation_logs (
      automation_type,
      execution_date,
      status,
      total_venues,
      successful_sends,
      failed_sends,
      execution_time_seconds,
      notes
    ) VALUES (
      'daily_venue_reports',
      CURRENT_DATE,
      'completed_no_data',
      0,
      0,
      0,
      EXTRACT(EPOCH FROM (NOW() - v_execution_start)),
      'No venues with activity found'
    );
    
    RETURN json_build_object(
      'success', true,
      'message', 'No venues with activity found for today',
      'venues_processed', 0,
      'execution_time', EXTRACT(EPOCH FROM (NOW() - v_execution_start))
    );
  END IF;
  
  RAISE NOTICE 'Found % venues to process for daily reports', v_venue_count;
  
  -- Call the Edge Function using pg_net extension (Supabase's HTTP client)
  BEGIN
    -- Use pg_net to call the Edge Function
    RAISE NOTICE 'Triggering Edge Function: %', v_edge_function_url;

    -- Make HTTP POST request to Edge Function
    PERFORM net.http_post(
      url := v_edge_function_url,
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || v_service_role_key
      ),
      body := jsonb_build_object(
        'date', CURRENT_DATE::TEXT,
        'triggered_by', 'cron_job'
      )
    );

    -- Since pg_net is async, we'll assume success and let the Edge Function handle the actual work
    v_success_count := v_venue_count;
    v_error_count := 0;

    v_response := json_build_object(
      'success', true,
      'message', 'Daily venue reports Edge Function triggered successfully',
      'total_venues', v_venue_count,
      'method', 'pg_net_async',
      'edge_function_url', v_edge_function_url
    );

  EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error calling Edge Function via pg_net: %', SQLERRM;

    -- Fallback: Try direct processing if Edge Function call fails
    BEGIN
      RAISE NOTICE 'Attempting fallback: direct processing of venue reports';

      -- Process venues directly in the database function
      -- This is a simplified fallback - the Edge Function is preferred
      v_success_count := 0;
      v_error_count := 0;

      -- Log that we attempted direct processing
      INSERT INTO automation_logs (
        automation_type,
        execution_date,
        status,
        total_venues,
        successful_sends,
        failed_sends,
        execution_time_seconds,
        notes
      ) VALUES (
        'daily_venue_reports_fallback',
        CURRENT_DATE,
        'edge_function_failed',
        v_venue_count,
        0,
        0,
        EXTRACT(EPOCH FROM (NOW() - v_execution_start)),
        'Edge Function call failed, fallback attempted: ' || SQLERRM
      );

      v_response := json_build_object(
        'success', false,
        'error', 'Edge Function call failed',
        'fallback_attempted', true,
        'original_error', SQLERRM,
        'total_venues', v_venue_count
      );

    EXCEPTION WHEN OTHERS THEN
      RAISE WARNING 'Fallback processing also failed: %', SQLERRM;
      v_error_count := v_venue_count;
      v_success_count := 0;

      v_response := json_build_object(
        'success', false,
        'error', 'Both Edge Function and fallback failed',
        'edge_function_error', SQLERRM,
        'total_venues', v_venue_count
      );
    END;
  END;
  
  v_execution_end := NOW();
  
  -- Log the execution results
  INSERT INTO automation_logs (
    automation_type,
    execution_date,
    status,
    total_venues,
    successful_sends,
    failed_sends,
    execution_time_seconds,
    notes,
    response_data
  ) VALUES (
    'daily_venue_reports',
    CURRENT_DATE,
    CASE WHEN v_success_count > 0 THEN 'completed' ELSE 'failed' END,
    v_venue_count,
    v_success_count,
    v_error_count,
    EXTRACT(EPOCH FROM (v_execution_end - v_execution_start)),
    CASE 
      WHEN v_success_count = v_venue_count THEN 'All reports sent successfully'
      WHEN v_success_count > 0 THEN 'Partial success'
      ELSE 'All reports failed'
    END,
    v_response
  );
  
  RAISE NOTICE 'Daily venue reports execution completed. Success: %, Errors: %', v_success_count, v_error_count;
  
  RETURN v_response;
END;
$$;

-- Create automation logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS automation_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  automation_type TEXT NOT NULL,
  execution_date DATE NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('completed', 'failed', 'completed_no_data', 'partial')),
  total_venues INTEGER DEFAULT 0,
  successful_sends INTEGER DEFAULT 0,
  failed_sends INTEGER DEFAULT 0,
  execution_time_seconds NUMERIC,
  notes TEXT,
  response_data JSON,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_automation_logs_type_date ON automation_logs(automation_type, execution_date);

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.trigger_daily_venue_reports() TO postgres;
GRANT INSERT ON automation_logs TO postgres;

-- Schedule the cron job for 5:20 AM IST (00:30 UTC) daily
-- Note: Supabase uses UTC time, so 5:20 AM IST = 11:50 PM UTC (previous day)
SELECT cron.schedule(
  'daily-venue-reports',           -- job name
  '50 23 * * *',                   -- cron schedule: 11:50 PM UTC = 5:20 AM IST
  'SELECT trigger_daily_venue_reports();'  -- command to execute
);

-- Alternative: If you prefer 6:00 AM IST (00:30 UTC), use:
-- SELECT cron.schedule(
--   'daily-venue-reports',
--   '30 0 * * *',
--   'SELECT trigger_daily_venue_reports();'
-- );

-- Verify the cron job was created
SELECT jobid, schedule, command, nodename, nodeport, database, username, active 
FROM cron.job 
WHERE jobname = 'daily-venue-reports';

-- Function to manually trigger daily reports (for testing)
CREATE OR REPLACE FUNCTION public.manual_trigger_daily_reports(p_date DATE DEFAULT CURRENT_DATE)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RAISE NOTICE 'Manually triggering daily venue reports for date: %', p_date;
  
  -- Call the main trigger function
  RETURN trigger_daily_venue_reports();
END;
$$;

-- Grant permission for manual trigger
GRANT EXECUTE ON FUNCTION public.manual_trigger_daily_reports(DATE) TO authenticated;

-- Function to check automation logs
CREATE OR REPLACE FUNCTION public.get_automation_logs(
  p_automation_type TEXT DEFAULT 'daily_venue_reports',
  p_limit INTEGER DEFAULT 10
)
RETURNS TABLE(
  execution_date DATE,
  status TEXT,
  total_venues INTEGER,
  successful_sends INTEGER,
  failed_sends INTEGER,
  execution_time_seconds NUMERIC,
  notes TEXT,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    al.execution_date,
    al.status,
    al.total_venues,
    al.successful_sends,
    al.failed_sends,
    al.execution_time_seconds,
    al.notes,
    al.created_at
  FROM automation_logs al
  WHERE al.automation_type = p_automation_type
  ORDER BY al.created_at DESC
  LIMIT p_limit;
END;
$$;

-- Grant permission to view logs
GRANT EXECUTE ON FUNCTION public.get_automation_logs(TEXT, INTEGER) TO authenticated;

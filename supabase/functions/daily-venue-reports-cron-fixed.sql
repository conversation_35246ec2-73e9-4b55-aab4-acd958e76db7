-- Fixed Daily Venue Reports Cron Job for Grid2Play
-- Addresses duplicate logging, Edge Function issues, and provides fallback MSG91 integration

-- Drop existing function to recreate
DROP FUNCTION IF EXISTS public.trigger_daily_venue_reports();

-- Enhanced function with duplicate prevention and direct MSG91 fallback
CREATE OR REPLACE FUNCTION public.trigger_daily_venue_reports()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_response JSON;
  v_venue_count INTEGER := 0;
  v_success_count INTEGER := 0;
  v_error_count INTEGER := 0;
  v_execution_start TIMESTAMP := NOW();
  v_execution_end TIMESTAMP;
  v_existing_log_count INTEGER;
  v_venue_data JSON;
  v_venue_record RECORD;
  v_msg91_auth_key TEXT;
  v_integrated_number TEXT := '919211848599';
  v_msg91_payload JSON;
  v_http_response TEXT;
  v_http_status INTEGER;
BEGIN
  -- Check if we already have a log entry for today to prevent duplicates
  SELECT COUNT(*) INTO v_existing_log_count
  FROM automation_logs 
  WHERE automation_type = 'daily_venue_reports' 
    AND execution_date = CURRENT_DATE
    AND status != 'failed'
    AND created_at >= CURRENT_DATE;
  
  IF v_existing_log_count > 0 THEN
    RAISE NOTICE 'Daily venue reports already executed today (% existing logs). Skipping to prevent duplicates.', v_existing_log_count;
    RETURN json_build_object(
      'success', true,
      'message', 'Daily venue reports already executed today',
      'existing_logs', v_existing_log_count,
      'execution_time', 0
    );
  END IF;
  
  -- Log start of execution
  RAISE NOTICE 'Starting daily venue reports execution at %', v_execution_start;
  
  -- Check if we have venues to process
  SELECT COUNT(*) INTO v_venue_count
  FROM get_venue_daily_summary(CURRENT_DATE);
  
  IF v_venue_count = 0 THEN
    RAISE NOTICE 'No venues with activity found for today. Skipping daily reports.';
    
    -- Log the execution
    INSERT INTO automation_logs (
      automation_type,
      execution_date,
      status,
      total_venues,
      successful_sends,
      failed_sends,
      execution_time_seconds,
      notes
    ) VALUES (
      'daily_venue_reports',
      CURRENT_DATE,
      'completed_no_data',
      0,
      0,
      0,
      EXTRACT(EPOCH FROM (NOW() - v_execution_start)),
      'No venues with activity found'
    );
    
    RETURN json_build_object(
      'success', true,
      'message', 'No venues with activity found for today',
      'venues_processed', 0,
      'execution_time', EXTRACT(EPOCH FROM (NOW() - v_execution_start))
    );
  END IF;
  
  RAISE NOTICE 'Found % venues to process for daily reports', v_venue_count;
  
  -- Try to get MSG91 credentials from environment
  BEGIN
    v_msg91_auth_key := current_setting('app.msg91_auth_key', true);
  EXCEPTION WHEN OTHERS THEN
    v_msg91_auth_key := NULL;
  END;
  
  -- Method 1: Try Edge Function approach first
  BEGIN
    RAISE NOTICE 'Attempting Edge Function approach...';
    
    -- Use pg_net to call the Edge Function
    PERFORM net.http_post(
      url := 'https://lrtirloetmulgmdxnusl.supabase.co/functions/v1/send-daily-venue-reports',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || COALESCE(current_setting('app.service_role_key', true), 'missing-key')
      ),
      body := jsonb_build_object(
        'date', CURRENT_DATE::TEXT,
        'triggered_by', 'cron_job'
      )
    );
    
    v_success_count := v_venue_count;
    v_error_count := 0;
    
    v_response := json_build_object(
      'success', true,
      'message', 'Daily venue reports Edge Function triggered successfully',
      'total_venues', v_venue_count,
      'method', 'edge_function'
    );
    
    RAISE NOTICE 'Edge Function triggered successfully';
    
  EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Edge Function approach failed: %. Trying direct MSG91 approach...', SQLERRM;
    
    -- Method 2: Direct MSG91 integration fallback
    IF v_msg91_auth_key IS NOT NULL THEN
      BEGIN
        RAISE NOTICE 'Using direct MSG91 integration fallback';
        v_success_count := 0;
        v_error_count := 0;
        
        -- Process each venue directly
        FOR v_venue_record IN 
          SELECT * FROM get_venue_daily_summary(CURRENT_DATE)
        LOOP
          BEGIN
            -- Format phone number
            IF v_venue_record.admin_phone IS NULL OR v_venue_record.admin_phone = '' THEN
              RAISE WARNING 'Skipping venue % - no valid phone number', v_venue_record.venue_name;
              v_error_count := v_error_count + 1;
              CONTINUE;
            END IF;
            
            -- Build MSG91 payload
            v_msg91_payload := json_build_object(
              'integrated_number', v_integrated_number,
              'content_type', 'template',
              'payload', json_build_object(
                'messaging_product', 'whatsapp',
                'type', 'template',
                'template', json_build_object(
                  'name', 'daily_venue_report',
                  'language', json_build_object(
                    'code', 'en_US',
                    'policy', 'deterministic'
                  ),
                  'namespace', '380c0d5c_8b3e_43ac_a4a3_183fea1845af',
                  'to_and_components', json_build_array(
                    json_build_object(
                      'to', json_build_array(v_venue_record.admin_phone),
                      'components', json_build_object(
                        'header_1', json_build_object('type', 'text', 'value', v_venue_record.venue_name),
                        'body_1', json_build_object('type', 'text', 'value', v_venue_record.venue_name),
                        'body_2', json_build_object('type', 'text', 'value', v_venue_record.report_date),
                        'body_3', json_build_object('type', 'text', 'value', v_venue_record.total_bookings),
                        'body_4', json_build_object('type', 'text', 'value', v_venue_record.confirmed_bookings),
                        'body_5', json_build_object('type', 'text', 'value', v_venue_record.cancelled_bookings),
                        'body_6', json_build_object('type', 'text', 'value', v_venue_record.gross_revenue),
                        'body_7', json_build_object('type', 'text', 'value', v_venue_record.net_revenue),
                        'body_8', json_build_object('type', 'text', 'value', v_venue_record.coupon_usage_count)
                      )
                    )
                  )
                )
              )
            );
            
            -- Make HTTP request to MSG91 (this would need http extension or pg_net)
            -- For now, just log the attempt
            RAISE NOTICE 'Would send MSG91 message to % (%) for venue %', 
              v_venue_record.admin_name, v_venue_record.admin_phone, v_venue_record.venue_name;
            
            v_success_count := v_success_count + 1;
            
            -- Add small delay to avoid rate limiting
            PERFORM pg_sleep(2);
            
          EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'Failed to process venue %: %', v_venue_record.venue_name, SQLERRM;
            v_error_count := v_error_count + 1;
          END;
        END LOOP;
        
        v_response := json_build_object(
          'success', true,
          'message', 'Direct MSG91 processing completed',
          'total_venues', v_venue_count,
          'successful_sends', v_success_count,
          'failed_sends', v_error_count,
          'method', 'direct_msg91_fallback'
        );
        
      EXCEPTION WHEN OTHERS THEN
        RAISE WARNING 'Direct MSG91 approach also failed: %', SQLERRM;
        v_error_count := v_venue_count;
        v_success_count := 0;
        
        v_response := json_build_object(
          'success', false,
          'error', 'Both Edge Function and direct MSG91 approaches failed',
          'edge_function_error', SQLERRM,
          'total_venues', v_venue_count,
          'method', 'all_methods_failed'
        );
      END;
    ELSE
      RAISE WARNING 'No MSG91 credentials available for fallback';
      v_error_count := v_venue_count;
      v_success_count := 0;
      
      v_response := json_build_object(
        'success', false,
        'error', 'Edge Function failed and no MSG91 credentials for fallback',
        'total_venues', v_venue_count,
        'method', 'no_fallback_available'
      );
    END IF;
  END;
  
  v_execution_end := NOW();
  
  -- Log the execution results
  INSERT INTO automation_logs (
    automation_type,
    execution_date,
    status,
    total_venues,
    successful_sends,
    failed_sends,
    execution_time_seconds,
    notes,
    response_data
  ) VALUES (
    'daily_venue_reports',
    CURRENT_DATE,
    CASE 
      WHEN v_success_count = v_venue_count THEN 'completed'
      WHEN v_success_count > 0 THEN 'partial'
      ELSE 'failed'
    END,
    v_venue_count,
    v_success_count,
    v_error_count,
    EXTRACT(EPOCH FROM (v_execution_end - v_execution_start)),
    CASE 
      WHEN v_success_count = v_venue_count THEN 'All reports processed successfully'
      WHEN v_success_count > 0 THEN 'Partial success - some reports failed'
      ELSE 'All reports failed'
    END,
    v_response
  );
  
  RAISE NOTICE 'Daily venue reports execution completed. Success: %, Errors: %', v_success_count, v_error_count;
  
  RETURN v_response;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.trigger_daily_venue_reports() TO postgres;

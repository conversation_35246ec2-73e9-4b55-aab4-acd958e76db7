-- Fix Daily Reports Cron Job Authentication
-- This fixes the 401 error by using the correct service role key

CREATE OR REPLACE FUNCTION public.trigger_daily_venue_reports()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_response JSON;
  v_venue_count INTEGER := 0;
  v_success_count INTEGER := 0;
  v_error_count INTEGER := 0;
  v_execution_start TIMESTAMP := NOW();
  v_execution_end TIMESTAMP;
  v_existing_log_count INTEGER;
BEGIN
  -- Check if we already have a log entry for today to prevent duplicates
  SELECT COUNT(*) INTO v_existing_log_count
  FROM automation_logs 
  WHERE automation_type = 'daily_venue_reports' 
    AND execution_date = CURRENT_DATE
    AND status != 'failed'
    AND created_at >= CURRENT_DATE;
  
  IF v_existing_log_count > 0 THEN
    RAISE NOTICE 'Daily venue reports already executed today (% existing logs). Skipping to prevent duplicates.', v_existing_log_count;
    RETURN json_build_object(
      'success', true,
      'message', 'Daily venue reports already executed today',
      'existing_logs', v_existing_log_count,
      'execution_time', 0
    );
  END IF;
  
  -- Log start of execution
  RAISE NOTICE 'Starting daily venue reports execution at %', v_execution_start;
  
  -- Check if we have venues to process
  SELECT COUNT(*) INTO v_venue_count
  FROM get_venue_daily_summary(CURRENT_DATE);
  
  IF v_venue_count = 0 THEN
    RAISE NOTICE 'No venues with activity found for today. Skipping daily reports.';
    
    -- Log the execution
    INSERT INTO automation_logs (
      automation_type,
      execution_date,
      status,
      total_venues,
      successful_sends,
      failed_sends,
      execution_time_seconds,
      notes
    ) VALUES (
      'daily_venue_reports',
      CURRENT_DATE,
      'completed_no_data',
      0,
      0,
      0,
      EXTRACT(EPOCH FROM (NOW() - v_execution_start)),
      'No venues with activity found'
    );
    
    RETURN json_build_object(
      'success', true,
      'message', 'No venues with activity found for today',
      'venues_processed', 0,
      'execution_time', EXTRACT(EPOCH FROM (NOW() - v_execution_start))
    );
  END IF;
  
  RAISE NOTICE 'Found % venues to process for daily reports', v_venue_count;
  
  -- Try Edge Function approach with correct service role key
  BEGIN
    RAISE NOTICE 'Attempting Edge Function approach with proper authentication...';
    
    -- Use pg_net to call the Edge Function with correct service role key from .env.local
    PERFORM net.http_post(
      url := 'https://lrtirloetmulgmdxnusl.supabase.co/functions/v1/send-daily-venue-reports',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxydGlybG9ldG11bGdtZHhudXNsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTg3MDE4NiwiZXhwIjoyMDYxNDQ2MTg2fQ.ynSQSjPl4CgKpwA2O-McFJwsv03NudXtP7hQE4-Wwfk'
      ),
      body := jsonb_build_object(
        'date', CURRENT_DATE::TEXT,
        'triggered_by', 'cron_job'
      )
    );
    
    v_success_count := v_venue_count;
    v_error_count := 0;
    
    v_response := json_build_object(
      'success', true,
      'message', 'Daily venue reports Edge Function triggered successfully',
      'total_venues', v_venue_count,
      'method', 'edge_function'
    );
    
    RAISE NOTICE 'Edge Function triggered successfully';
    
  EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Edge Function approach failed: %. Logging as partial failure.', SQLERRM;
    
    v_error_count := v_venue_count;
    v_success_count := 0;
    
    v_response := json_build_object(
      'success', false,
      'error', 'Edge Function call failed',
      'details', SQLERRM,
      'total_venues', v_venue_count,
      'method', 'edge_function_failed'
    );
  END;
  
  v_execution_end := NOW();
  
  -- Log the execution results
  INSERT INTO automation_logs (
    automation_type,
    execution_date,
    status,
    total_venues,
    successful_sends,
    failed_sends,
    execution_time_seconds,
    notes,
    response_data
  ) VALUES (
    'daily_venue_reports',
    CURRENT_DATE,
    CASE 
      WHEN v_success_count = v_venue_count THEN 'completed'
      WHEN v_success_count > 0 THEN 'partial'
      ELSE 'failed'
    END,
    v_venue_count,
    v_success_count,
    v_error_count,
    EXTRACT(EPOCH FROM (v_execution_end - v_execution_start)),
    CASE 
      WHEN v_success_count = v_venue_count THEN 'Edge Function triggered successfully'
      ELSE 'Edge Function call failed - check authentication'
    END,
    v_response
  );
  
  RAISE NOTICE 'Daily venue reports execution completed. Success: %, Errors: %', v_success_count, v_error_count;
  
  RETURN v_response;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.trigger_daily_venue_reports() TO postgres;

-- Test the function
SELECT public.trigger_daily_venue_reports() as test_result;

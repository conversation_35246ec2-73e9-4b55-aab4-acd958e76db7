
-- Function to get admin venues (basic venue info for admin users)
create or replace function public.get_admin_venues()
returns table(
  venue_id uuid,
  venue_name text,
  allow_cash_payments boolean
)
language plpgsql security definer
as $$
begin
  return query
  select
    v.id as venue_id,
    v.name as venue_name,
    v.allow_cash_payments
  from
    venues v
  left join
    venue_admins va on v.id = va.venue_id
  where
    v.is_active = true
    and (
      va.admin_id = auth.uid()
      or exists (select 1 from user_roles ur where ur.user_id = auth.uid() and ur.role = 'super_admin')
    )
  order by
    v.name;
end;
$$;

-- Function to get venues with stats (bookings and courts count)
create or replace function public.get_admin_venues_with_stats()
returns table(
  id uuid,
  name text,
  location text,
  image_url text,
  bookings_count bigint,
  courts_count bigint
)
language plpgsql security definer
as $$
begin
  return query
  select 
    v.id,
    v.name,
    v.location,
    v.image_url,
    count(distinct b.id) as bookings_count,
    count(distinct c.id) as courts_count
  from 
    venues v
  left join 
    venue_admins va on v.id = va.venue_id
  left join 
    courts c on v.id = c.venue_id and c.is_active = true
  left join 
    bookings b on c.id = b.court_id
  where 
    va.admin_id = auth.uid()
    or exists (select 1 from user_roles ur where ur.user_id = auth.uid() and ur.role = 'super_admin')
  group by 
    v.id, v.name, v.location, v.image_url
  order by 
    v.name;
end;
$$;

-- Function to get admin dashboard stats
create or replace function public.get_admin_dashboard_stats()
returns table(
  total_venues bigint,
  total_bookings bigint,
  recent_bookings bigint,
  total_courts bigint
)
language plpgsql security definer
as $$
begin
  return query
  select
    (select count(distinct v.id)
     from venues v
     left join venue_admins va on v.id = va.venue_id
     where va.admin_id = auth.uid()
       or exists (select 1 from user_roles ur where ur.user_id = auth.uid() and ur.role = 'super_admin')) as total_venues,

    (select count(b.id)
     from bookings b
     join courts c on b.court_id = c.id
     join venues v on c.venue_id = v.id
     left join venue_admins va on v.id = va.venue_id
     where va.admin_id = auth.uid()
       or exists (select 1 from user_roles ur where ur.user_id = auth.uid() and ur.role = 'super_admin')) as total_bookings,

    (select count(b.id)
     from bookings b
     join courts c on b.court_id = c.id
     join venues v on c.venue_id = v.id
     left join venue_admins va on v.id = va.venue_id
     where (va.admin_id = auth.uid()
       or exists (select 1 from user_roles ur where ur.user_id = auth.uid() and ur.role = 'super_admin'))
     and b.booking_date >= current_date - interval '7 days') as recent_bookings,

    (select count(c.id)
     from courts c
     join venues v on c.venue_id = v.id
     left join venue_admins va on v.id = va.venue_id
     where (va.admin_id = auth.uid()
       or exists (select 1 from user_roles ur where ur.user_id = auth.uid() and ur.role = 'super_admin'))
     and c.is_active = true) as total_courts;
end;
$$;

-- PHASE 1: OPTION B NET SETTLEMENT CALCULATION FUNCTION
-- Updated function to show venue admins the exact amount they receive in bank accounts
-- Platform fees calculated on original amounts, venues bear full coupon costs
create or replace function public.get_admin_dashboard_stats_optimized(
  admin_user_id UUID,
  target_date DATE DEFAULT CURRENT_DATE
)
returns JSON
language plpgsql security definer
as $$
declare
  user_role TEXT;
  venue_ids UUID[];
  result JSON;
  today_bookings_count INTEGER := 0;
  pending_bookings_count INTEGER := 0;
  upcoming_bookings_count INTEGER := 0;
  avg_rating DECIMAL := 0;
  recent_reviews_count INTEGER := 0;
  todays_net_settlement DECIMAL := 0;
  todays_online_settlement DECIMAL := 0;
  todays_offline_settlement DECIMAL := 0;
  cancelled_bookings_count INTEGER := 0;
  cancelled_amount DECIMAL := 0;
  occupancy_rate INTEGER := 0;
  venue_count INTEGER := 1;
begin
  -- Get user role (highest priority)
  select case
    when exists(select 1 from user_roles where user_id = admin_user_id and role = 'super_admin') then 'super_admin'
    when exists(select 1 from user_roles where user_id = admin_user_id and role = 'admin') then 'admin'
    else 'user'
  end into user_role;

  -- Get venue access based on role
  if user_role = 'admin' then
    select array_agg(venue_id) into venue_ids
    from venue_admins
    where user_id = admin_user_id;

    venue_count := coalesce(array_length(venue_ids, 1), 1);
  elsif user_role = 'super_admin' then
    select array_agg(id) into venue_ids
    from venues
    where is_active = true;

    select count(*) into venue_count
    from venues
    where is_active = true;
  else
    -- Regular user - no access
    venue_ids := array[]::UUID[];
  end if;

  -- If no venue access, return empty stats
  if venue_ids is null or array_length(venue_ids, 1) = 0 then
    select json_build_object(
      'todayBookings', 0,
      'pendingBookings', 0,
      'upcomingBookings', 0,
      'averageRating', 0,
      'recentReviews', 0,
      'todaysRevenue', 0,
      'todaysNetSettlement', 0,
      'todaysOnlineSettlement', 0,
      'todaysOfflineSettlement', 0,
      'cancelledBookingsCount', 0,
      'cancelledAmount', 0,
      'occupancyRate', 0,
      'userRole', user_role,
      'venueCount', 0
    ) into result;
    return result;
  end if;

  -- 🚀 OPTIMIZED: Single query for all booking stats with OPTION B NET SETTLEMENT CALCULATION
  -- FIXED: Platform fees calculated on original amounts, venues bear full coupon costs
  with booking_stats as (
    select
      count(*) filter (where booking_date = target_date and status in ('confirmed', 'pending', 'completed')) as today_count,
      count(*) filter (where booking_date >= target_date and status = 'pending') as pending_count,
      count(*) filter (where booking_date >= target_date and booking_date <= target_date + interval '7 days' and status in ('confirmed', 'pending')) as upcoming_count,
      count(*) filter (where booking_date >= target_date - interval '7 days' and booking_date <= target_date and status in ('confirmed', 'completed')) as recent_count,
      count(*) filter (where booking_date = target_date and status = 'cancelled') as cancelled_count,

      -- OPTION B NET SETTLEMENT: What venue actually receives in bank account
      -- Formula: User Payment - Platform Fee (calculated on original amount)
      coalesce(
        sum(
          case
            when booking_date = target_date and status in ('confirmed', 'completed') then
              -- User payment (post-discount) minus platform fee (on original amount)
              b.total_price - (
                coalesce(cu.original_price, b.total_price) *
                (coalesce(v.platform_fee_percentage, 5.0) / 100)
              )
            else 0
          end
        ), 0
      ) as today_net_settlement,

      -- ONLINE NET SETTLEMENT (payment_method = 'online')
      coalesce(
        sum(
          case
            when booking_date = target_date and status in ('confirmed', 'completed') and b.payment_method = 'online' then
              b.total_price - (
                coalesce(cu.original_price, b.total_price) *
                (coalesce(v.platform_fee_percentage, 5.0) / 100)
              )
            else 0
          end
        ), 0
      ) as today_online_settlement,

      -- OFFLINE NET SETTLEMENT (payment_method = 'cash')
      coalesce(
        sum(
          case
            when booking_date = target_date and status in ('confirmed', 'completed') and b.payment_method = 'cash' then
              b.total_price - (
                coalesce(cu.original_price, b.total_price) *
                (coalesce(v.platform_fee_percentage, 5.0) / 100)
              )
            else 0
          end
        ), 0
      ) as today_offline_settlement,

      -- CANCELLED BOOKINGS AMOUNT (potential revenue lost)
      coalesce(
        sum(
          case
            when booking_date = target_date and status = 'cancelled' then
              coalesce(cu.original_price, b.total_price)
            else 0
          end
        ), 0
      ) as cancelled_amount

    from bookings b
    inner join courts c on b.court_id = c.id
    inner join venues v on c.venue_id = v.id
    left join coupon_usage cu on b.id = cu.booking_id
    where c.venue_id = any(venue_ids)
  ),
  review_stats as (
    select
      coalesce(avg(rating), 0) as avg_rating,
      count(*) filter (where created_at >= target_date - interval '30 days') as recent_count
    from reviews
    where is_approved = true
    and (user_role = 'super_admin' or venue_id = any(venue_ids))
  )
  select
    bs.today_count,
    bs.pending_count,
    bs.upcoming_count,
    bs.recent_count,
    bs.cancelled_count,
    bs.today_net_settlement,
    bs.today_online_settlement,
    bs.today_offline_settlement,
    bs.cancelled_amount,
    rs.avg_rating,
    rs.recent_count as recent_reviews
  into
    today_bookings_count,
    pending_bookings_count,
    upcoming_bookings_count,
    occupancy_rate, -- Reuse recent_count for occupancy calculation
    cancelled_bookings_count,
    todays_net_settlement,
    todays_online_settlement,
    todays_offline_settlement,
    cancelled_amount,
    avg_rating,
    recent_reviews_count
  from booking_stats bs, review_stats rs;

  -- Calculate occupancy rate (simplified: recent bookings vs target)
  occupancy_rate := least(100, round((occupancy_rate * 100.0) / (venue_count * 10 * 7))); -- 10 bookings/day/venue for 7 days

  -- Build result JSON with Option B calculations
  select json_build_object(
    'todayBookings', today_bookings_count,
    'pendingBookings', pending_bookings_count,
    'upcomingBookings', upcoming_bookings_count,
    'averageRating', round(avg_rating, 1),
    'recentReviews', recent_reviews_count,
    'todaysRevenue', todays_net_settlement, -- Legacy field for backward compatibility
    'todaysNetSettlement', todays_net_settlement,
    'todaysOnlineSettlement', todays_online_settlement,
    'todaysOfflineSettlement', todays_offline_settlement,
    'cancelledBookingsCount', cancelled_bookings_count,
    'cancelledAmount', cancelled_amount,
    'occupancyRate', occupancy_rate,
    'userRole', user_role,
    'venueCount', venue_count,
    'venueIds', venue_ids
  ) into result;

  return result;
end;
$$;

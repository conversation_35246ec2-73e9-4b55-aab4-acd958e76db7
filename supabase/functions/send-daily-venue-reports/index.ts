import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get MSG91 WhatsApp configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const integratedNumber = Deno.env.get('MSG91_INTEGRATED_NUMBER') || '919211848599'

    if (!authKey) {
      console.error('MSG91 configuration missing')
      return new Response(
        JSON.stringify({ error: 'MSG91 configuration not found' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body to get date (optional, defaults to current date)
    let reportDate = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
    
    try {
      const body = await req.json()
      if (body.date) {
        reportDate = body.date
      }
    } catch {
      // Use default date if no body or invalid JSON
    }

    console.log(`📊 Starting daily venue reports for date: ${reportDate}`)

    // Get venue daily summary data
    const { data: venueReports, error: venueError } = await supabaseAdmin
      .rpc('get_venue_daily_summary', { p_date: reportDate })

    if (venueError) {
      console.error('Error fetching venue daily summary:', venueError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch venue data', details: venueError.message }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!venueReports || venueReports.length === 0) {
      console.log('No venue reports to send for date:', reportDate)
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'No venues with activity found for the specified date',
          date: reportDate,
          venues_processed: 0
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log(`📱 Found ${venueReports.length} venue reports to send`)

    let successCount = 0
    let errorCount = 0
    const errors: string[] = []

    // Process each venue report
    for (const report of venueReports) {
      try {
        // Format phone number
        const adminPhone = formatPhoneNumber(report.admin_phone)
        
        if (!adminPhone) {
          console.error(`Invalid phone number for venue ${report.venue_name}: ${report.admin_phone}`)
          errorCount++
          errors.push(`Invalid phone number for venue ${report.venue_name}`)
          continue
        }

        // Prepare MSG91 WhatsApp payload for daily_venue_report template
        const msg91Payload = {
          integrated_number: integratedNumber,
          content_type: "template",
          payload: {
            messaging_product: "whatsapp",
            type: "template",
            template: {
              name: "daily_venue_report",
              language: {
                code: "en_US",
                policy: "deterministic"
              },
              namespace: "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
              to_and_components: [
                {
                  to: [adminPhone],
                  components: {
                    header_1: { type: "text", value: report.venue_name },
                    body_1: { type: "text", value: report.venue_name },
                    body_2: { type: "text", value: report.report_date },
                    body_3: { type: "text", value: report.total_bookings },
                    body_4: { type: "text", value: report.confirmed_bookings },
                    body_5: { type: "text", value: report.cancelled_bookings },
                    body_6: { type: "text", value: report.gross_revenue },
                    body_7: { type: "text", value: report.net_revenue },
                    body_8: { type: "text", value: report.coupon_usage_count }
                  }
                }
              ]
            }
          }
        }

        console.log(`📤 Sending daily report to ${report.admin_name} (${adminPhone}) for venue: ${report.venue_name}`)

        // Send WhatsApp message via MSG91 API
        const response = await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'authkey': authKey
          },
          body: JSON.stringify(msg91Payload)
        })

        if (response.ok) {
          successCount++
          console.log(`✅ Daily report sent successfully to ${report.venue_name}`)
        } else {
          const errorText = await response.text()
          console.error(`❌ Failed to send daily report to ${report.venue_name}:`, errorText)
          errorCount++
          errors.push(`Failed to send to ${report.venue_name}: ${errorText}`)
        }

        // Add delay between messages to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000))

      } catch (error) {
        console.error(`❌ Error processing venue ${report.venue_name}:`, error)
        errorCount++
        errors.push(`Error processing ${report.venue_name}: ${error.message}`)
      }
    }

    // Log execution summary
    console.log(`📈 Daily venue reports execution summary:`)
    console.log(`   Date: ${reportDate}`)
    console.log(`   Total venues: ${venueReports.length}`)
    console.log(`   Successful sends: ${successCount}`)
    console.log(`   Failed sends: ${errorCount}`)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Daily venue reports processing completed',
        date: reportDate,
        total_venues: venueReports.length,
        successful_sends: successCount,
        failed_sends: errorCount,
        errors: errors
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('❌ Critical error in daily venue reports:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

// Helper function to format phone numbers
function formatPhoneNumber(phone: string): string | null {
  if (!phone) return null
  
  // Clean the phone number
  let formatted = phone.toString().trim().replace(/[^\d+]/g, '')
  
  // Format to +91XXXXXXXXXX
  if (formatted.startsWith('0')) {
    formatted = '+91' + formatted.substring(1)
  } else if (formatted.startsWith('91') && formatted.length === 12) {
    formatted = '+' + formatted
  } else if (!formatted.startsWith('+91') && formatted.length === 10) {
    formatted = '+91' + formatted
  } else if (!formatted.startsWith('+')) {
    formatted = '+' + formatted
  }
  
  // Validate final format (+91 followed by 10 digits)
  const phoneRegex = /^\+91[6-9]\d{9}$/
  return phoneRegex.test(formatted) ? formatted : null
}

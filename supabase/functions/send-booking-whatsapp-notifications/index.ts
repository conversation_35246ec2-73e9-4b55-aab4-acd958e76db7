import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get MSG91 WhatsApp configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const integratedNumber = Deno.env.get('MSG91_INTEGRATED_NUMBER') || '919211848599'

    if (!authKey) {
      console.error('MSG91 configuration missing')
      return new Response(
        JSON.stringify({ error: 'MSG91 configuration not found' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body
    const { bookingId } = await req.json()

    if (!bookingId) {
      return new Response(
        JSON.stringify({ error: 'Missing required field: bookingId' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Processing booking WhatsApp notifications for booking:', bookingId)

    // Get booking details with court and venue information
    const { data: bookingData, error: bookingError } = await supabaseAdmin
      .from('bookings')
      .select(`
        id,
        user_id,
        court_id,
        booking_date,
        start_time,
        end_time,
        booking_reference,
        status
      `)
      .eq('id', bookingId)
      .single()

    if (bookingError || !bookingData) {
      console.error('Error fetching booking data:', bookingError)
      return new Response(
        JSON.stringify({ error: 'Booking not found' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Get court and venue information
    const { data: courtData, error: courtError } = await supabaseAdmin
      .from('courts')
      .select(`
        id,
        name,
        venue_id,
        sport_id,
        venues!inner(
          id,
          name
        ),
        sports!inner(
          id,
          name
        )
      `)
      .eq('id', bookingData.court_id)
      .single()

    if (courtError || !courtData) {
      console.error('Error fetching court data:', courtError)
      return new Response(
        JSON.stringify({ error: 'Court not found' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Get user information
    const { data: userData, error: userError } = await supabaseAdmin
      .from('profiles')
      .select('full_name, phone')
      .eq('id', bookingData.user_id)
      .single()

    if (userError || !userData) {
      console.error('Error fetching user data:', userError)
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Get venue-specific admin user IDs first
    const { data: venueAdminRoles, error: venueAdminError } = await supabaseAdmin
      .from('venue_admins')
      .select('user_id')
      .eq('venue_id', courtData.venue_id)

    if (venueAdminError) {
      console.error('Error fetching venue admin roles:', venueAdminError)
    }

    // Get venue admin profiles with phone numbers
    let venueAdmins = []
    if (venueAdminRoles && venueAdminRoles.length > 0) {
      const venueAdminUserIds = venueAdminRoles.map(role => role.user_id)
      const { data: adminProfiles, error: profilesError } = await supabaseAdmin
        .from('profiles')
        .select('id, full_name, phone')
        .in('id', venueAdminUserIds)
        .not('phone', 'is', null)
        .neq('phone', '')
        .neq('phone', '000000000') // Exclude test phone numbers

      if (!profilesError && adminProfiles) {
        venueAdmins = adminProfiles
      } else if (profilesError) {
        console.error('Error fetching venue admin profiles:', profilesError)
      }
    }

    // Prepare template variables
    const userName = userData.full_name || 'Customer'
    const venueName = courtData.venues.name
    const courtName = courtData.name
    const sportName = courtData.sports.name
    const bookingReference = bookingData.booking_reference

    // Format date and time
    const bookingDate = new Date(bookingData.booking_date).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })

    const startTime = bookingData.start_time.substring(0, 5) // HH:MM format
    const endTime = bookingData.end_time.substring(0, 5) // HH:MM format

    console.log('Template variables:', {
      userName,
      venueName,
      courtName,
      sportName,
      bookingDate,
      startTime,
      endTime,
      bookingReference,
      venueId: courtData.venue_id
    })

    let userNotificationSent = false
    let adminNotificationsSent = 0

    // Send WhatsApp notification to user if they have a valid phone number
    if (userData.phone && userData.phone !== '' && userData.phone !== '000000000') {
      const userPhone = formatPhoneNumber(userData.phone)

      const userPayload = {
        integrated_number: integratedNumber,
        content_type: "template",
        payload: {
          messaging_product: "whatsapp",
          type: "template",
          template: {
            name: "grid2play_booking_user",
            language: {
              code: "En_US",
              policy: "deterministic"
            },
            namespace: "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
            to_and_components: [
              {
                to: [userPhone],
                components: {
                  body_1: { type: "text", value: userName },
                  body_2: { type: "text", value: venueName },
                  body_3: { type: "text", value: bookingDate },
                  body_4: { type: "text", value: startTime },
                  body_5: { type: "text", value: endTime },
                  body_6: { type: "text", value: courtName },
                  body_7: { type: "text", value: bookingReference }
                }
              }
            ]
          }
        }
      }

      console.log('Sending user WhatsApp notification to:', userPhone)

      try {
        const userResponse = await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'authkey': authKey
          },
          body: JSON.stringify(userPayload)
        })

        if (userResponse.ok) {
          userNotificationSent = true
          console.log('User WhatsApp notification sent successfully')
        } else {
          const errorText = await userResponse.text()
          console.error('User WhatsApp notification failed:', errorText)
        }
      } catch (error) {
        console.error('Error sending user WhatsApp notification:', error)
      }
    } else {
      console.log('User has no valid phone number, skipping WhatsApp notification')
    }

    // Send WhatsApp notifications to venue-specific admin users
    if (venueAdmins && venueAdmins.length > 0) {
      const adminPhones = venueAdmins.map(admin => formatPhoneNumber(admin.phone))

      // Create separate to_and_components entries for each admin to prevent duplicates
      const adminToAndComponents = adminPhones.map(phone => ({
        to: [phone],
        components: {
          body_1: { type: "text", value: userName },
          body_2: { type: "text", value: courtName },
          body_3: { type: "text", value: venueName },
          body_4: { type: "text", value: bookingDate },
          body_5: { type: "text", value: startTime },
          body_6: { type: "text", value: endTime },
          body_7: { type: "text", value: courtName },
          body_8: { type: "text", value: sportName },
          body_9: { type: "text", value: bookingReference }
        }
      }))

      const adminPayload = {
        integrated_number: integratedNumber,
        content_type: "template",
        payload: {
          messaging_product: "whatsapp",
          type: "template",
          template: {
            name: "grid2play_booking_admin",
            language: {
              code: "En",
              policy: "deterministic"
            },
            namespace: "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
            to_and_components: adminToAndComponents
          }
        }
      }

      console.log('Sending admin WhatsApp notifications to venue admins:', adminPhones)

      try {
        const adminResponse = await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'authkey': authKey
          },
          body: JSON.stringify(adminPayload)
        })

        if (adminResponse.ok) {
          adminNotificationsSent = adminPhones.length
          console.log('Admin WhatsApp notifications sent successfully to', adminPhones.length, 'venue admins')
        } else {
          const errorText = await adminResponse.text()
          console.error('Admin WhatsApp notifications failed:', errorText)
        }
      } catch (error) {
        console.error('Error sending admin WhatsApp notifications:', error)
      }
    } else {
      console.log('No venue admin users with valid phone numbers found for venue:', courtData.venue_id)
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Booking WhatsApp notifications processed',
        user_notification_sent: userNotificationSent,
        admin_notifications_sent: adminNotificationsSent,
        booking_reference: bookingReference,
        venue_id: courtData.venue_id
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Error in send-booking-whatsapp-notifications function:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error',
        message: error.message
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

// Helper function to format phone numbers
function formatPhoneNumber(phone: string): string {
  let formattedPhone = phone.trim()

  // Ensure phone number is in international format
  if (formattedPhone.startsWith('0')) {
    formattedPhone = '+91' + formattedPhone.substring(1)
  } else if (!formattedPhone.startsWith('+')) {
    formattedPhone = '+91' + formattedPhone
  }

  return formattedPhone
}

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get MSG91 WhatsApp configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const integratedNumber = Deno.env.get('MSG91_INTEGRATED_NUMBER') || '919211848599'

    console.log('🔍 Testing MSG91 Daily Reports Integration')
    console.log('Auth Key available:', !!authKey)
    console.log('Integrated Number:', integratedNumber)

    if (!authKey) {
      console.error('❌ MSG91_AUTH_KEY not found in environment variables')
      return new Response(
        JSON.stringify({ 
          error: 'MSG91_AUTH_KEY not configured',
          available_env_vars: Object.keys(Deno.env.toObject()).filter(key => key.includes('MSG91'))
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body to get test phone number
    let testPhone = '+919876543210' // Default test number
    try {
      const body = await req.json()
      if (body.phone) {
        testPhone = body.phone
      }
    } catch {
      // Use default phone if no body
    }

    console.log('📱 Testing with phone number:', testPhone)

    // Test MSG91 daily_venue_report template
    const testPayload = {
      integrated_number: integratedNumber,
      content_type: "template",
      payload: {
        messaging_product: "whatsapp",
        type: "template",
        template: {
          name: "daily_venue_report",
          language: {
            code: "en_US",
            policy: "deterministic"
          },
          namespace: "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
          to_and_components: [
            {
              to: [testPhone],
              components: {
                header_1: { type: "text", value: "Test Venue Arena" },
                body_1: { type: "text", value: "Test Venue Arena" },
                body_2: { type: "text", value: "04/07/2025" },
                body_3: { type: "text", value: "8" },
                body_4: { type: "text", value: "7" },
                body_5: { type: "text", value: "1" },
                body_6: { type: "text", value: "₹3,500" },
                body_7: { type: "text", value: "₹3,325" },
                body_8: { type: "text", value: "2" }
              }
            }
          ]
        }
      }
    }

    console.log('🚀 Sending test message to MSG91...')
    console.log('Payload:', JSON.stringify(testPayload, null, 2))

    // Send test message via MSG91 API
    const response = await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'authkey': authKey
      },
      body: JSON.stringify(testPayload)
    })

    const responseText = await response.text()
    console.log('📊 MSG91 Response Status:', response.status)
    console.log('📊 MSG91 Response Body:', responseText)

    let responseData
    try {
      responseData = JSON.parse(responseText)
    } catch {
      responseData = { raw_response: responseText }
    }

    if (response.ok) {
      console.log('✅ MSG91 test message sent successfully!')
      return new Response(
        JSON.stringify({
          success: true,
          message: 'MSG91 daily_venue_report template test successful',
          test_phone: testPhone,
          msg91_response: responseData,
          status_code: response.status,
          template_name: 'daily_venue_report',
          integrated_number: integratedNumber
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else {
      console.error('❌ MSG91 test message failed!')
      return new Response(
        JSON.stringify({
          success: false,
          error: 'MSG91 API call failed',
          test_phone: testPhone,
          msg91_response: responseData,
          status_code: response.status,
          template_name: 'daily_venue_report',
          integrated_number: integratedNumber,
          troubleshooting: {
            possible_issues: [
              'Template daily_venue_report not approved',
              'Invalid phone number format',
              'MSG91 auth key invalid or expired',
              'Template namespace mismatch',
              'Rate limiting or quota exceeded'
            ]
          }
        }),
        {
          status: response.status,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('💥 Critical error in MSG91 test:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error.message,
        stack: error.stack
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

// Helper function to format phone numbers (same as in other functions)
function formatPhoneNumber(phone: string): string | null {
  if (!phone) return null
  
  // Clean the phone number
  let formatted = phone.toString().trim().replace(/[^\d+]/g, '')
  
  // Format to +91XXXXXXXXXX
  if (formatted.startsWith('0')) {
    formatted = '+91' + formatted.substring(1)
  } else if (formatted.startsWith('91') && formatted.length === 12) {
    formatted = '+' + formatted
  } else if (!formatted.startsWith('+91') && formatted.length === 10) {
    formatted = '+91' + formatted
  } else if (!formatted.startsWith('+')) {
    formatted = '+' + formatted
  }
  
  // Validate final format (+91 followed by 10 digits)
  const phoneRegex = /^\+91[6-9]\d{9}$/
  return phoneRegex.test(formatted) ? formatted : null
}

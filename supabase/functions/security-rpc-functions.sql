-- Grid2Play Security RPC Functions
-- These functions provide server-side validation and security checks

-- 1. Secure Price Validation Function
CREATE OR REPLACE FUNCTION validate_booking_price_security(
    p_court_id UUID,
    p_booking_date DATE,
    p_start_time TIME,
    p_end_time TIME,
    p_client_price NUMERIC
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_server_price NUMERIC := 0;
    v_slot_count INTEGER := 0;
    v_court_exists BOOLEAN := FALSE;
    v_venue_id UUID;
    v_sport_id UUID;
    v_price_difference NUMERIC;
    v_tolerance NUMERIC := 0.01; -- 1 paisa tolerance for rounding
BEGIN
    -- Check if court exists and is active
    SELECT 
        c.venue_id, 
        c.sport_id,
        TRUE
    INTO 
        v_venue_id, 
        v_sport_id,
        v_court_exists
    FROM courts c
    WHERE c.id = p_court_id 
    AND c.is_active = TRUE;
    
    IF NOT v_court_exists THEN
        RETURN json_build_object(
            'valid', false,
            'error', 'Invalid or inactive court',
            'security_violation', true
        );
    END IF;
    
    -- Calculate server-side price from template_slots
    SELECT 
        COALESCE(SUM(ts.price), 0),
        COUNT(*)
    INTO 
        v_server_price,
        v_slot_count
    FROM template_slots ts
    WHERE ts.court_id = p_court_id
    AND ts.start_time >= p_start_time
    AND ts.end_time <= p_end_time
    AND ts.is_active = TRUE;
    
    -- If no template slots found, return error
    IF v_slot_count = 0 THEN
        RETURN json_build_object(
            'valid', false,
            'error', 'No pricing data available for selected time slots',
            'security_violation', true
        );
    END IF;
    
    -- Calculate price difference
    v_price_difference := ABS(v_server_price - p_client_price);
    
    -- Check if price matches within tolerance
    IF v_price_difference > v_tolerance THEN
        -- Log potential price tampering attempt
        INSERT INTO security_logs (
            event_type,
            severity,
            details,
            created_at
        ) VALUES (
            'PRICE_TAMPERING_ATTEMPT',
            'HIGH',
            json_build_object(
                'court_id', p_court_id,
                'booking_date', p_booking_date,
                'client_price', p_client_price,
                'server_price', v_server_price,
                'difference', v_price_difference,
                'user_agent', current_setting('request.headers', true)::json->>'user-agent'
            ),
            NOW()
        );
        
        RETURN json_build_object(
            'valid', false,
            'error', 'Price validation failed - security violation detected',
            'security_violation', true,
            'server_price', v_server_price,
            'client_price', p_client_price
        );
    END IF;
    
    -- Return successful validation
    RETURN json_build_object(
        'valid', true,
        'server_price', v_server_price,
        'slot_count', v_slot_count,
        'venue_id', v_venue_id,
        'sport_id', v_sport_id
    );
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error
        INSERT INTO security_logs (
            event_type,
            severity,
            details,
            created_at
        ) VALUES (
            'PRICE_VALIDATION_ERROR',
            'MEDIUM',
            json_build_object(
                'error', SQLERRM,
                'court_id', p_court_id,
                'booking_date', p_booking_date
            ),
            NOW()
        );
        
        RETURN json_build_object(
            'valid', false,
            'error', 'Price validation service error'
        );
END;
$$;

-- 2. Secure Coupon Validation with Rate Limiting
CREATE OR REPLACE FUNCTION validate_coupon_with_security(
    p_coupon_code TEXT,
    p_venue_id UUID,
    p_original_price NUMERIC,
    p_user_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_attempt_count INTEGER;
    v_last_attempt TIMESTAMP;
    v_rate_limit_window INTERVAL := '1 minute';
    v_max_attempts INTEGER := 3;
    v_coupon_result JSON;
BEGIN
    -- Sanitize coupon code
    p_coupon_code := UPPER(TRIM(REGEXP_REPLACE(p_coupon_code, '[^A-Z0-9]', '', 'g')));
    
    -- Check rate limiting
    SELECT 
        COUNT(*),
        MAX(created_at)
    INTO 
        v_attempt_count,
        v_last_attempt
    FROM coupon_validation_attempts
    WHERE user_id = p_user_id
    AND created_at > NOW() - v_rate_limit_window;
    
    -- Enforce rate limit
    IF v_attempt_count >= v_max_attempts THEN
        -- Log rate limit violation
        INSERT INTO security_logs (
            event_type,
            severity,
            details,
            user_id,
            created_at
        ) VALUES (
            'COUPON_RATE_LIMIT_VIOLATION',
            'MEDIUM',
            json_build_object(
                'user_id', p_user_id,
                'attempt_count', v_attempt_count,
                'coupon_code', p_coupon_code
            ),
            p_user_id,
            NOW()
        );
        
        RETURN json_build_object(
            'valid', false,
            'error', 'Too many coupon attempts. Please wait a minute.',
            'rate_limited', true
        );
    END IF;
    
    -- Log the attempt
    INSERT INTO coupon_validation_attempts (
        user_id,
        coupon_code,
        venue_id,
        original_price,
        created_at
    ) VALUES (
        p_user_id,
        p_coupon_code,
        p_venue_id,
        p_original_price,
        NOW()
    );
    
    -- Call the original coupon validation function
    SELECT validate_and_apply_coupon(
        p_coupon_code,
        p_venue_id,
        p_original_price,
        p_user_id
    ) INTO v_coupon_result;
    
    -- Log successful validation if coupon is valid
    IF (v_coupon_result->>'valid')::boolean THEN
        INSERT INTO security_logs (
            event_type,
            severity,
            details,
            user_id,
            created_at
        ) VALUES (
            'COUPON_VALIDATION_SUCCESS',
            'LOW',
            json_build_object(
                'user_id', p_user_id,
                'coupon_code', p_coupon_code,
                'discount_amount', v_coupon_result->>'discount_amount'
            ),
            p_user_id,
            NOW()
        );
    END IF;
    
    RETURN v_coupon_result;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error
        INSERT INTO security_logs (
            event_type,
            severity,
            details,
            user_id,
            created_at
        ) VALUES (
            'COUPON_VALIDATION_ERROR',
            'HIGH',
            json_build_object(
                'error', SQLERRM,
                'user_id', p_user_id,
                'coupon_code', p_coupon_code
            ),
            p_user_id,
            NOW()
        );
        
        RETURN json_build_object(
            'valid', false,
            'error', 'Coupon validation service error'
        );
END;
$$;

-- 3. Create required security tables
CREATE TABLE IF NOT EXISTS security_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    details JSONB,
    user_id UUID REFERENCES auth.users(id),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS coupon_validation_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    coupon_code TEXT NOT NULL,
    venue_id UUID REFERENCES venues(id),
    original_price NUMERIC,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON security_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON security_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_coupon_attempts_user_created ON coupon_validation_attempts(user_id, created_at);

-- Enable RLS on security tables
ALTER TABLE security_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupon_validation_attempts ENABLE ROW LEVEL SECURITY;

-- RLS policies for security logs (admin access only)
CREATE POLICY "Admin can view security logs" ON security_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role IN ('super_admin', 'admin')
        )
    );

-- RLS policies for coupon attempts (users can view their own)
CREATE POLICY "Users can view own coupon attempts" ON coupon_validation_attempts
    FOR SELECT USING (user_id = auth.uid());

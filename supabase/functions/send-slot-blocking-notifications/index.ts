import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Add debug endpoint
  if (req.method === 'GET') {
    return new Response(
      JSON.stringify({
        status: 'Slot Blocking Notifications Edge Function is running',
        timestamp: new Date().toISOString(),
        environment: {
          supabase_url: !!Deno.env.get('SUPABASE_URL'),
          service_role_key: !!Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'),
          msg91_auth_key: !!Deno.env.get('MSG91_AUTH_KEY'),
          integrated_number: Deno.env.get('MSG91_INTEGRATED_NUMBER') || '919211848599'
        }
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }

  try {
    // Check if this is an internal call (from pg_net) by checking user agent
    const userAgent = req.headers.get('user-agent') || ''
    const isInternalCall = userAgent.includes('pg_net')

    console.log(`📡 Request source: ${isInternalCall ? 'Internal (pg_net)' : 'External'}, User-Agent: ${userAgent}`)

    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get MSG91 WhatsApp configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const integratedNumber = Deno.env.get('MSG91_INTEGRATED_NUMBER') || '919211848599'

    if (!authKey) {
      console.error('MSG91 configuration missing')
      return new Response(
        JSON.stringify({ error: 'MSG91 configuration not found' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // For external calls (not from pg_net), require authentication
    if (!isInternalCall) {
      const authHeader = req.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        console.error('❌ External call without proper authorization')
        return new Response(
          JSON.stringify({ error: 'Authorization required for external calls' }),
          {
            status: 401,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }
    }

    // Parse request body to get blocked slot ID
    let blocked_slot_id: string

    try {
      const body = await req.json()
      blocked_slot_id = body.blocked_slot_id
    } catch (parseError) {
      console.error('Error parsing request body:', parseError)
      return new Response(
        JSON.stringify({ error: 'Invalid JSON in request body' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!blocked_slot_id) {
      return new Response(
        JSON.stringify({ error: 'blocked_slot_id is required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log(`🚫 Starting slot blocking notifications for blocked_slot_id: ${blocked_slot_id}`)

    // Get blocked slot details and venue admins
    const { data: slotData, error: slotError } = await supabaseAdmin
      .rpc('send_slot_blocking_notification', { p_blocked_slot_id: blocked_slot_id })

    if (slotError) {
      console.error('Error getting slot blocking notification data:', slotError)
      return new Response(
        JSON.stringify({ error: 'Failed to get notification data', details: slotError.message || slotError }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Slot data received:', JSON.stringify(slotData, null, 2))

    if (!slotData.success || !slotData.msg91_payloads || slotData.msg91_payloads.length === 0) {
      console.log('No notifications to send:', slotData.message)
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: slotData.message || 'No venue admins found to notify',
          venue_name: slotData.venue_name,
          notifications_sent: 0
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log(`📱 Found ${slotData.msg91_payloads.length} venue admins to notify for ${slotData.venue_name}`)

    let successCount = 0
    let errorCount = 0
    const errors: string[] = []

    // Process each venue admin notification
    for (const payload of slotData.msg91_payloads) {
      try {
        console.log(`📤 Sending slot blocking notification to ${payload.admin_name} (${payload.admin_phone})`)
        console.log('MSG91 Payload:', JSON.stringify(payload.msg91_payload, null, 2))

        // Send WhatsApp message via MSG91 API
        const response = await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'authkey': authKey
          },
          body: JSON.stringify(payload.msg91_payload)
        })

        const responseText = await response.text()
        console.log(`MSG91 Response Status: ${response.status}`)
        console.log(`MSG91 Response Body: ${responseText}`)

        if (response.ok) {
          successCount++
          console.log(`✅ Slot blocking notification sent successfully to ${payload.admin_name}`)
        } else {
          console.error(`❌ Failed to send notification to ${payload.admin_name}:`, responseText)
          errorCount++
          errors.push(`Failed to send to ${payload.admin_name}: ${responseText}`)
        }

        // Add delay between messages to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000))

      } catch (error) {
        console.error(`❌ Error processing notification for ${payload.admin_name}:`, error)
        errorCount++
        errors.push(`Error processing ${payload.admin_name}: ${error.message}`)
      }
    }

    // Log execution summary
    console.log(`📈 Slot blocking notifications execution summary:`)
    console.log(`   Venue: ${slotData.venue_name}`)
    console.log(`   Court: ${slotData.court_name}`)
    console.log(`   Date: ${slotData.blocked_date}`)
    console.log(`   Time: ${slotData.time_range}`)
    console.log(`   Total admins: ${slotData.msg91_payloads.length}`)
    console.log(`   Successful sends: ${successCount}`)
    console.log(`   Failed sends: ${errorCount}`)

    // Log to automation_logs table
    try {
      await supabaseAdmin
        .from('automation_logs')
        .insert({
          automation_type: 'slot_blocking_notifications',
          execution_date: new Date().toISOString().split('T')[0],
          status: successCount > 0 ? 'completed' : 'completed_no_data',
          total_venues: 1,
          successful_sends: successCount,
          failed_sends: errorCount,
          notes: `Venue: ${slotData.venue_name}, Court: ${slotData.court_name}, Date: ${slotData.blocked_date}`,
          response_data: {
            venue_name: slotData.venue_name,
            court_name: slotData.court_name,
            blocked_date: slotData.blocked_date,
            time_range: slotData.time_range,
            errors: errors
          }
        })
    } catch (logError) {
      console.error('Failed to log automation execution:', logError)
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Slot blocking notifications processing completed',
        venue_name: slotData.venue_name,
        court_name: slotData.court_name,
        blocked_date: slotData.blocked_date,
        time_range: slotData.time_range,
        total_admins: slotData.msg91_payloads.length,
        successful_sends: successCount,
        failed_sends: errorCount,
        errors: errors
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('❌ Unexpected error in slot blocking notifications:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

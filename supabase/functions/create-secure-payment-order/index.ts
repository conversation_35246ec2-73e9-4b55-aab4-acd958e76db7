import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PaymentOrderRequest {
  courtId: string;
  bookingDate: string;
  startTime: string;
  endTime: string;
  slots: string[];
  couponCode?: string;
  guestName?: string;
  guestPhone?: string;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get user from JWT
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const requestData: PaymentOrderRequest = await req.json()

    // Validate input data
    if (!requestData.courtId || !requestData.bookingDate || !requestData.startTime || !requestData.endTime) {
      return new Response(
        JSON.stringify({ error: 'Missing required booking data' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Sanitize inputs
    const sanitizedData = {
      courtId: requestData.courtId.trim(),
      bookingDate: requestData.bookingDate.trim(),
      startTime: requestData.startTime.trim(),
      endTime: requestData.endTime.trim(),
      slots: requestData.slots.map(slot => slot.trim()),
      couponCode: requestData.couponCode?.trim().toUpperCase(),
      guestName: requestData.guestName?.trim().substring(0, 100),
      guestPhone: requestData.guestPhone?.replace(/[^\d+]/g, '')
    }

    // Step 1: Validate booking price security
    const { data: priceValidation, error: priceError } = await supabaseClient
      .rpc('validate_booking_price_security', {
        p_court_id: sanitizedData.courtId,
        p_booking_date: sanitizedData.bookingDate,
        p_start_time: sanitizedData.startTime,
        p_end_time: sanitizedData.endTime,
        p_client_price: 0 // Will be calculated server-side
      })

    if (priceError || !priceValidation.valid) {
      console.error('Price validation failed:', priceError || priceValidation.error)
      
      // Log security violation
      await supabaseClient.from('security_logs').insert({
        event_type: 'PAYMENT_ORDER_PRICE_VALIDATION_FAILED',
        severity: 'HIGH',
        details: {
          user_id: user.id,
          court_id: sanitizedData.courtId,
          error: priceValidation.error || 'Price validation failed'
        },
        user_id: user.id
      })

      return new Response(
        JSON.stringify({ error: 'Price validation failed' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    let finalPrice = priceValidation.server_price
    let discountApplied = 0
    let couponDetails = null

    // Step 2: Validate coupon if provided
    if (sanitizedData.couponCode) {
      const { data: couponValidation, error: couponError } = await supabaseClient
        .rpc('validate_coupon_with_security', {
          p_coupon_code: sanitizedData.couponCode,
          p_venue_id: priceValidation.venue_id,
          p_original_price: priceValidation.server_price,
          p_user_id: user.id
        })

      if (couponError) {
        console.error('Coupon validation error:', couponError)
        return new Response(
          JSON.stringify({ error: 'Coupon validation failed' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      if (couponValidation.rate_limited) {
        return new Response(
          JSON.stringify({ error: couponValidation.error }),
          { status: 429, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      if (couponValidation.valid) {
        finalPrice = couponValidation.final_price
        discountApplied = couponValidation.discount_amount
        couponDetails = couponValidation
      }
    }

    // Step 3: Create Razorpay order
    const razorpayKeyId = Deno.env.get('RAZORPAY_KEY_ID')
    const razorpayKeySecret = Deno.env.get('RAZORPAY_KEY_SECRET')

    if (!razorpayKeyId || !razorpayKeySecret) {
      console.error('Razorpay credentials not configured')
      return new Response(
        JSON.stringify({ error: 'Payment service configuration error' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const orderAmount = Math.round(finalPrice * 100) // Convert to paise
    const receipt = `booking_${Date.now()}_${user.id.substring(0, 8)}`

    const razorpayOrder = {
      amount: orderAmount,
      currency: 'INR',
      receipt: receipt,
      notes: {
        court_id: sanitizedData.courtId,
        booking_date: sanitizedData.bookingDate,
        start_time: sanitizedData.startTime,
        end_time: sanitizedData.endTime,
        user_id: user.id,
        original_price: priceValidation.server_price,
        final_price: finalPrice,
        discount_applied: discountApplied,
        coupon_code: sanitizedData.couponCode || null
      }
    }

    const razorpayResponse = await fetch('https://api.razorpay.com/v1/orders', {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`${razorpayKeyId}:${razorpayKeySecret}`)}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(razorpayOrder)
    })

    if (!razorpayResponse.ok) {
      const errorText = await razorpayResponse.text()
      console.error('Razorpay order creation failed:', errorText)
      
      // Log payment order failure
      await supabaseClient.from('security_logs').insert({
        event_type: 'RAZORPAY_ORDER_CREATION_FAILED',
        severity: 'HIGH',
        details: {
          user_id: user.id,
          error: errorText,
          amount: orderAmount
        },
        user_id: user.id
      })

      return new Response(
        JSON.stringify({ error: 'Payment order creation failed' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const razorpayOrderData = await razorpayResponse.json()

    // Step 4: Store secure payment intent
    const { error: intentError } = await supabaseClient
      .from('payment_intents')
      .insert({
        order_id: razorpayOrderData.id,
        user_id: user.id,
        court_id: sanitizedData.courtId,
        booking_date: sanitizedData.bookingDate,
        start_time: sanitizedData.startTime,
        end_time: sanitizedData.endTime,
        original_price: priceValidation.server_price,
        final_price: finalPrice,
        discount_applied: discountApplied,
        coupon_code: sanitizedData.couponCode,
        guest_name: sanitizedData.guestName,
        guest_phone: sanitizedData.guestPhone,
        status: 'created',
        expires_at: new Date(Date.now() + 15 * 60 * 1000) // 15 minutes
      })

    if (intentError) {
      console.error('Payment intent storage failed:', intentError)
      return new Response(
        JSON.stringify({ error: 'Payment intent storage failed' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Log successful payment order creation
    await supabaseClient.from('security_logs').insert({
      event_type: 'PAYMENT_ORDER_CREATED',
      severity: 'LOW',
      details: {
        user_id: user.id,
        order_id: razorpayOrderData.id,
        amount: finalPrice,
        court_id: sanitizedData.courtId
      },
      user_id: user.id
    })

    // Return secure response (no sensitive data)
    return new Response(
      JSON.stringify({
        success: true,
        order: {
          id: razorpayOrderData.id,
          amount: razorpayOrderData.amount,
          currency: razorpayOrderData.currency
        },
        pricing: {
          original_price: priceValidation.server_price,
          final_price: finalPrice,
          discount_applied: discountApplied
        },
        // Note: Razorpay key_id will be provided separately via secure endpoint
        booking_data: sanitizedData
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Payment order creation error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

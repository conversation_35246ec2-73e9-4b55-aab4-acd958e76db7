# Grid2Play Security Configuration
# Copy this file to .env.local and fill in your actual values

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Razorpay Configuration (Server-Side Only)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# Security Configuration
BOOKING_SECURITY_SECRET=your_booking_security_secret_32_chars
CSRF_SECRET=your_csrf_secret_32_chars
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=10

# Grid2Play Specific
GRID2PLAY_PLATFORM_FEE_PERCENTAGE=5
GRID2PLAY_MAX_BOOKING_DAYS_AHEAD=30
GRID2PLAY_BOOKING_TIMEOUT_MINUTES=15

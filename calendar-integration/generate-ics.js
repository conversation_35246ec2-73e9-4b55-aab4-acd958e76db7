/**
 * Grid2Play Direct Calendar Integration
 * Generates .ics calendar files for confirmed bookings
 * Replaces Calendly with direct calendar integration
 */

/**
 * Generate .ics calendar file content for a Grid2Play booking
 * @param {Object} booking - Booking details from Grid2Play database
 * @returns {string} - .ics file content
 */
function generateICSFile(booking) {
  const {
    booking_reference,
    guest_name,
    guest_phone,
    venue_name,
    venue_location,
    court_name,
    sport_name,
    booking_date,
    start_time,
    end_time,
    total_price,
    payment_reference
  } = booking;

  // Convert date and time to proper format
  const startDateTime = formatDateTimeForICS(booking_date, start_time);
  const endDateTime = formatDateTimeForICS(booking_date, end_time);
  const createdDateTime = new Date().toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

  // Generate unique UID for the event
  const uid = `${booking_reference}@grid2play.com`;

  // Create event description with all booking details
  const description = [
    `🏈 ${sport_name} Booking at ${venue_name}`,
    `🎫 Booking Reference: ${booking_reference}`,
    `👤 Player: ${guest_name}`,
    `📞 Contact: ${guest_phone}`,
    `🏟️ Court: ${court_name}`,
    `💰 Amount Paid: ₹${total_price}`,
    `💳 Payment ID: ${payment_reference}`,
    ``,
    `📍 Venue Address:`,
    `${venue_location}`,
    ``,
    `⚡ Powered by Grid२Play - India's Premier Sports Booking Platform`
  ].join('\\n');

  // Generate .ics file content
  const icsContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Grid2Play//Sports Booking Calendar//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:PUBLISH',
    'BEGIN:VEVENT',
    `UID:${uid}`,
    `DTSTAMP:${createdDateTime}`,
    `DTSTART;TZID=Asia/Kolkata:${startDateTime}`,
    `DTEND;TZID=Asia/Kolkata:${endDateTime}`,
    `SUMMARY:${sport_name} Booking - ${venue_name}`,
    `DESCRIPTION:${description}`,
    `LOCATION:${venue_name}, ${venue_location}`,
    `STATUS:CONFIRMED`,
    `TRANSP:OPAQUE`,
    `CATEGORIES:SPORTS,BOOKING,GRID2PLAY`,
    'BEGIN:VALARM',
    'TRIGGER:-PT1H',
    'ACTION:DISPLAY',
    'DESCRIPTION:Your sports booking starts in 1 hour!',
    'END:VALARM',
    'BEGIN:VALARM',
    'TRIGGER:-PT15M',
    'ACTION:DISPLAY',
    'DESCRIPTION:Your sports booking starts in 15 minutes!',
    'END:VALARM',
    'END:VEVENT',
    'END:VCALENDAR'
  ].join('\r\n');

  return icsContent;
}

/**
 * Format date and time for ICS format (YYYYMMDDTHHMMSS)
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} time - Time in HH:MM:SS format
 * @returns {string} - Formatted datetime for ICS
 */
function formatDateTimeForICS(date, time) {
  const dateStr = date.replace(/-/g, '');
  const timeStr = time.replace(/:/g, '').substring(0, 6);
  return `${dateStr}T${timeStr}`;
}

/**
 * Generate calendar URLs for different platforms
 * @param {Object} booking - Booking details
 * @returns {Object} - URLs for different calendar platforms
 */
function generateCalendarUrls(booking) {
  const {
    booking_reference,
    guest_name,
    venue_name,
    venue_location,
    court_name,
    sport_name,
    booking_date,
    start_time,
    end_time,
    total_price
  } = booking;

  // Format for URL encoding
  const title = encodeURIComponent(`${sport_name} Booking - ${venue_name}`);
  const location = encodeURIComponent(`${venue_name}, ${venue_location}`);
  const details = encodeURIComponent([
    `🏈 ${sport_name} at ${court_name}`,
    `🎫 Booking: ${booking_reference}`,
    `👤 Player: ${guest_name}`,
    `💰 Amount: ₹${total_price}`,
    `⚡ Powered by Grid२Play`
  ].join('\n'));

  // Convert to ISO format for URLs
  const startISO = new Date(`${booking_date}T${start_time}+05:30`).toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
  const endISO = new Date(`${booking_date}T${end_time}+05:30`).toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

  return {
    google: `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startISO}/${endISO}&location=${location}&details=${details}`,
    outlook: `https://outlook.live.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startISO}&enddt=${endISO}&location=${location}&body=${details}`,
    yahoo: `https://calendar.yahoo.com/?v=60&view=d&type=20&title=${title}&st=${startISO}&et=${endISO}&in_loc=${location}&desc=${details}`,
    office365: `https://outlook.office.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startISO}&enddt=${endISO}&location=${location}&body=${details}`
  };
}

/**
 * Generate HTML for calendar integration buttons
 * @param {Object} booking - Booking details
 * @returns {string} - HTML for calendar buttons
 */
function generateCalendarButtonsHTML(booking) {
  const urls = generateCalendarUrls(booking);
  
  return `
    <div class="calendar-integration" style="margin: 20px 0; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; background: #f9f9f9;">
      <h3 style="color: #059669; margin-bottom: 15px;">📅 Add to Your Calendar</h3>
      <p style="color: #666; margin-bottom: 15px;">Never miss your sports booking! Add this event to your calendar:</p>
      
      <div class="calendar-buttons" style="display: flex; flex-wrap: wrap; gap: 10px;">
        <a href="${urls.google}" target="_blank" 
           style="background: #4285f4; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; font-size: 14px;">
          📅 Google Calendar
        </a>
        
        <a href="${urls.outlook}" target="_blank"
           style="background: #0078d4; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; font-size: 14px;">
          📅 Outlook
        </a>
        
        <a href="${urls.yahoo}" target="_blank"
           style="background: #7b0099; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; font-size: 14px;">
          📅 Yahoo Calendar
        </a>
        
        <a href="${urls.office365}" target="_blank"
           style="background: #d83b01; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; font-size: 14px;">
          📅 Office 365
        </a>
        
        <button onclick="downloadICSFile('${booking.booking_reference}')"
                style="background: #059669; color: white; padding: 10px 15px; border: none; border-radius: 5px; font-size: 14px; cursor: pointer;">
          📥 Download .ics File
        </button>
      </div>
      
      <p style="color: #666; font-size: 12px; margin-top: 15px;">
        ⏰ Automatic reminders: 1 hour before and 15 minutes before your booking
      </p>
    </div>
  `;
}

// Export functions for use in Grid2Play app
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    generateICSFile,
    generateCalendarUrls,
    generateCalendarButtonsHTML,
    formatDateTimeForICS
  };
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grid२Play Calendar Integration - Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f9fafb;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .booking-details {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .calendar-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .calendar-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
        }
        .calendar-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .google { background: #4285f4; }
        .outlook { background: #0078d4; }
        .yahoo { background: #7b0099; }
        .office365 { background: #d83b01; }
        .download { background: #059669; }
        .ics-content {
            background: #f9f9f9;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .success-message {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏈 Grid२Play Calendar Integration</h1>
            <p>Direct Calendar Integration - No Scheduling Interface!</p>
        </div>

        <div class="success-message">
            ✅ <strong>Perfect!</strong> This is how calendar integration should work - direct add to calendar, no time selection needed!
        </div>

        <div class="booking-details">
            <h3>📋 Your Booking Details</h3>
            <div class="detail-row">
                <span><strong>🎫 Booking Reference:</strong></span>
                <span>GR2P-0E631B9C</span>
            </div>
            <div class="detail-row">
                <span><strong>👤 Player:</strong></span>
                <span>vikrant</span>
            </div>
            <div class="detail-row">
                <span><strong>🏟️ Venue:</strong></span>
                <span>RPM BOX CRICKET/FOOTBALL BOX</span>
            </div>
            <div class="detail-row">
                <span><strong>🏈 Sport & Court:</strong></span>
                <span>Box Football - Court 1 BoxFootball</span>
            </div>
            <div class="detail-row">
                <span><strong>📅 Date:</strong></span>
                <span>Sunday, July 6, 2025</span>
            </div>
            <div class="detail-row">
                <span><strong>⏰ Time:</strong></span>
                <span>6:30 AM - 7:30 AM</span>
            </div>
            <div class="detail-row">
                <span><strong>💰 Amount:</strong></span>
                <span>₹1300.00 PAID</span>
            </div>
            <div class="detail-row">
                <span><strong>📞 Contact:</strong></span>
                <span>+918448177459</span>
            </div>
        </div>

        <h3>📅 Add to Your Calendar</h3>
        <p>Click any button below to add this booking directly to your calendar:</p>

        <div class="calendar-buttons">
            <a href="#" class="calendar-btn google" onclick="openGoogleCalendar()">
                📅 Google Calendar
            </a>
            <a href="#" class="calendar-btn outlook" onclick="openOutlook()">
                📅 Outlook
            </a>
            <a href="#" class="calendar-btn yahoo" onclick="openYahoo()">
                📅 Yahoo Calendar
            </a>
            <a href="#" class="calendar-btn office365" onclick="openOffice365()">
                📅 Office 365
            </a>
        </div>

        <button class="calendar-btn download" onclick="downloadICS()" style="width: 100%;">
            📥 Download Calendar File (.ics)
        </button>

        <div style="text-align: center; margin-top: 15px; color: #6b7280; font-size: 14px;">
            ⏰ Includes automatic reminders: 1 hour and 15 minutes before your booking
        </div>

        <details style="margin-top: 30px;">
            <summary style="cursor: pointer; font-weight: bold; color: #059669;">🔍 View .ics File Content (Technical Details)</summary>
            <div class="ics-content" id="icsContent"></div>
        </details>
    </div>

    <script>
        // Booking data
        const booking = {
            booking_reference: "GR2P-0E631B9C",
            guest_name: "vikrant",
            guest_phone: "+918448177459",
            venue_name: "RPM BOX CRICKET/FOOTBALL BOX",
            venue_location: "Dilshad Garden, St.Lawrence Public School, Dilshad Garden, New Delhi, 110095",
            court_name: "Court 1 BoxFootball",
            sport_name: "Box Football",
            booking_date: "2025-07-06",
            start_time: "06:30:00",
            end_time: "07:30:00",
            total_price: "1300.00",
            payment_reference: "pay_QphsNTfFPkC1Ra"
        };

        // Generate calendar URLs
        function generateCalendarUrls() {
            const title = encodeURIComponent(`${booking.sport_name} Booking - ${booking.venue_name}`);
            const location = encodeURIComponent(`${booking.venue_name}, ${booking.venue_location}`);
            const details = encodeURIComponent([
                `🏈 ${booking.sport_name} at ${booking.court_name}`,
                `🎫 Booking: ${booking.booking_reference}`,
                `👤 Player: ${booking.guest_name}`,
                `📞 Contact: ${booking.guest_phone}`,
                `💰 Amount: ₹${booking.total_price}`,
                `💳 Payment ID: ${booking.payment_reference}`,
                ``,
                `⚡ Powered by Grid२Play - India's Premier Sports Booking Platform`
            ].join('\n'));

            // Convert to ISO format for URLs
            const startISO = new Date(`${booking.booking_date}T${booking.start_time}+05:30`).toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
            const endISO = new Date(`${booking.booking_date}T${booking.end_time}+05:30`).toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

            return {
                google: `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startISO}/${endISO}&location=${location}&details=${details}`,
                outlook: `https://outlook.live.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startISO}&enddt=${endISO}&location=${location}&body=${details}`,
                yahoo: `https://calendar.yahoo.com/?v=60&view=d&type=20&title=${title}&st=${startISO}&et=${endISO}&in_loc=${location}&desc=${details}`,
                office365: `https://outlook.office.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startISO}&enddt=${endISO}&location=${location}&body=${details}`
            };
        }

        // Generate .ics file content
        function generateICSContent() {
            const startDateTime = booking.booking_date.replace(/-/g, '') + 'T' + booking.start_time.replace(/:/g, '').substring(0, 6);
            const endDateTime = booking.booking_date.replace(/-/g, '') + 'T' + booking.end_time.replace(/:/g, '').substring(0, 6);
            const createdDateTime = new Date().toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

            const description = [
                `🏈 ${booking.sport_name} Booking at ${booking.venue_name}`,
                `🎫 Booking Reference: ${booking.booking_reference}`,
                `👤 Player: ${booking.guest_name}`,
                `📞 Contact: ${booking.guest_phone}`,
                `🏟️ Court: ${booking.court_name}`,
                `💰 Amount Paid: ₹${booking.total_price}`,
                `💳 Payment ID: ${booking.payment_reference}`,
                ``,
                `📍 Venue Address:`,
                `${booking.venue_location}`,
                ``,
                `⚡ Powered by Grid२Play - India's Premier Sports Booking Platform`
            ].join('\\n');

            return [
                'BEGIN:VCALENDAR',
                'VERSION:2.0',
                'PRODID:-//Grid2Play//Sports Booking Calendar//EN',
                'CALSCALE:GREGORIAN',
                'METHOD:PUBLISH',
                'BEGIN:VEVENT',
                `UID:${booking.booking_reference}@grid2play.com`,
                `DTSTAMP:${createdDateTime}`,
                `DTSTART;TZID=Asia/Kolkata:${startDateTime}`,
                `DTEND;TZID=Asia/Kolkata:${endDateTime}`,
                `SUMMARY:${booking.sport_name} Booking - ${booking.venue_name}`,
                `DESCRIPTION:${description}`,
                `LOCATION:${booking.venue_name}, ${booking.venue_location}`,
                `STATUS:CONFIRMED`,
                `TRANSP:OPAQUE`,
                `CATEGORIES:SPORTS,BOOKING,GRID2PLAY`,
                'BEGIN:VALARM',
                'TRIGGER:-PT1H',
                'ACTION:DISPLAY',
                'DESCRIPTION:Your sports booking starts in 1 hour!',
                'END:VALARM',
                'BEGIN:VALARM',
                'TRIGGER:-PT15M',
                'ACTION:DISPLAY',
                'DESCRIPTION:Your sports booking starts in 15 minutes!',
                'END:VALARM',
                'END:VEVENT',
                'END:VCALENDAR'
            ].join('\r\n');
        }

        // Calendar platform functions
        function openGoogleCalendar() {
            const urls = generateCalendarUrls();
            window.open(urls.google, '_blank');
        }

        function openOutlook() {
            const urls = generateCalendarUrls();
            window.open(urls.outlook, '_blank');
        }

        function openYahoo() {
            const urls = generateCalendarUrls();
            window.open(urls.yahoo, '_blank');
        }

        function openOffice365() {
            const urls = generateCalendarUrls();
            window.open(urls.office365, '_blank');
        }

        function downloadICS() {
            const icsContent = generateICSContent();
            const blob = new Blob([icsContent], { type: 'text/calendar' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${booking.booking_reference}.ics`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        }

        // Show .ics content when details are opened
        document.querySelector('details').addEventListener('toggle', function(e) {
            if (e.target.open) {
                document.getElementById('icsContent').textContent = generateICSContent();
            }
        });
    </script>
</body>
</html>

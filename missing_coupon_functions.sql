-- Missing RPC Function for AdminHome.tsx and AdminHome_Mobile.tsx
-- This function is critical for coupon tracking in revenue reports

CREATE OR REPLACE FUNCTION get_bookings_with_coupons(
    start_date DATE,
    end_date DATE
)
RETURNS TABLE (
    id UUID,
    booking_date DATE,
    start_time TIME,
    end_time TIME,
    total_price NUMERIC,
    payment_method TEXT,
    status booking_status,
    created_at TIMESTAMPTZ,
    guest_name TEXT,
    court_data JSONB,
    coupon_data JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    b.id,
    b.booking_date,
    b.start_time,
    b.end_time,
    b.total_price,
    b.payment_method,
    b.status,
    b.created_at,
    b.guest_name,
    -- Court data as JSONB
    jsonb_build_object(
      'id', c.id,
      'name', c.name,
      'venue', jsonb_build_object(
        'id', v.id,
        'name', v.name,
        'platform_fee_percentage', v.platform_fee_percentage
      ),
      'sport', jsonb_build_object(
        'id', s.id,
        'name', s.name
      )
    ) as court_data,
    -- Coupon data as J<PERSON><PERSON><PERSON> array
    CASE 
      WHEN cu.id IS NOT NULL THEN 
        jsonb_build_array(
          jsonb_build_object(
            'original_price', cu.original_price,
            'discount_applied', cu.discount_applied,
            'final_price', cu.final_price,
            'coupon', jsonb_build_object('code', cp.code)
          )
        )
      ELSE NULL
    END as coupon_data
  FROM bookings b
  LEFT JOIN courts c ON b.court_id = c.id
  LEFT JOIN venues v ON c.venue_id = v.id
  LEFT JOIN sports s ON c.sport_id = s.id
  LEFT JOIN coupon_usage cu ON b.id = cu.booking_id
  LEFT JOIN coupons cp ON cu.coupon_id = cp.id
  WHERE b.booking_date >= start_date
    AND b.booking_date <= end_date
    AND b.status IN ('confirmed', 'completed')
  ORDER BY b.booking_date DESC, b.start_time DESC;
END;
$$;

-- Additional coupon system functions that might be missing

CREATE OR REPLACE FUNCTION validate_and_apply_coupon(
    p_coupon_code TEXT,
    p_venue_id UUID,
    p_original_price NUMERIC,
    p_user_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_coupon_id UUID;
    v_discount_type TEXT;
    v_discount_value NUMERIC;
    v_max_uses INTEGER;
    v_current_uses INTEGER;
    v_valid_until DATE;
    v_venue_specific BOOLEAN;
    v_is_public BOOLEAN;
    v_final_price NUMERIC;
    v_discount_amount NUMERIC;
BEGIN
    -- Check if coupon exists and is active
    SELECT id, discount_type, discount_value, max_uses, valid_until, 
           venue_id IS NOT NULL, is_public
    INTO v_coupon_id, v_discount_type, v_discount_value, v_max_uses, 
         v_valid_until, v_venue_specific, v_is_public
    FROM coupons 
    WHERE code = p_coupon_code AND is_active = true;
    
    IF v_coupon_id IS NULL THEN
        RETURN json_build_object(
            'valid', false,
            'error', 'Invalid coupon code'
        );
    END IF;
    
    -- Check expiry
    IF v_valid_until IS NOT NULL AND v_valid_until < CURRENT_DATE THEN
        RETURN json_build_object(
            'valid', false,
            'error', 'Coupon has expired'
        );
    END IF;
    
    -- Check venue restriction
    IF v_venue_specific AND NOT EXISTS(
        SELECT 1 FROM coupons WHERE id = v_coupon_id AND venue_id = p_venue_id
    ) THEN
        RETURN json_build_object(
            'valid', false,
            'error', 'Coupon not valid for this venue'
        );
    END IF;
    
    -- Check usage limit
    IF v_max_uses IS NOT NULL THEN
        SELECT COUNT(*) INTO v_current_uses
        FROM coupon_usage WHERE coupon_id = v_coupon_id;
        
        IF v_current_uses >= v_max_uses THEN
            RETURN json_build_object(
                'valid', false,
                'error', 'Coupon usage limit reached'
            );
        END IF;
    END IF;
    
    -- Calculate discount
    IF v_discount_type = 'percentage' THEN
        v_discount_amount := p_original_price * (v_discount_value / 100);
    ELSE
        v_discount_amount := v_discount_value;
    END IF;
    
    -- Ensure discount doesn't exceed original price
    IF v_discount_amount > p_original_price THEN
        v_discount_amount := p_original_price;
    END IF;
    
    v_final_price := p_original_price - v_discount_amount;
    
    RETURN json_build_object(
        'valid', true,
        'coupon_id', v_coupon_id,
        'discount_amount', v_discount_amount,
        'final_price', v_final_price,
        'original_price', p_original_price
    );
END;
$$;

-- Price validation function to prevent price manipulation
CREATE OR REPLACE FUNCTION validate_booking_price(
    p_court_id UUID,
    p_start_time TIME,
    p_end_time TIME,
    p_booking_date DATE,
    p_submitted_price NUMERIC
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_expected_price NUMERIC := 0;
    v_day_of_week INTEGER;
    v_slot_price NUMERIC;
    v_slot_record RECORD;
BEGIN
    -- Get day of week (0 = Sunday, 1 = Monday, etc.)
    v_day_of_week := EXTRACT(DOW FROM p_booking_date);

    -- Calculate expected price from template_slots
    FOR v_slot_record IN
        SELECT price
        FROM template_slots
        WHERE court_id = p_court_id
        AND day_of_week = v_day_of_week
        AND start_time >= p_start_time
        AND end_time <= p_end_time
        AND is_available = true
    LOOP
        v_expected_price := v_expected_price + v_slot_record.price;
    END LOOP;

    -- Allow small rounding differences (within 1 rupee)
    RETURN ABS(v_expected_price - p_submitted_price) <= 1.0;
END;
$$;

CREATE OR REPLACE FUNCTION create_booking_with_coupon(
    p_court_id UUID,
    p_user_id UUID,
    p_booking_date DATE,
    p_start_time TIME,
    p_end_time TIME,
    p_original_price NUMERIC,
    p_coupon_code TEXT DEFAULT NULL,
    p_guest_name TEXT DEFAULT NULL,
    p_guest_phone TEXT DEFAULT NULL,
    p_payment_reference TEXT DEFAULT NULL,
    p_payment_status TEXT DEFAULT 'pending',
    p_payment_method TEXT DEFAULT 'online'
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_booking_id UUID;
    v_coupon_validation JSON;
    v_final_price NUMERIC;
    v_coupon_id UUID;
    v_price_valid BOOLEAN;
BEGIN
    -- Validate submitted price against template_slots
    SELECT validate_booking_price(
        p_court_id,
        p_start_time,
        p_end_time,
        p_booking_date,
        p_original_price
    ) INTO v_price_valid;

    IF NOT v_price_valid THEN
        RETURN json_build_object(
            'success', false,
            'error', 'SECURITY VIOLATION: Price mismatch detected - security violation'
        );
    END IF;

    -- Validate coupon if provided
    IF p_coupon_code IS NOT NULL AND p_coupon_code != '' THEN
        SELECT validate_and_apply_coupon(
            p_coupon_code,
            (SELECT venue_id FROM courts WHERE id = p_court_id),
            p_original_price,
            p_user_id
        ) INTO v_coupon_validation;

        IF NOT (v_coupon_validation->>'valid')::boolean THEN
            RETURN json_build_object(
                'success', false,
                'error', v_coupon_validation->>'error'
            );
        END IF;

        v_final_price := (v_coupon_validation->>'final_price')::numeric;
        v_coupon_id := (v_coupon_validation->>'coupon_id')::uuid;
    ELSE
        v_final_price := p_original_price;
    END IF;

    -- Create booking
    INSERT INTO bookings (
        court_id, user_id, booking_date, start_time, end_time,
        total_price, guest_name, guest_phone, status,
        payment_reference, payment_status, payment_method
    ) VALUES (
        p_court_id, p_user_id, p_booking_date, p_start_time, p_end_time,
        v_final_price, p_guest_name, p_guest_phone, 'confirmed',
        p_payment_reference, p_payment_status, p_payment_method
    ) RETURNING id INTO v_booking_id;

    -- Record coupon usage if coupon was applied
    IF v_coupon_id IS NOT NULL THEN
        INSERT INTO coupon_usage (
            coupon_id, booking_id, user_id, original_price,
            discount_applied, final_price
        ) VALUES (
            v_coupon_id, v_booking_id, p_user_id, p_original_price,
            (v_coupon_validation->>'discount_amount')::numeric, v_final_price
        );
    END IF;

    RETURN json_build_object(
        'success', true,
        'booking_id', v_booking_id,
        'final_price', v_final_price
    );
END;
$$;

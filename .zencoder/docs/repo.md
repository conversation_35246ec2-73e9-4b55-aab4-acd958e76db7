# Repository Information

## Testing Framework
- **E2E Testing Framework**: Playwright
- **Unit Testing Framework**: Vitest
- **Test Runner**: Playwright Test

## Project Overview
This is a React/TypeScript application for sports venue booking with Vite as the build tool. The application includes features for venue discovery, booking management, user authentication, and tournament systems.

## Architecture
- **Frontend**: React + TypeScript + Vite
- **Backend**: Supabase
- **UI Components**: Radix UI + Tailwind CSS
- **State Management**: React Query
- **Authentication**: Supabase Auth

## E2E Test Scenarios
- **User Registration and Login Flow**: Phone number (SMS OTP) → Verify SMS OTP → Login with phone/OTP → Access homepage
# Grid2Play Daily Venue Report - ViaSocket Automation Guide

## 🎯 Overview
This guide provides step-by-step instructions to set up ViaSocket automation for sending daily venue performance reports to venue administrators via MSG91 WhatsApp.

## 📋 Prerequisites
- ✅ ViaSocket account with PostgreSQL integration
- ✅ ViaSocket account with MSG91 integration  
- ✅ MSG91 template `daily_venue_report` approved (En_US)
- ✅ Database function `get_venue_daily_summary()` deployed

## 🔧 ViaSocket Workflow Configuration

### Step 1: Create New Workflow
1. **Login to ViaSocket Dashboard**
2. **Create New Workflow**: "Grid2Play Daily Venue Reports"
3. **Set Workflow Type**: Scheduled Automation
4. **Schedule**: Daily at 11:00 PM IST (23:00 Asia/Kolkata)

### Step 2: Configure PostgreSQL Trigger
**Trigger Configuration:**
- **App**: PostgreSQL
- **Event**: Run SQL Query (Scheduled)
- **Connection**: Your Grid2Play PostgreSQL database
- **Schedule**: Daily at 23:00 IST

**SQL Query:**
```sql
SELECT 
  venue_id,
  venue_name,
  admin_phone,
  admin_name,
  report_date,
  total_bookings,
  confirmed_bookings,
  cancelled_bookings,
  gross_revenue,
  net_revenue,
  coupon_usage_count
FROM get_venue_daily_summary(CURRENT_DATE);
```

**Expected Output Format:**
```json
[
  {
    "venue_id": "uuid-here",
    "venue_name": "SportZone Arena",
    "admin_phone": "9876543210",
    "admin_name": "John Admin",
    "report_date": "04/07/2025",
    "total_bookings": "12",
    "confirmed_bookings": "10", 
    "cancelled_bookings": "2",
    "gross_revenue": "₹5,250",
    "net_revenue": "₹4,987",
    "coupon_usage_count": "3"
  }
]
```

### Step 3: Configure Data Processing (Optional)
**Add Data Processor (if needed):**
- **Purpose**: Format phone numbers to international format
- **Logic**: Ensure phone numbers start with +91

**Phone Number Formatting:**
```javascript
// ViaSocket JavaScript processor
const formatPhone = (phone) => {
  if (!phone) return null;
  let formatted = phone.toString().trim();
  if (formatted.startsWith('0')) {
    formatted = '+91' + formatted.substring(1);
  } else if (!formatted.startsWith('+91')) {
    formatted = '+91' + formatted;
  }
  return formatted;
};

// Apply to each record
output = input.map(record => ({
  ...record,
  admin_phone: formatPhone(record.admin_phone)
}));
```

### Step 4: Configure MSG91 WhatsApp Action
**Action Configuration:**
- **App**: MSG91
- **Action**: Send WhatsApp Template Message
- **Template**: `daily_venue_report`
- **Language**: `en_US`

**MSG91 Configuration:**
```json
{
  "integrated_number": "919211848599",
  "content_type": "template",
  "payload": {
    "messaging_product": "whatsapp",
    "type": "template",
    "template": {
      "name": "daily_venue_report",
      "language": {
        "code": "en_US",
        "policy": "deterministic"
      },
      "namespace": "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
      "to_and_components": [
        {
          "to": ["{{admin_phone}}"],
          "components": {
            "header_1": {
              "type": "text",
              "value": "{{venue_name}}"
            },
            "body_1": {
              "type": "text", 
              "value": "{{venue_name}}"
            },
            "body_2": {
              "type": "text",
              "value": "{{report_date}}"
            },
            "body_3": {
              "type": "text",
              "value": "{{total_bookings}}"
            },
            "body_4": {
              "type": "text",
              "value": "{{confirmed_bookings}}"
            },
            "body_5": {
              "type": "text",
              "value": "{{cancelled_bookings}}"
            },
            "body_6": {
              "type": "text",
              "value": "{{gross_revenue}}"
            },
            "body_7": {
              "type": "text",
              "value": "{{net_revenue}}"
            },
            "body_8": {
              "type": "text",
              "value": "{{coupon_usage_count}}"
            }
          }
        }
      ]
    }
  }
}
```

### Step 5: Configure Loop/Iterator
**For Multiple Venues:**
- **Add Iterator**: Loop through PostgreSQL results
- **Iterator Input**: PostgreSQL query results array
- **Action per Item**: Send MSG91 WhatsApp message

**Iterator Configuration:**
- **Input Array**: `{{postgresql_results}}`
- **Current Item Variable**: `{{current_venue}}`
- **Delay Between Items**: 2 seconds (to avoid rate limiting)

## 🔍 Variable Mapping Reference

| ViaSocket Variable | Database Column | MSG91 Template Variable | Example Value |
|-------------------|-----------------|------------------------|---------------|
| `{{venue_name}}` | `venue_name` | `header_1`, `body_1` | "SportZone Arena" |
| `{{report_date}}` | `report_date` | `body_2` | "04/07/2025" |
| `{{total_bookings}}` | `total_bookings` | `body_3` | "12" |
| `{{confirmed_bookings}}` | `confirmed_bookings` | `body_4` | "10" |
| `{{cancelled_bookings}}` | `cancelled_bookings` | `body_5` | "2" |
| `{{gross_revenue}}` | `gross_revenue` | `body_6` | "₹5,250" |
| `{{net_revenue}}` | `net_revenue` | `body_7` | "₹4,987" |
| `{{coupon_usage_count}}` | `coupon_usage_count` | `body_8` | "3" |
| `{{admin_phone}}` | `admin_phone` | `to` array | "+919876543210" |

## ⚙️ Error Handling & Monitoring

### Error Handling Configuration:
1. **Database Connection Errors**: Retry 3 times with 5-minute intervals
2. **MSG91 API Errors**: Log error and continue with next venue
3. **Invalid Phone Numbers**: Skip and log for manual review
4. **Empty Results**: Send notification to super admin

### Monitoring Setup:
1. **Success Metrics**: Track successful message deliveries
2. **Error Logging**: Log all failures with venue details
3. **Daily Summary**: Send automation summary to super admin
4. **Rate Limiting**: Implement 2-second delays between messages

## 🧪 Testing Checklist

### Pre-Deployment Testing:
- [ ] Database function returns correct data format
- [ ] Phone numbers are properly formatted (+91 prefix)
- [ ] MSG91 template variables map correctly
- [ ] Revenue formatting includes ₹ symbol
- [ ] Date format is DD/MM/YYYY
- [ ] Only venues with activity get reports
- [ ] Only verified admin phone numbers are used

### Test Data Example:
```sql
-- Test the database function
SELECT * FROM get_venue_daily_summary('2025-01-04');
```

## 🚀 Deployment Steps

1. **Deploy Database Function**: Execute `venue-daily-summary-function.sql`
2. **Create ViaSocket Workflow**: Follow configuration steps above
3. **Test with Sample Data**: Run workflow manually first
4. **Enable Scheduled Execution**: Set daily 11 PM IST schedule
5. **Monitor First Week**: Check logs and delivery reports
6. **Optimize Based on Feedback**: Adjust timing or content as needed

## 📞 Support & Troubleshooting

### Common Issues:
- **No data returned**: Check if venues have bookings for the date
- **Phone format errors**: Verify +91 prefix and 10-digit numbers
- **MSG91 delivery failures**: Check template approval status
- **Database timeout**: Optimize query performance with indexes

### Contact Information:
- **Database Issues**: Check Supabase logs
- **MSG91 Issues**: Verify authkey and template status
- **ViaSocket Issues**: Check workflow execution logs

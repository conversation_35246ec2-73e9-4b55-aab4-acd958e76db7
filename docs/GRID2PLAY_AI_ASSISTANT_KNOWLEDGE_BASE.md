# Grid२Play AI Assistant Knowledge Base
*Complete Technical & Business Documentation for AI Development Collaboration*

## 🎯 BUSINESS OVERVIEW

### Company Mission & Vision
**Mission**: Democratize sports facility access across India by connecting players with quality venues through technology-driven booking solutions.

**Vision**: Become India's leading sports community platform, fostering active lifestyles through seamless venue discovery, booking, and tournament experiences.

### Core Value Proposition
- **For Players**: Instant venue discovery, transparent pricing, secure bookings, and community tournaments
- **For Venue Owners**: Automated booking management, revenue optimization, and comprehensive analytics
- **For Sports Communities**: Tournament hosting, team formation, and competitive play organization

### Target Market Analysis
**Primary Market**: Urban India (Tier 1 & 2 cities)
- **Demographics**: 18-45 years, middle to upper-middle class
- **Sports Focus**: Cricket, Football, Badminton, Tennis, Basketball
- **User Base**: 90% mobile users, primarily Android

**Market Size**: ₹2,000+ crore Indian sports facility booking market
**Growth Rate**: 25% YoY in urban sports facility usage

### Competitive Positioning

#### vs Playo (Market Leader)
**Grid२Play Advantages**:
- Tournament system (unique USP)
- WhatsApp-based venue communication
- Transparent settlement system
- AI-powered booking assistance
- Better mobile-first design

#### vs Hudle.in
**Grid२Play Advantages**:
- More comprehensive venue management
- Advanced analytics dashboard
- Automated daily reporting
- Superior payment processing
- Enhanced security features

### Revenue Model
1. **Platform Commission**: 5% on online bookings
2. **Tournament Fees**: Registration and hosting fees
3. **Premium Venue Features**: Enhanced listings and analytics
4. **Advertisement Revenue**: Sponsored venue placements

## 🏗️ TECHNICAL ARCHITECTURE

### System Architecture Overview
```
Frontend (React/TypeScript) → Supabase (PostgreSQL + Edge Functions) → Third-party APIs
├── Authentication: Supabase Auth
├── Database: PostgreSQL with RLS
├── Real-time: Supabase Realtime
├── Storage: Supabase Storage
├── Business Logic: Edge Functions
└── Integrations: Razorpay, MSG91, OpenAI
```

### Core Technology Stack
- **Frontend**: React 18, TypeScript, Tailwind CSS, Vite
- **Backend**: Supabase (PostgreSQL, Edge Functions)
- **Authentication**: Supabase Auth with RLS
- **Payments**: Razorpay integration
- **Communication**: MSG91 (WhatsApp, SMS, Email)
- **AI**: OpenAI GPT-4 for chat assistant
- **Deployment**: Lovable platform

### Database Schema (Key Tables)

#### Core Business Tables
```sql
-- Users and Authentication
users (auth.users) - Supabase managed
user_profiles - Extended user information

-- Venues and Sports
venues - Sports facility information
sports - Available sports types
courts - Individual playing areas
template_slots - Pricing and availability templates

-- Bookings and Payments
bookings - Core booking records
booking_slots - Individual time slot bookings
payments - Payment transaction records
razorpay_orders - Razorpay integration data

-- Tournament System (Key USP)
tournaments - Tournament definitions
tournament_registrations - User/team registrations
tournament_brackets - Competition structure

-- Revenue and Settlement
daily_earnings - Daily venue revenue tracking
settlements - Weekly venue payouts
coupon_usage - Discount tracking

-- Administrative
venue_admins - Venue management permissions
automation_logs - System automation tracking
security_logs - Security event monitoring
```

#### Row Level Security (RLS) Patterns
- **User Data**: Users can only access their own records
- **Venue Data**: Venue admins can only manage their venues
- **Booking Data**: Users see their bookings, venue admins see venue bookings
- **Financial Data**: Restricted to venue admins and super admins

### Edge Functions Architecture

#### Core Business Functions
- `chat-assistant` - AI-powered booking assistance
- `send-daily-revenue-reports` - Automated WhatsApp reporting
- `create-razorpay-order` - Secure payment processing
- `validate-booking-price-security` - Anti-tampering protection

#### Function Patterns
```typescript
// Standard Edge Function structure
export default async function handler(req: Request) {
  // 1. Authentication validation
  // 2. Input validation and sanitization
  // 3. Business logic execution
  // 4. Response formatting
  // 5. Error handling and logging
}
```

### Third-Party Integrations

#### Razorpay Payment Processing
- **Purpose**: Secure online payment handling
- **Integration**: Server-side order creation, client-side payment UI
- **Security**: Server-side validation, webhook verification

#### MSG91 Communication Platform
- **WhatsApp Business**: Daily revenue reports, booking confirmations
- **Email**: Transactional emails, notifications
- **SMS**: OTP verification, urgent notifications
- **Chat Widget**: Customer support integration

#### OpenAI Integration
- **Model**: GPT-4 for intelligent responses
- **Functions**: Court search, availability checking, booking guidance
- **Context**: Real-time venue and booking data access

## 🚀 CORE FEATURES & FUNCTIONALITY

### Venue Discovery & Booking System

#### Search & Discovery
- **Location-based search**: Geolocation and manual location input
- **Sport-specific filtering**: Cricket, Football, Badminton, etc.
- **Availability filtering**: Real-time slot availability
- **Price range filtering**: Budget-based venue selection

#### Booking Flow
```typescript
// Booking process stages
1. Venue Selection → 2. Court Selection → 3. Slot Selection → 
4. Price Calculation → 5. Payment Processing → 6. Confirmation
```

#### Key Components
- `VenueCard` - Venue display with ratings and pricing
- `BookingPage` - Complete booking interface
- `SlotSelector` - Time slot selection with real-time availability
- `PaymentProcessor` - Razorpay integration component

### Tournament Management System (Key USP)

#### Tournament Features
- **Tournament Creation**: Venue-hosted tournament setup
- **Registration System**: Individual and team registrations
- **Bracket Management**: Automated tournament brackets
- **Prize Distribution**: Winner tracking and rewards

#### Business Value
- **Differentiation**: Unique feature vs competitors
- **Community Building**: Regular player engagement
- **Revenue Generation**: Tournament fees and increased bookings
- **Venue Loyalty**: Recurring tournament hosting

### Payment Processing & Settlement

#### Online Payment Flow
```typescript
// Payment processing stages
1. Price Calculation → 2. Razorpay Order Creation → 
3. Payment UI → 4. Payment Verification → 5. Booking Confirmation
```

#### Settlement System
- **Weekly Settlements**: Automated venue payouts
- **Revenue Separation**: Online (settlement) vs Offline (informational)
- **Platform Fee**: 5% commission on online bookings
- **Transparency**: Detailed revenue breakdowns

### Admin Dashboard & Management

#### Venue Admin Features
- **Earnings Dashboard**: Daily/weekly revenue tracking
- **Booking Management**: View and manage venue bookings
- **Settlement Tracking**: Payment history and pending settlements
- **Analytics**: Booking patterns and revenue insights

#### Super Admin Features
- **System Monitoring**: Overall platform health
- **Venue Management**: Venue approval and configuration
- **User Management**: User support and account management
- **Financial Oversight**: Platform-wide financial tracking

### WhatsApp Reporting Automation

#### Daily Revenue Reports
- **Schedule**: 12:00:01 AM IST daily
- **Recipients**: All venue admins
- **Content**: Online vs offline revenue separation
- **Template**: 15-variable MSG91 template

#### Report Structure
```
🏟️ Grid२Play Daily Report
📅 [Date] - [Venue Name]
📊 BOOKING SUMMARY
💰 ONLINE REVENUE (Settlement)
💳 SETTLEMENT SUMMARY
ℹ️ Cash Bookings (Info Only)
```

### AI Chat Assistant

#### Capabilities
- **Real-time Data Access**: Live venue and booking information
- **Booking Guidance**: Step-by-step booking assistance
- **FAQ Integration**: Automated common question responses
- **Escalation**: Seamless handoff to human support

#### Function Calling
- `search_courts_by_sport()` - Find venues by sport type
- `get_unified_availability()` - Real-time slot availability
- `prepareBookingGuidance()` - Booking process assistance

## 💻 DEVELOPMENT CONTEXT

### Codebase Structure
```
src/
├── components/          # Reusable UI components
│   ├── admin/          # Admin-specific components
│   ├── booking/        # Booking flow components
│   └── ui/             # Base UI components
├── pages/              # Main application pages
├── context/            # React context providers
├── hooks/              # Custom React hooks
├── services/           # API and business logic
├── utils/              # Utility functions
└── integrations/       # Third-party integrations

supabase/
├── functions/          # Edge Functions
├── migrations/         # Database migrations
└── seed.sql           # Initial data setup
```

### Key Files & Components

#### Core Pages
- `src/pages/VenueDetails.tsx` - Venue information and booking
- `src/pages/BookingPage.tsx` - Complete booking interface
- `src/pages/admin/EarningsDashboard.tsx` - Admin revenue dashboard
- `src/pages/TournamentDetails.tsx` - Tournament information

#### Critical Components
- `src/components/booking/BookSlotModal.tsx` - Booking modal interface
- `src/components/admin/SettlementManagementModal.tsx` - Settlement management
- `src/components/EnhancedMSG91ChatWidget/` - AI chat assistant

#### Business Logic
- `src/services/bookingService.ts` - Booking operations
- `src/services/paymentService.ts` - Payment processing
- `src/services/userContextService.ts` - User data management

### Development Patterns

#### Component Architecture
```typescript
// Standard component pattern
interface ComponentProps {
  // Typed props
}

const Component: React.FC<ComponentProps> = ({ props }) => {
  // Hooks and state
  // Business logic
  // Event handlers
  // Render JSX
};
```

#### API Integration Pattern
```typescript
// Supabase query pattern
const { data, error } = await supabase
  .from('table_name')
  .select('columns')
  .eq('filter_column', value)
  .single();

if (error) {
  console.error('Error:', error);
  toast.error('Operation failed');
  return;
}
```

#### Error Handling
- **User-facing**: Toast notifications for user actions
- **Development**: Console logging for debugging
- **Production**: Security logging for audit trails

### Recent Enhancements

#### Security Improvements
- **Price Validation**: Server-side price tampering prevention
- **Security Logging**: Comprehensive audit trail system
- **Rate Limiting**: API abuse prevention
- **Input Sanitization**: XSS and injection prevention

#### Feature Additions
- **AI Chat Assistant**: OpenAI-powered booking assistance
- **Enhanced MSG91 Widget**: Comprehensive support system
- **Tournament System**: Complete tournament management
- **Settlement Automation**: Automated venue payouts

#### Performance Optimizations
- **Real-time Updates**: Supabase realtime subscriptions
- **Caching Strategy**: Optimized data fetching
- **Mobile Optimization**: 90% mobile user base focus

## 🔧 OPERATIONAL SYSTEMS

### Daily Revenue Reporting

#### Automation Flow
```typescript
// Daily automation sequence
1. Cron Trigger (6:30 PM UTC = 12:00 AM IST)
2. Data Validation (validate_daily_earnings_before_reports)
3. Data Fetch (get_venue_daily_earnings_detailed)
4. WhatsApp Send (MSG91 API)
5. Logging (automation_logs table)
```

#### Data Integrity Protection
- **Auto-validation**: Detects missing/inconsistent data
- **Auto-correction**: Recalculates daily earnings
- **Audit Trail**: Complete operation logging

### Settlement Management

#### Weekly Settlement Process
- **Calculation**: Automated revenue calculation
- **Validation**: Multi-level verification
- **Processing**: Bank transfer initiation
- **Tracking**: Status monitoring and updates

#### Revenue Separation Logic
```typescript
// Online vs Offline revenue handling
Online Revenue (Settlement):
- Platform processes payment
- 5% platform fee deducted
- Net amount settled to venue

Offline Revenue (Informational):
- Cash payments at venue
- No platform fee
- Informational tracking only
```

### Security & Audit Systems

#### Security Event Types
- **Authentication**: Login attempts, password changes
- **Authorization**: Access violations, permission changes
- **Payment**: Transaction anomalies, fraud attempts
- **Data**: Unauthorized access, data modifications

#### Monitoring & Alerts
- **Real-time Alerts**: Critical security events
- **Daily Summaries**: Security event reports
- **Compliance Reporting**: Audit trail generation

## 🚀 ENHANCEMENT OPPORTUNITIES

### AI-Assisted Development Areas

#### 1. Predictive Analytics
- **Booking Patterns**: Predict peak booking times
- **Revenue Forecasting**: Venue revenue predictions
- **User Behavior**: Personalized recommendations
- **Demand Planning**: Optimal pricing strategies

#### 2. Enhanced User Experience
- **Smart Recommendations**: AI-powered venue suggestions
- **Dynamic Pricing**: Real-time price optimization
- **Personalization**: Customized user interfaces
- **Predictive Search**: Intelligent search suggestions

#### 3. Operational Automation
- **Customer Support**: Advanced chatbot capabilities
- **Content Generation**: Automated venue descriptions
- **Quality Assurance**: Automated testing and validation
- **Performance Optimization**: Code optimization suggestions

### Feature Roadmap

#### Phase 1 (Next 3 months)
- **Enhanced Tournament System**: Advanced bracket management
- **Mobile App**: Native iOS/Android applications
- **Advanced Analytics**: Comprehensive reporting dashboard
- **API Ecosystem**: Third-party developer APIs

#### Phase 2 (3-6 months)
- **Multi-city Expansion**: Tier 2 city rollout
- **Corporate Bookings**: B2B booking solutions
- **Loyalty Program**: User reward system
- **Advanced AI**: Predictive booking assistance

#### Phase 3 (6-12 months)
- **Franchise Model**: Venue partner program
- **International Expansion**: Southeast Asia markets
- **IoT Integration**: Smart venue management
- **Blockchain**: Transparent tournament rewards

### Integration Possibilities

#### Sports Ecosystem
- **Fitness Tracking**: Wearable device integration
- **Sports Equipment**: E-commerce partnerships
- **Coaching Services**: Professional trainer network
- **Health Monitoring**: Wellness tracking integration

#### Business Ecosystem
- **Food Delivery**: Post-game meal ordering
- **Transportation**: Ride-sharing integration
- **Insurance**: Sports injury coverage
- **Social Media**: Enhanced sharing capabilities

### Scalability Considerations

#### Technical Scalability
- **Database Optimization**: Query performance improvements
- **Caching Strategy**: Redis implementation
- **CDN Integration**: Global content delivery
- **Microservices**: Service decomposition

#### Business Scalability
- **Multi-tenant Architecture**: Franchise support
- **Localization**: Regional customization
- **Payment Gateways**: Multiple payment options
- **Regulatory Compliance**: Multi-jurisdiction support

## 📋 DEVELOPMENT GUIDELINES FOR AI ASSISTANTS

### Code Quality Standards
- **TypeScript**: Strict typing for all components
- **Error Handling**: Comprehensive error management
- **Security**: Input validation and sanitization
- **Performance**: Optimized queries and rendering

### Business Logic Principles
- **User-Centric**: Always prioritize user experience
- **Transparency**: Clear pricing and policies
- **Security**: Protect user and financial data
- **Scalability**: Design for growth and expansion

### Integration Patterns
- **API-First**: Design APIs before implementations
- **Event-Driven**: Use real-time updates where appropriate
- **Modular**: Create reusable components and services
- **Testable**: Write testable and maintainable code

### Collaboration Guidelines
- **Documentation**: Maintain comprehensive documentation
- **Code Reviews**: Peer review all changes
- **Testing**: Automated and manual testing
- **Monitoring**: Track performance and errors

---

**This knowledge base serves as the foundation for AI-assisted development collaboration on Grid२Play. It provides the context needed to understand the platform's business objectives, technical architecture, and development patterns, enabling informed suggestions and contributions to the codebase.**

**Last Updated**: January 2025
**Version**: 1.0
**Maintained By**: Grid२Play Development Team
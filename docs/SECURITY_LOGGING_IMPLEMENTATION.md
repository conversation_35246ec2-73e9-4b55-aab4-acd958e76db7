# Grid2Play Security Logging Implementation Guide

## 1. Enhanced Security Logging Infrastructure

### 1.1 Enhanced Security Logs Table

```sql
-- Enhanced security_logs table with additional tracking fields
ALTER TABLE security_logs ADD COLUMN IF NOT EXISTS session_id TEXT;
ALTER TABLE security_logs ADD COLUMN IF NOT EXISTS request_id TEXT;
ALTER TABLE security_logs ADD COLUMN IF NOT EXISTS geographic_location JSONB;
ALTER TABLE security_logs ADD COLUMN IF NOT EXISTS device_fingerprint TEXT;
ALTER TABLE security_logs ADD COLUMN IF NOT EXISTS correlation_id TEXT;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_security_logs_session_id ON security_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_security_logs_request_id ON security_logs(request_id);
CREATE INDEX IF NOT EXISTS idx_security_logs_severity ON security_logs(severity);
CREATE INDEX IF NOT EXISTS idx_security_logs_correlation_id ON security_logs(correlation_id);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_security_logs_user_event_time ON security_logs(user_id, event_type, created_at);
```

### 1.2 Unified Security Logging Service

```typescript
// src/utils/unifiedSecurityLogger.ts
import { supabase } from '@/integrations/supabase/client';

export interface SecurityEventData {
  eventType: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  details: Record<string, any>;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  correlationId?: string;
}

export class UnifiedSecurityLogger {
  private static instance: UnifiedSecurityLogger;
  private sessionId: string;
  private deviceFingerprint: string;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.deviceFingerprint = this.generateDeviceFingerprint();
  }

  public static getInstance(): UnifiedSecurityLogger {
    if (!UnifiedSecurityLogger.instance) {
      UnifiedSecurityLogger.instance = new UnifiedSecurityLogger();
    }
    return UnifiedSecurityLogger.instance;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateDeviceFingerprint(): string {
    if (typeof window === 'undefined') return 'server';
    
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx?.fillText('Grid2Play', 10, 10);
    const canvasFingerprint = canvas.toDataURL();
    
    const fingerprint = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      canvas: canvasFingerprint.slice(-50) // Last 50 chars
    };
    
    return btoa(JSON.stringify(fingerprint)).slice(0, 100);
  }

  private async getGeographicLocation(): Promise<any> {
    try {
      // Use IP geolocation service (implement based on your preference)
      const response = await fetch('https://ipapi.co/json/');
      return await response.json();
    } catch (error) {
      return null;
    }
  }

  public async logSecurityEvent(eventData: SecurityEventData): Promise<void> {
    try {
      const enhancedDetails = {
        ...eventData.details,
        timestamp: new Date().toISOString(),
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
        url: typeof window !== 'undefined' ? window.location.href : 'server',
        referrer: typeof window !== 'undefined' ? document.referrer : null
      };

      const logEntry = {
        event_type: eventData.eventType,
        severity: eventData.severity,
        details: enhancedDetails,
        user_id: eventData.userId || null,
        session_id: eventData.sessionId || this.sessionId,
        request_id: eventData.requestId || this.generateRequestId(),
        correlation_id: eventData.correlationId || null,
        ip_address: await this.getClientIP(),
        user_agent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
        device_fingerprint: this.deviceFingerprint,
        geographic_location: await this.getGeographicLocation()
      };

      const { error } = await supabase
        .from('security_logs')
        .insert(logEntry);

      if (error) {
        console.error('Security logging failed:', error);
      }

      // Check if this event requires immediate notification
      await this.checkForImmediateNotification(eventData);

    } catch (error) {
      console.error('Security logging exception:', error);
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async getClientIP(): Promise<string | null> {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      return null;
    }
  }

  private async checkForImmediateNotification(eventData: SecurityEventData): Promise<void> {
    const criticalEvents = [
      'UNAUTHORIZED_ACCESS_ATTEMPT',
      'SUSPICIOUS_PAYMENT_PATTERN',
      'BULK_OPERATION_PERFORMED',
      'SUSPICIOUS_LOGIN_PATTERN'
    ];

    if (eventData.severity === 'CRITICAL' || criticalEvents.includes(eventData.eventType)) {
      await this.sendImmediateAlert(eventData);
    }
  }

  private async sendImmediateAlert(eventData: SecurityEventData): Promise<void> {
    try {
      await supabase.functions.invoke('send-security-alert', {
        body: {
          eventType: eventData.eventType,
          severity: eventData.severity,
          details: eventData.details,
          userId: eventData.userId,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to send security alert:', error);
    }
  }

  // Convenience methods for common event types
  public async logAuthenticationEvent(
    eventType: string,
    userId?: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    const severity = eventType.includes('FAILURE') || eventType.includes('UNAUTHORIZED') ? 'HIGH' : 'LOW';
    await this.logSecurityEvent({
      eventType,
      severity,
      details,
      userId
    });
  }

  public async logBookingEvent(
    eventType: string,
    userId: string,
    bookingDetails: Record<string, any>
  ): Promise<void> {
    const severity = eventType.includes('CONFLICT') ? 'HIGH' : 'LOW';
    await this.logSecurityEvent({
      eventType,
      severity,
      details: bookingDetails,
      userId
    });
  }

  public async logPaymentEvent(
    eventType: string,
    userId: string,
    paymentDetails: Record<string, any>
  ): Promise<void> {
    const severity = eventType.includes('FAILURE') || eventType.includes('SUSPICIOUS') ? 'HIGH' : 'MEDIUM';
    await this.logSecurityEvent({
      eventType,
      severity,
      details: paymentDetails,
      userId
    });
  }

  public async logAdminEvent(
    eventType: string,
    adminId: string,
    actionDetails: Record<string, any>
  ): Promise<void> {
    await this.logSecurityEvent({
      eventType,
      severity: 'HIGH',
      details: actionDetails,
      userId: adminId
    });
  }
}

// Export singleton instance
export const securityLogger = UnifiedSecurityLogger.getInstance();
```

### 1.3 Real-time Security Monitoring Edge Function

```typescript
// supabase/functions/send-security-alert/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SecurityAlertRequest {
  eventType: string;
  severity: string;
  details: Record<string, any>;
  userId?: string;
  timestamp: string;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { eventType, severity, details, userId, timestamp }: SecurityAlertRequest = await req.json()

    // Get super admin phone numbers for alerts
    const { data: superAdmins } = await supabaseAdmin
      .from('profiles')
      .select('phone, full_name')
      .eq('role', 'super_admin')
      .not('phone', 'is', null)

    if (!superAdmins || superAdmins.length === 0) {
      console.log('No super admins found for security alert')
      return new Response(JSON.stringify({ success: false, error: 'No recipients' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Format alert message
    const alertMessage = formatSecurityAlert(eventType, severity, details, userId, timestamp)

    // Send WhatsApp alerts to all super admins
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const integratedNumber = Deno.env.get('MSG91_INTEGRATED_NUMBER') || '919211848599'

    const toAndComponents = superAdmins.map(admin => ({
      to: [admin.phone.replace(/\D/g, '')], // Clean phone number
      components: {
        body_1: { type: "text", value: eventType },
        body_2: { type: "text", value: severity },
        body_3: { type: "text", value: timestamp },
        body_4: { type: "text", value: userId || 'System' },
        body_5: { type: "text", value: JSON.stringify(details).slice(0, 100) }
      }
    }))

    const msg91Payload = {
      integrated_number: integratedNumber,
      content_type: "template",
      payload: {
        messaging_product: "whatsapp",
        type: "template",
        template: {
          name: "grid2play_security_alert",
          language: {
            code: "en",
            policy: "deterministic"
          },
          namespace: "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
          to_and_components: toAndComponents
        }
      }
    }

    const response = await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'authkey': authKey
      },
      body: JSON.stringify(msg91Payload)
    })

    const result = await response.json()
    console.log('Security alert sent:', { eventType, severity, recipients: superAdmins.length })

    return new Response(JSON.stringify({ success: true, result }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('Security alert error:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

function formatSecurityAlert(
  eventType: string,
  severity: string,
  details: Record<string, any>,
  userId?: string,
  timestamp?: string
): string {
  return `🚨 SECURITY ALERT
Event: ${eventType}
Severity: ${severity}
Time: ${timestamp}
User: ${userId || 'System'}
Details: ${JSON.stringify(details).slice(0, 200)}

Please review immediately.`
}
```

## 2. Implementation Examples for Critical Events

### 2.1 Authentication Events Implementation

```typescript
// src/context/AuthContext.tsx - Enhanced with security logging
import { securityLogger } from '@/utils/unifiedSecurityLogger';

// In sign-in function
const signIn = async (phone: string, otp: string) => {
  try {
    const { data, error } = await supabase.auth.verifyOtp({
      phone,
      token: otp,
      type: 'sms'
    });

    if (error) {
      await securityLogger.logAuthenticationEvent('USER_LOGIN_FAILURE', undefined, {
        phone: phone.slice(-4), // Only log last 4 digits
        error_type: error.message,
        attempt_timestamp: new Date().toISOString()
      });
      throw error;
    }

    await securityLogger.logAuthenticationEvent('USER_LOGIN_SUCCESS', data.user?.id, {
      phone: phone.slice(-4),
      login_method: 'sms_otp',
      session_id: data.session?.access_token?.slice(-10)
    });

    // Rest of sign-in logic...
  } catch (error) {
    // Handle error...
  }
};
```

### 2.2 Booking Events Implementation

```typescript
// src/components/BookSlotModal.tsx - Enhanced with security logging
import { securityLogger } from '@/utils/unifiedSecurityLogger';

const handleBookingSubmission = async () => {
  try {
    // Existing booking logic...
    
    await securityLogger.logBookingEvent('BOOKING_CREATED', user.id, {
      booking_id: bookingResult.id,
      court_id: selectedCourt,
      booking_date: selectedDate,
      total_price: finalPrice,
      payment_method: paymentMethod,
      slot_count: selectedSlots.length
    });

    // Rest of booking logic...
  } catch (error) {
    await securityLogger.logBookingEvent('BOOKING_CREATION_FAILED', user.id, {
      court_id: selectedCourt,
      booking_date: selectedDate,
      error_message: error.message,
      attempted_slots: selectedSlots.length
    });
    throw error;
  }
};
```

### 2.3 Payment Events Implementation

```typescript
// supabase/functions/create-razorpay-order/index.ts - Enhanced with security logging
const logPaymentEvent = async (eventType: string, details: any, userId?: string) => {
  await supabaseAdmin
    .from('security_logs')
    .insert({
      event_type: eventType,
      severity: eventType.includes('FAILURE') ? 'HIGH' : 'MEDIUM',
      details,
      user_id: userId,
      created_at: new Date().toISOString()
    });
};

// In payment order creation
try {
  const order = await razorpay.orders.create(orderOptions);
  
  await logPaymentEvent('PAYMENT_INITIATION', {
    order_id: order.id,
    amount: order.amount,
    currency: order.currency,
    booking_details: validationResult
  }, userId);

  // Rest of order creation logic...
} catch (error) {
  await logPaymentEvent('PAYMENT_ORDER_CREATION_FAILED', {
    error_message: error.message,
    attempted_amount: orderOptions.amount,
    user_id: userId
  }, userId);
  throw error;
}
```

## 3. Rate Limiting and Notification Management

### 3.1 Notification Rate Limiter

```typescript
// src/utils/notificationRateLimiter.ts
export class NotificationRateLimiter {
  private static instance: NotificationRateLimiter;
  private eventCounts: Map<string, { count: number; lastReset: number }> = new Map();

  public static getInstance(): NotificationRateLimiter {
    if (!NotificationRateLimiter.instance) {
      NotificationRateLimiter.instance = new NotificationRateLimiter();
    }
    return NotificationRateLimiter.instance;
  }

  public shouldSendNotification(eventType: string, severity: string): boolean {
    const now = Date.now();
    const key = `${eventType}_${severity}`;
    
    // Rate limits based on severity
    const limits = {
      CRITICAL: { maxCount: Infinity, windowMs: 0 }, // No limit for critical
      HIGH: { maxCount: 1, windowMs: 5 * 60 * 1000 }, // 1 per 5 minutes
      MEDIUM: { maxCount: 1, windowMs: 60 * 60 * 1000 }, // 1 per hour
      LOW: { maxCount: 0, windowMs: Infinity } // No notifications for low
    };

    const limit = limits[severity as keyof typeof limits];
    if (!limit || limit.maxCount === 0) return false;
    if (limit.maxCount === Infinity) return true;

    const record = this.eventCounts.get(key) || { count: 0, lastReset: now };
    
    // Reset count if window has passed
    if (now - record.lastReset > limit.windowMs) {
      record.count = 0;
      record.lastReset = now;
    }

    if (record.count >= limit.maxCount) {
      return false;
    }

    record.count++;
    this.eventCounts.set(key, record);
    return true;
  }
}
```

## Next Steps

1. **Database Migration**: Run the enhanced security_logs table migration
2. **Deploy Edge Function**: Deploy the send-security-alert function
3. **Update Components**: Integrate security logging into existing components
4. **Configure MSG91**: Set up the security alert WhatsApp template
5. **Testing**: Comprehensive testing of all security event triggers
6. **Monitoring**: Set up dashboards for security event monitoring

# Enhanced MSG91 Chat Widget - Usage Guide

## Quick Start

The Enhanced MSG91 Chat Widget is now live on the Grid२Play homepage, providing a comprehensive support experience that combines FAQs, help tickets, and live chat.

## For Users

### Accessing Support
1. **Visit Homepage**: Go to the Grid२Play homepage
2. **Click Support Button**: Look for the emerald green floating button in the bottom-right corner
3. **Search FAQs**: Start by searching for your question or browse quick help categories

### Support Flow
```
FAQ Search → FAQ Detail → Still Need Help? → Live Chat or Ticket
```

### Features Available
- **Smart FAQ Search**: Type keywords to find relevant answers
- **Quick Categories**: Booking Issues, Payment Problems, Venue Questions, Account Help
- **Detailed Answers**: Full FAQ responses with helpful tags
- **Feedback System**: Rate FAQ helpfulness to improve content
- **Live Chat Escalation**: Connect directly with support agents
- **Help Tickets**: Create trackable support requests

## For Administrators

### Managing FAQs
FAQs are managed through the existing Supabase `faqs` table:

```sql
-- Add new FAQ
INSERT INTO faqs (question, answer, tags, is_active) 
VALUES (
  'How do I change my booking?',
  'To change your booking, go to My Bookings and select the booking you want to modify...',
  ARRAY['booking', 'change', 'modify'],
  true
);

-- Update existing FAQ
UPDATE faqs 
SET answer = 'Updated answer text...'
WHERE id = 1;

-- Deactivate FAQ
UPDATE faqs 
SET is_active = false 
WHERE id = 1;
```

### Monitoring Support Requests
Help requests are tracked in the `help_requests` table with enhanced context:

```sql
-- View recent support requests with context
SELECT 
  hr.ticket_number,
  hr.subject,
  hr.status,
  hr.category,
  hr.created_at,
  p.full_name,
  p.phone
FROM help_requests hr
LEFT JOIN profiles p ON hr.user_id = p.id
ORDER BY hr.created_at DESC
LIMIT 20;
```

### MSG91 Integration
The widget automatically passes enhanced user context to MSG91:
- User profile information
- Recent booking history
- Current page context
- Device and session information
- Previous support interactions

## For Developers

### Component Structure
```
src/components/EnhancedMSG91ChatWidget.tsx  # Main widget component
src/services/faqService.ts                  # FAQ management service
src/services/userContextService.ts          # User context aggregation
```

### Key Props and Configuration
```tsx
<EnhancedMSG91ChatWidget 
  className="custom-positioning" // Optional custom styling
/>
```

### Service Usage
```tsx
// FAQ Service
import { faqService } from '@/services/faqService';

// Search FAQs
const results = await faqService.searchFAQs('booking cancel');

// Get categories
const categories = await faqService.getFAQCategories();

// User Context Service
import { userContextService } from '@/services/userContextService';

// Get comprehensive user context
const context = await userContextService.getUserContext(user);
```

### Customization Options

#### Styling
The widget uses Tailwind CSS classes and can be customized:
```tsx
// Custom colors
className="[&_.bg-emerald-900]:bg-blue-900 [&_.text-emerald-300]:text-blue-300"

// Custom positioning
className="bottom-4 right-4" // Instead of bottom-6 right-6
```

#### MSG91 Configuration
Widget token and configuration are in the component:
```tsx
const finalConfig = {
  widgetToken: "65828", // Your MSG91 widget token
  // ... other configuration
};
```

## Testing and Quality Assurance

### Manual Testing Checklist
- [ ] Widget appears on homepage
- [ ] FAQ search returns relevant results
- [ ] Quick action buttons work correctly
- [ ] FAQ detail view displays properly
- [ ] Feedback system functions
- [ ] Live chat escalation works
- [ ] Help ticket creation succeeds
- [ ] Mobile responsiveness verified
- [ ] Touch targets meet 44px minimum

### Automated Testing
```bash
# Run component tests
npm test EnhancedMSG91ChatWidget

# Run service tests
npm test faqService
npm test userContextService
```

## Troubleshooting

### Common Issues

#### Widget Not Appearing
1. Check browser console for errors
2. Verify component is imported in Index.tsx
3. Ensure user authentication is working

#### FAQ Search Not Working
1. Check Supabase connection
2. Verify `faqs` table has active records
3. Check network requests in browser dev tools

#### MSG91 Chat Not Loading
1. Verify widget token "65828" is correct
2. Check MSG91 script loading in network tab
3. Ensure user context service is working

### Debug Mode
Enable detailed logging:
```javascript
// In browser console
localStorage.setItem('debug_enhanced_widget', 'true');
```

### Performance Monitoring
```javascript
// Check FAQ cache status
console.log(faqService.lastFetchTime);

// Monitor user context loading
console.time('user-context-load');
await userContextService.getUserContext(user);
console.timeEnd('user-context-load');
```

## Removal Instructions

### Complete Removal (For Testing)
If you need to remove the enhanced widget completely:

1. **Remove from Index.tsx**:
```tsx
// Remove this import
import EnhancedMSG91ChatWidget from '@/components/EnhancedMSG91ChatWidget';

// Remove this component
<EnhancedMSG91ChatWidget />
```

2. **Delete component files**:
```bash
rm src/components/EnhancedMSG91ChatWidget.tsx
rm src/services/faqService.ts
rm src/services/userContextService.ts
rm docs/ENHANCED_MSG91_CHAT_WIDGET.md
rm docs/ENHANCED_WIDGET_USAGE_GUIDE.md
```

3. **Revert to basic MSG91** (if needed):
```tsx
// Add back to Index.tsx
const useMSG91ChatWidget = (user) => {
  useEffect(() => {
    // Basic MSG91 initialization
    const script = document.createElement('script');
    script.src = 'https://blacksea.msg91.com/chat-widget.js';
    script.onload = () => {
      initChatWidget({
        widgetToken: "65828",
        unique_id: user?.id || 'guest',
        name: user?.user_metadata?.full_name || 'Grid२Play User',
        // ... basic config
      }, 1000);
    };
    document.head.appendChild(script);
  }, [user]);
};
```

## Best Practices

### Content Management
- Keep FAQ answers concise but comprehensive
- Use relevant tags for better searchability
- Regularly review and update FAQ content
- Monitor FAQ feedback to identify gaps

### User Experience
- Respond to help tickets promptly
- Use MSG91 chat for real-time issues
- Collect user feedback to improve support flow
- Monitor support metrics and user satisfaction

### Technical Maintenance
- Monitor FAQ service cache performance
- Review user context data for privacy compliance
- Keep MSG91 integration updated
- Regular testing of all support flows

---

**Need Help?**
- Check the main documentation: `ENHANCED_MSG91_CHAT_WIDGET.md`
- Review component code for implementation details
- Test in development environment before production changes

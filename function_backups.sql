-- =====================================================
-- GRID२PLAY FUNCTION BACKUPS FOR TDS ROLLBACK
-- =====================================================
-- Created: 2025-07-12
-- Purpose: Backup original function definitions before TDS implementation
-- =====================================================

-- =====================================================
-- BACKUP: get_custom_revenue_with_option_b()
-- =====================================================

CREATE OR REPLACE FUNCTION get_custom_revenue_with_option_b_BACKUP(
  start_date DATE,
  end_date DATE,
  admin_user_id UUID DEFAULT NULL,
  filter_venue_id UUID DEFAULT NULL
) RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_role TEXT;
  venue_ids UUID[];
  result JSON;
  -- Total metrics
  gross_revenue DECIMAL := 0;
  platform_fee DECIMAL := 0;
  net_settlement DECIMAL := 0;
  total_bookings_count INTEGER := 0;
  -- Online metrics
  online_gross_revenue DECIMAL := 0;
  online_platform_fee DECIMAL := 0;
  online_settlement DECIMAL := 0;
  online_bookings_count INTEGER := 0;
  -- Offline metrics
  offline_gross_revenue DECIMAL := 0;
  offline_platform_fee DECIMAL := 0;
  offline_settlement DECIMAL := 0;
  offline_bookings_count INTEGER := 0;
  -- Coupon metrics
  coupon_discount_total DECIMAL := 0;
  coupon_bookings_count INTEGER := 0;
  -- Cancelled metrics
  cancelled_bookings_count INTEGER := 0;
  cancelled_amount DECIMAL := 0;
BEGIN
  -- Get user role
  SELECT CASE 
    WHEN EXISTS(SELECT 1 FROM user_roles WHERE user_id = admin_user_id AND role = 'super_admin') THEN 'super_admin'
    WHEN EXISTS(SELECT 1 FROM user_roles WHERE user_id = admin_user_id AND role = 'admin') THEN 'admin'
    ELSE 'user'
  END INTO user_role;

  -- Get venue access based on role
  IF user_role = 'admin' THEN
    SELECT ARRAY_AGG(venue_id) INTO venue_ids
    FROM venue_admins 
    WHERE user_id = admin_user_id;
  ELSIF user_role = 'super_admin' THEN
    SELECT ARRAY_AGG(id) INTO venue_ids
    FROM venues 
    WHERE is_active = true;
  ELSE
    venue_ids := ARRAY[]::UUID[];
  END IF;

  -- PHASE 2: ONLINE VS OFFLINE REVENUE SEPARATION
  WITH booking_stats AS (
    SELECT 
      -- Original amount (before discount)
      COALESCE(cu.original_price, b.total_price) as original_amount,
      -- User payment (after discount)
      b.total_price as payment_amount,
      -- Coupon discount
      COALESCE(cu.discount_applied, 0) as discount_amount,
      -- Platform fee (calculated on original amount)
      COALESCE(cu.original_price, b.total_price) * (COALESCE(v.platform_fee_percentage, 5.0) / 100) as platform_fee_amount,
      -- Payment method
      b.payment_method,
      -- Coupon usage
      CASE WHEN cu.id IS NOT NULL THEN true ELSE false END as has_coupon,
      -- Booking status
      b.status
    FROM bookings b
    INNER JOIN courts c ON b.court_id = c.id
    INNER JOIN venues v ON c.venue_id = v.id
    LEFT JOIN coupon_usage cu ON b.id = cu.booking_id
    WHERE 
      b.booking_date BETWEEN start_date AND end_date
      AND b.status IN ('confirmed', 'completed', 'cancelled')
      AND (
        -- Filter by venue access
        user_role = 'super_admin' 
        OR c.venue_id = ANY(venue_ids)
        -- Filter by specific venue if provided
        OR (filter_venue_id IS NOT NULL AND c.venue_id = filter_venue_id)
      )
  )
  SELECT 
    -- TOTAL METRICS
    COUNT(*) FILTER (WHERE status IN ('confirmed', 'completed')),
    SUM(original_amount) FILTER (WHERE status IN ('confirmed', 'completed')),
    SUM(platform_fee_amount) FILTER (WHERE status IN ('confirmed', 'completed')),
    SUM(payment_amount - platform_fee_amount) FILTER (WHERE status IN ('confirmed', 'completed')),
    
    -- ONLINE METRICS (payment_method = 'online')
    COUNT(*) FILTER (WHERE status IN ('confirmed', 'completed') AND payment_method = 'online'),
    SUM(original_amount) FILTER (WHERE status IN ('confirmed', 'completed') AND payment_method = 'online'),
    SUM(platform_fee_amount) FILTER (WHERE status IN ('confirmed', 'completed') AND payment_method = 'online'),
    SUM(payment_amount - platform_fee_amount) FILTER (WHERE status IN ('confirmed', 'completed') AND payment_method = 'online'),
    
    -- OFFLINE METRICS (payment_method = 'cash')
    COUNT(*) FILTER (WHERE status IN ('confirmed', 'completed') AND payment_method = 'cash'),
    SUM(original_amount) FILTER (WHERE status IN ('confirmed', 'completed') AND payment_method = 'cash'),
    SUM(platform_fee_amount) FILTER (WHERE status IN ('confirmed', 'completed') AND payment_method = 'cash'),
    SUM(payment_amount - platform_fee_amount) FILTER (WHERE status IN ('confirmed', 'completed') AND payment_method = 'cash'),
    
    -- COUPON METRICS
    SUM(discount_amount) FILTER (WHERE status IN ('confirmed', 'completed')),
    COUNT(*) FILTER (WHERE has_coupon AND status IN ('confirmed', 'completed')),
    
    -- CANCELLED METRICS
    COUNT(*) FILTER (WHERE status = 'cancelled'),
    SUM(original_amount) FILTER (WHERE status = 'cancelled')
  INTO 
    total_bookings_count,
    gross_revenue,
    platform_fee,
    net_settlement,
    online_bookings_count,
    online_gross_revenue,
    online_platform_fee,
    online_settlement,
    offline_bookings_count,
    offline_gross_revenue,
    offline_platform_fee,
    offline_settlement,
    coupon_discount_total,
    coupon_bookings_count,
    cancelled_bookings_count,
    cancelled_amount
  FROM booking_stats;

  -- Build result JSON with online/offline separation
  SELECT json_build_object(
    'totalBookings', COALESCE(total_bookings_count, 0),
    'grossRevenue', COALESCE(gross_revenue, 0),
    'platformFee', COALESCE(platform_fee, 0),
    'netSettlement', COALESCE(net_settlement, 0),
    
    'onlineBookings', COALESCE(online_bookings_count, 0),
    'onlineGrossRevenue', COALESCE(online_gross_revenue, 0),
    'onlinePlatformFee', COALESCE(online_platform_fee, 0),
    'onlineSettlement', COALESCE(online_settlement, 0),
    
    'offlineBookings', COALESCE(offline_bookings_count, 0),
    'offlineGrossRevenue', COALESCE(offline_gross_revenue, 0),
    'offlinePlatformFee', COALESCE(offline_platform_fee, 0),
    'offlineSettlement', COALESCE(offline_settlement, 0),
    
    'couponDiscountTotal', COALESCE(coupon_discount_total, 0),
    'couponBookingsCount', COALESCE(coupon_bookings_count, 0),
    'cancelledBookingsCount', COALESCE(cancelled_bookings_count, 0),
    'cancelledAmount', COALESCE(cancelled_amount, 0),
    'dateRange', json_build_object('start', start_date, 'end', end_date),
    'userRole', user_role
  ) INTO result;

  RETURN result;
END;
$$;

import { useState, useEffect } from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import { RouteGuard } from "./components/RouteGuard";
import { AuthErrorBoundary } from "./components/AuthErrorBoundary";
import { initializeExtensionSafety } from "./utils/extensionSafeNavigation";
import { ExtensionSafetyMonitor } from "./components/ExtensionConflictNotice";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Venues from "./pages/Venues";
import VenueDetails from "./pages/VenueDetails";
import Sports from "./pages/Sports";
import Bookings from "./pages/Bookings";
import BookingPage from "./pages/BookingPage";
import Login from "./pages/Login";
import Register from "./pages/Register";
import VerifyEmail from "./pages/VerifyEmail";
import VerifyEmailPrompt from "./pages/VerifyEmailPrompt";
import VerifyEmailToken from "./pages/VerifyEmailToken";
import ResetPassword from "./pages/ResetPassword";
import AdminDashboard from "./pages/admin/Dashboard";
import AdminHome from "./pages/admin/AdminHome";
import Profile from "./pages/Profile";
import ChallengeDashboard from "./pages/challenge/ChallengeDashboard";
import TeamDetails from "./pages/challenge/TeamDetails";
import EnhancedMSG91ChatWidget from "./components/EnhancedMSG91ChatWidget";
import FAQ from "./pages/Faq3Demo";
import Help from "./pages/Help";
import Contact from "./pages/Contact";
import Privacy from "./pages/Privacy";
import TermsConditions from "./pages/TermsConditions";
import CookiePolicy from "./pages/CookiePolicy";
import Settings from "./pages/Settings";
import CancellationRefunds from "./pages/CancellationRefunds";
import BottomNav from "./components/ui/BottomNav";
import LegalComplianceManager from "./components/LegalComplianceManager";
import CookieSettingsManager from "./components/CookieSettingsManager";

// Enhanced Tournament System Imports
import { TournamentWelcomePage } from "./pages/tournament/TournamentWelcomePage";
import { TournamentBrowsePageEnhanced } from "./pages/tournament/TournamentBrowsePageEnhanced";
import { TournamentDetailsPageEnhanced } from "./pages/tournament/TournamentDetailsPageEnhanced";
import { HostTournamentPageEnhanced } from "./pages/tournament/HostTournamentPageEnhanced";
import { OrganizerDashboard } from "./pages/tournament/OrganizerDashboard";
import { FEATURE_FLAGS } from "./utils/featureFlags";

// Legacy Tournament Imports (for backward compatibility)
import { TournamentDashboard } from "./pages/tournament/TournamentDashboard";
import TournamentDetailsPage from "./pages/tournament/TournamentDetailsPage";
import { HostTournamentPage } from "./pages/tournament/HostTournamentPage";
import MorePage from "./pages/MorePage";
import ScrollToTopOnMobile from "@/components/ScrollToTopOnMobile";
import NotificationBell from './components/NotificationBell';
import { HelmetProvider } from 'react-helmet-async';
import AdminRedirector from './components/AdminRedirector';
import AnalyticsDashboard_Mobile from '@/pages/admin/AnalyticsDashboard_Mobile';
import BookingTrends_Mobile from '@/pages/admin/BookingTrends_Mobile';
import PopularSports_Mobile from '@/pages/admin/PopularSports_Mobile';
import PeakHours_Mobile from '@/pages/admin/PeakHours_Mobile';
import RecentBookings_Mobile from '@/pages/admin/RecentBookings_Mobile';
import Bookings_Mobile from '@/pages/admin/Bookings_Mobile';
import BookForCustomer_Mobile from '@/pages/admin/BookForCustomer_Mobile';
import BlockTimeSlots_Mobile from '@/pages/admin/BlockTimeSlots_Mobile';
import AdminHome_Mobile from '@/pages/admin/AdminHome_Mobile';
import AdminBottomNav from './components/ui/AdminBottomNav';

import ReviewManagement_Mobile from '@/pages/admin/ReviewManagement_Mobile';
import VenueManagement_Mobile from '@/pages/admin/VenueManagement_Mobile';
import HelpRequestsManagement_Mobile from '@/pages/admin/HelpRequestsManagement_Mobile';
import { VenueFAQManagement } from '@/components/admin/VenueFAQManagement';
import RefundsCancellations from '@/pages/admin/RefundsCancellations';
import RefundsCancellations_Mobile from '@/pages/admin/RefundsCancellations_Mobile';
import EarningsDashboard_Mobile from '@/pages/admin/EarningsDashboard_Mobile';
import SettlementsList_Mobile from '@/pages/admin/SettlementsList_Mobile';
import SuperAdminSlotManagementPage from '@/pages/admin/SuperAdminSlotManagement';
import SuperAdminDashboard from '@/pages/admin/SuperAdminDashboard';
import FirstTimeWelcome from './pages/FirstTimeWelcome';

const queryClient = new QueryClient();

const App = () => {
  const [chatActive, setChatActive] = useState(false);
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;

  const handleChatClick = () => setChatActive((prev) => !prev);

  // Initialize extension safety on app startup
  useEffect(() => {
    initializeExtensionSafety();
  }, []);

  // Listen for chat widget open events from SupportButton
  useEffect(() => {
    const handleOpenChatWidget = () => {
      setChatActive(true);
    };

    window.addEventListener('openChatWidget', handleOpenChatWidget as EventListener);

    return () => {
      window.removeEventListener('openChatWidget', handleOpenChatWidget as EventListener);
    };
  }, []);

  return (
    <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AuthErrorBoundary>
            <AuthProvider>
              <ScrollToTopOnMobile />
              {isMobile && <NotificationBell />}
              <AdminRedirector />
              <ExtensionSafetyMonitor />
            <Routes>
              {/* Public routes */}
              <Route element={<RouteGuard requireAuth={false} />}>
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/verify-email" element={<VerifyEmail />} />
                <Route path="/verify-email-prompt" element={<VerifyEmailPrompt />} />
                <Route path="/verify-email-token" element={<VerifyEmailToken />} />
                <Route path="/reset-password" element={<ResetPassword />} />
              </Route>

              {/* Protected routes - only for normal users */}
              <Route element={<RouteGuard requireAuth={true} adminOnly={false} />}>
                <Route path="/legal-agreements" element={<FirstTimeWelcome />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/bookings" element={<Bookings />} />
                <Route path="/book" element={<BookingPage />} />
                <Route path="/book/venue/:venueId" element={<BookingPage />} />
                <Route path="/book/venue/:venueId/sport/:sportId" element={<BookingPage />} />
                <Route path="/book/venue/:venueId/sport/:sportId/court/:courtId" element={<BookingPage />} />
                <Route path="/challenge" element={<ChallengeDashboard />} />
                <Route path="/team/:slug" element={<TeamDetails />} />
                <Route path="/more" element={<MorePage />} />
              </Route>
              
              {/* Mobile admin routes - MUST come before catch-all /admin/* route */}
              <Route element={<RouteGuard requireAuth={true} requiredRole="admin" adminOnly={true} />}>
                {/* Mobile admin home */}
                <Route path="/admin/mobile-home" element={<AdminHome_Mobile />} />

                {/* Mobile analytics routes */}
                <Route path="/admin/analytics-mobile" element={<AnalyticsDashboard_Mobile />} />
                <Route path="/admin/booking-trends-mobile" element={<BookingTrends_Mobile />} />
                <Route path="/admin/popular-sports-mobile" element={<PopularSports_Mobile />} />
                <Route path="/admin/peak-hours-mobile" element={<PeakHours_Mobile />} />
                <Route path="/admin/recent-bookings-mobile" element={<RecentBookings_Mobile />} />

                {/* Mobile bookings routes */}
                <Route path="/admin/bookings-mobile" element={<Bookings_Mobile />} />
                <Route path="/admin/book-for-customer-mobile" element={<BookForCustomer_Mobile />} />
                <Route path="/admin/block-time-slots-mobile" element={<BlockTimeSlots_Mobile />} />

                {/* Mobile admin section routes */}
                <Route path="/admin/venues-mobile" element={<VenueManagement_Mobile />} />
                <Route path="/admin/reviews-mobile" element={<ReviewManagement_Mobile />} />

                <Route path="/admin/help-mobile" element={<HelpRequestsManagement_Mobile />} />
                <Route path="/admin/faq-management" element={<VenueFAQManagement />} />
                { <Route path="/admin/refunds-cancellations" element={<RefundsCancellations />} /> }
                {<Route path="/admin/refunds-cancellations-mobile" element={<RefundsCancellations_Mobile />} /> }

                {/* Earnings & Settlement routes */}
                <Route path="/admin/earnings-mobile" element={<EarningsDashboard_Mobile />} />
                <Route path="/admin/settlements-mobile" element={<SettlementsList_Mobile />} />
              </Route>

              {/* Super Admin only routes */}
              <Route element={<RouteGuard requireAuth={true} requiredRole="super_admin" adminOnly={true} />}>
                <Route path="/admin/super-dashboard" element={<SuperAdminDashboard />} />
                <Route path="/admin/super-slot-management" element={<SuperAdminSlotManagementPage />} />
              </Route>

              {/* Admin routes - accessible to both admin and super_admin */}
              <Route element={<RouteGuard requireAuth={true} requiredRole="admin" adminOnly={true} />}>
                <Route path="/admin" element={<AdminHome />} />
                <Route path="/admin/*" element={<AdminDashboard />} />
              </Route>

              {/* Enhanced Tournament System Routes - Only available when feature is enabled */}
              {FEATURE_FLAGS.TOURNAMENTS && (
                <>
                  {/* Public: Tournament discovery and details */}
                  <Route path="/tournaments" element={<TournamentWelcomePage />} />
                  <Route path="/tournaments/browse" element={<TournamentBrowsePageEnhanced />} />
                  <Route path="/tournaments/:slug" element={<TournamentDetailsPageEnhanced />} />

                  {/* Protected: Tournament management for logged-in users */}
                  <Route element={<RouteGuard requireAuth={true} adminOnly={false} />}>
                    <Route path="/tournaments/host" element={<HostTournamentPageEnhanced />} />
                    <Route path="/tournaments/organizer" element={<OrganizerDashboard />} />
                    <Route path="/tournaments/manage" element={<OrganizerDashboard />} />
                  </Route>

                  {/* Legacy Tournament Routes (for backward compatibility) */}
                  <Route path="/tournaments/legacy" element={<TournamentDashboard />} />
                  <Route path="/tournaments/legacy/:slug" element={<TournamentDetailsPage />} />
                  <Route element={<RouteGuard requireAuth={true} adminOnly={false} />}>
                    <Route path="/tournaments/legacy/host" element={<HostTournamentPage />} />
                  </Route>
                </>
              )}

              {/* Root path and content routes - also protected from admin access via RouteGuard logic */}
              <Route path="/" element={<Index />} />
              <Route path="/venues" element={<Venues />} />
              <Route path="/venues/:id" element={<VenueDetails />} />
              <Route path="/sports" element={<Sports />} />
              <Route path="/faq" element={<FAQ />} />
              <Route path="/help" element={<Help />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/privacy" element={<Privacy />} />
              <Route path="/terms" element={<TermsConditions />} />
              <Route path="/cookie-policy" element={<CookiePolicy />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="/cancellation-refunds" element={<CancellationRefunds />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
            
            {/* Enhanced MSG91 Chat Widget with consistent state management */}
            <EnhancedMSG91ChatWidget isOpen={chatActive} setIsOpen={setChatActive} />
            <LegalComplianceManager />
            <CookieSettingsManager />
            {(!chatActive || !isMobile) && (
              <>
                <AdminBottomNav />
                <BottomNav onChatClick={handleChatClick} chatActive={chatActive} setChatActive={setChatActive} />
              </>
            )}
          </AuthProvider>
          </AuthErrorBoundary>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
    </HelmetProvider>
  );
};

export default App;

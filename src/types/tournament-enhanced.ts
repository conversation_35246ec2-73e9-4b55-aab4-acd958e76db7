// Enhanced Tournament Types for the New Isolated Tournament System
import { Database } from '@/integrations/supabase/types';

// Tournament Organizer Types
export interface TournamentOrganizer {
  id: string;
  organization_name: string;
  organization_type: 'academy' | 'league' | 'federation' | 'club' | 'corporate';
  contact_person_name: string;
  contact_email: string;
  contact_phone: string;
  website_url?: string;
  logo_url?: string;
  description?: string;
  verification_status: 'pending' | 'verified' | 'rejected';
  partnership_tier: 'basic' | 'premium' | 'enterprise';
  user_id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Tournament Category Types
export interface TournamentCategory {
  id: string;
  name: string;
  description?: string;
  age_group_min?: number;
  age_group_max?: number;
  skill_level?: 'beginner' | 'intermediate' | 'advanced' | 'professional';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Enhanced Tournament Types
export interface TournamentEnhanced {
  id: string;
  name: string;
  slug: string;
  description?: string;
  sport_id: string;
  venue_id: string;
  organizer_id: string;
  category_id?: string;
  
  // Tournament Configuration
  tournament_type: 'knockout' | 'round_robin' | 'league' | 'swiss';
  format_type: 'individual' | 'team' | 'mixed';
  max_participants: number;
  min_participants: number;
  team_size: number;
  
  // Dates and Registration
  registration_start_date: string;
  registration_end_date: string;
  tournament_start_date: string;
  tournament_end_date: string;
  
  // Financial
  entry_fee: number;
  prize_pool: number;
  registration_fee_distribution: {
    organizer: number;
    venue: number;
    platform: number;
  };
  
  // Tournament Rules and Settings
  rules?: string;
  age_restrictions?: {
    min_age?: number;
    max_age?: number;
  };
  skill_requirements?: string;
  equipment_provided: boolean;
  live_streaming: boolean;
  public_viewing: boolean;
  
  // Status and Metadata
  status: 'draft' | 'published' | 'registration_open' | 'registration_closed' | 'ongoing' | 'completed' | 'cancelled';
  visibility: 'public' | 'private' | 'invite_only';
  featured: boolean;
  
  // Analytics and Engagement
  view_count: number;
  registration_count: number;
  spectator_count: number;
  
  // Metadata
  created_at: string;
  updated_at: string;
  published_at?: string;
  completed_at?: string;
  
  // Joined data
  sport_name?: string;
  venue_name?: string;
  venue_location?: string;
  organizer_name?: string;
  category_name?: string;
}

// Tournament Participant Types
export interface TournamentParticipant {
  id: string;
  tournament_id: string;
  user_id: string;
  
  // Team Information
  team_name: string;
  team_captain_id?: string;
  team_members: string[]; // Array of user IDs
  player_count: number;
  
  // Registration Details
  registration_date: string;
  registration_source: 'web' | 'mobile' | 'admin' | 'bulk_import';
  
  // Player Information
  player_age?: number;
  player_skill_level?: 'beginner' | 'intermediate' | 'advanced' | 'professional';
  player_rating: number;
  previous_tournament_count: number;
  
  // Payment and Status
  payment_status: 'pending' | 'paid' | 'refunded' | 'waived';
  payment_reference?: string;
  payment_amount?: number;
  payment_date?: string;
  
  // Seeding and Bracket Position
  seed_number?: number;
  bracket_position?: number;
  
  // Status and Notes
  status: 'registered' | 'confirmed' | 'withdrawn' | 'disqualified';
  notes?: string;
  admin_notes?: string;
  
  // Metadata
  created_at: string;
  updated_at: string;
  
  // Joined data
  user_name?: string;
  user_email?: string;
}

// Tournament Match Types
export interface TournamentMatchEnhanced {
  id: string;
  tournament_id: string;
  bracket_id: string;
  
  // Match Identification
  round_number: number;
  match_number: number;
  bracket_position: number;
  
  // Participants
  participant_a_id?: string;
  participant_b_id?: string;
  
  // Venue and Scheduling
  venue_id: string;
  court_name?: string;
  match_date?: string;
  start_time?: string;
  end_time?: string;
  estimated_duration: number;
  
  // Match Details
  match_format: string;
  scoring_system: string;
  
  // Status and Results
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'postponed';
  winner_id?: string;
  
  // Live Match Data
  live_score: any;
  match_statistics: any;
  
  // Broadcasting and Viewing
  is_featured: boolean;
  live_stream_url?: string;
  spectator_count: number;
  
  // Metadata
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
  
  // Joined data
  participant_a_name?: string;
  participant_b_name?: string;
  venue_name?: string;
}

// Player Tournament Statistics
export interface PlayerTournamentStats {
  id: string;
  user_id: string;
  sport_id: string;
  
  // Overall Statistics
  tournaments_played: number;
  tournaments_won: number;
  tournaments_runner_up: number;
  tournaments_semifinal: number;
  
  // Match Statistics
  total_matches: number;
  matches_won: number;
  matches_lost: number;
  win_percentage: number;
  
  // Performance Metrics
  current_rating: number;
  peak_rating: number;
  rating_history: any[];
  
  // Achievements
  total_prize_money: number;
  biggest_tournament_win?: string;
  longest_winning_streak: number;
  current_winning_streak: number;
  
  // Recent Performance
  last_tournament_date?: string;
  recent_form: any[];
  
  // Rankings
  local_ranking?: number;
  national_ranking?: number;
  global_ranking?: number;
  
  created_at: string;
  updated_at: string;
  
  // Joined data
  sport_name?: string;
}

// Tournament Achievement Types
export interface TournamentAchievement {
  id: string;
  user_id: string;
  tournament_id?: string;
  
  achievement_type: 'tournament_winner' | 'tournament_runner_up' | 'tournament_semifinalist' | 
                   'first_tournament' | 'veteran_player' | 'giant_killer' | 'undefeated' |
                   'comeback_king' | 'perfect_season' | 'multi_sport_champion';
  achievement_name: string;
  achievement_description?: string;
  
  achievement_data: any;
  points_awarded: number;
  badge_icon_url?: string;
  
  earned_date: string;
  is_featured: boolean;
  created_at: string;
  
  // Joined data
  tournament_name?: string;
}

// Tournament Analytics Types
export interface TournamentAnalytics {
  id: string;
  tournament_id: string;
  
  // Registration Analytics
  total_registrations: number;
  daily_registrations: any;
  registration_sources: any;
  
  // Participant Demographics
  age_distribution: any;
  skill_level_distribution: any;
  geographic_distribution: any;
  
  // Engagement Metrics
  total_views: number;
  unique_viewers: number;
  social_shares: number;
  spectator_attendance: number;
  
  // Financial Analytics
  total_revenue: number;
  revenue_breakdown: any;
  refunds_issued: number;
  
  // Performance Metrics
  completion_rate: number;
  satisfaction_score: number;
  
  // Real-time Data
  live_spectators: number;
  current_matches: number;
  
  created_at: string;
  updated_at: string;
}

// Form Types for Tournament Creation
export interface CreateTournamentForm {
  // Basic Information
  tournament_name: string;
  description?: string;
  sport_id: string;
  venue_id: string;
  category_id?: string;
  
  // Tournament Configuration
  tournament_type: 'knockout' | 'round_robin' | 'league' | 'swiss';
  format_type: 'individual' | 'team' | 'mixed';
  max_participants: number;
  min_participants: number;
  team_size: number;
  
  // Dates
  registration_start_date: string;
  registration_end_date: string;
  tournament_start_date: string;
  tournament_end_date: string;
  
  // Financial
  entry_fee: number;
  prize_pool: number;
  
  // Settings
  rules?: string;
  age_restrictions?: {
    min_age?: number;
    max_age?: number;
  };
  skill_requirements?: string;
  equipment_provided: boolean;
  live_streaming: boolean;
  public_viewing: boolean;
  visibility: 'public' | 'private' | 'invite_only';
  
  // Organizer Information (for new organizers)
  organizer_name?: string;
  contact_info?: string;
}

// Tournament Registration Form
export interface TournamentRegistrationForm {
  team_name: string;
  player_count: number;
  player_skill_level?: 'beginner' | 'intermediate' | 'advanced' | 'professional';
  notes?: string;
  team_members?: string[]; // For team tournaments
}

// Tournament Filter Types
export interface TournamentFilters {
  status?: string[];
  sport_id?: string;
  venue_id?: string;
  tournament_type?: string[];
  format_type?: string[];
  entry_fee_range?: [number, number];
  date_range?: [string, string];
  featured_only?: boolean;
  search_query?: string;
}

// Tournament List Response
export interface TournamentListResponse {
  tournaments: TournamentEnhanced[];
  total_count: number;
  page: number;
  page_size: number;
  has_more: boolean;
}

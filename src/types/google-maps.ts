// Google Maps API TypeScript interfaces for Places Autocomplete

export interface GoogleMapsConfig {
  apiKey: string;
  libraries: string[];
  region?: string;
  language?: string;
}

export interface PlacesPrediction {
  description: string;
  matched_substrings: Array<{
    length: number;
    offset: number;
  }>;
  place_id: string;
  reference: string;
  structured_formatting: {
    main_text: string;
    main_text_matched_substrings: Array<{
      length: number;
      offset: number;
    }>;
    secondary_text: string;
  };
  terms: Array<{
    offset: number;
    value: string;
  }>;
  types: string[];
}

export interface PlacesAutocompletePrediction {
  predictions: PlacesPrediction[];
  status: string;
}

export interface PlaceDetailsResult {
  address_components: Array<{
    long_name: string;
    short_name: string;
    types: string[];
  }>;
  formatted_address: string;
  geometry: {
    location: {
      lat(): number;
      lng(): number;
    };
    viewport: {
      getNorthEast(): { lat(): number; lng(): number };
      getSouthWest(): { lat(): number; lng(): number };
    };
  };
  name: string;
  place_id: string;
  types: string[];
}

export interface PlaceDetailsResponse {
  result: PlaceDetailsResult;
  status: string;
}

export interface AutocompleteRequest {
  input: string;
  componentRestrictions?: {
    country: string | string[];
  };
  bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  types?: string[];
  strictBounds?: boolean;
}

export interface PlaceDetailsRequest {
  placeId: string;
  fields: string[];
}

// Delhi NCR bounds for location restriction
export const DELHI_NCR_BOUNDS = {
  north: 28.8955,
  south: 28.4089,
  east: 77.6081,
  west: 76.8380
};

// Google Maps API status codes
export enum GoogleMapsStatus {
  OK = 'OK',
  ZERO_RESULTS = 'ZERO_RESULTS',
  OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
  REQUEST_DENIED = 'REQUEST_DENIED',
  INVALID_REQUEST = 'INVALID_REQUEST',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// Address component types for parsing
export enum AddressComponentTypes {
  STREET_NUMBER = 'street_number',
  ROUTE = 'route',
  SUBLOCALITY_LEVEL_1 = 'sublocality_level_1',
  SUBLOCALITY_LEVEL_2 = 'sublocality_level_2',
  LOCALITY = 'locality',
  ADMINISTRATIVE_AREA_LEVEL_1 = 'administrative_area_level_1',
  ADMINISTRATIVE_AREA_LEVEL_2 = 'administrative_area_level_2',
  COUNTRY = 'country',
  POSTAL_CODE = 'postal_code'
}

// Global Google Maps API interface
declare global {
  interface Window {
    google: {
      maps: {
        places: {
          AutocompleteService: new () => {
            getPlacePredictions: (
              request: AutocompleteRequest,
              callback: (predictions: PlacesPrediction[] | null, status: string) => void
            ) => void;
          };
          PlacesService: new (map: HTMLDivElement) => {
            getDetails: (
              request: PlaceDetailsRequest,
              callback: (result: PlaceDetailsResult | null, status: string) => void
            ) => void;
          };
        };
      };
    };
    initGoogleMaps?: () => void;
  }
}

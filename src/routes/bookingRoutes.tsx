import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import BookingPage from '@/pages/BookingPage';

// Protected route wrapper
const ProtectedBookingRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

// Booking routes configuration
const BookingRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Main booking page */}
      <Route 
        path="/book" 
        element={
          <ProtectedBookingRoute>
            <BookingPage />
          </ProtectedBookingRoute>
        } 
      />
      
      {/* Booking with pre-filled venue */}
      <Route 
        path="/book/venue/:venueId" 
        element={
          <ProtectedBookingRoute>
            <BookingPage />
          </ProtectedBookingRoute>
        } 
      />
      
      {/* Booking with pre-filled venue and sport */}
      <Route 
        path="/book/venue/:venueId/sport/:sportId" 
        element={
          <ProtectedBookingRoute>
            <BookingPage />
          </ProtectedBookingRoute>
        } 
      />
      
      {/* Booking with pre-filled venue, sport, and court */}
      <Route 
        path="/book/venue/:venueId/sport/:sportId/court/:courtId" 
        element={
          <ProtectedBookingRoute>
            <BookingPage />
          </ProtectedBookingRoute>
        } 
      />
      
      {/* Redirect old modal-based routes to new page-based routes */}
      <Route path="/booking/*" element={<Navigate to="/book" replace />} />
    </Routes>
  );
};

export default BookingRoutes;

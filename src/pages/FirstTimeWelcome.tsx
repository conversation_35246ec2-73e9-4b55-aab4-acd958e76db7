import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import BlurFade from '@/components/BlurFade';
import { useAccountActivation } from '@/hooks/useAccountActivation';
import { CheckCircle, Loader2, Sparkles, Trophy, Users, Calendar, FileText, Shield, ExternalLink, AlertTriangle } from 'lucide-react';

const FirstTimeWelcome = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  const navigate = useNavigate();
  const { triggerActivationUpdate } = useAccountActivation();

  const handleAcceptAndActivate = async () => {
    if (!user) {
      toast({
        title: "Authentication Error",
        description: "Please log in again to continue.",
        variant: "destructive",
      });
      navigate('/login');
      return;
    }

    setIsLoading(true);

    try {
      // Get user's IP for audit trail
      const ipResponse = await fetch('https://api.ipify.org?format=json');
      const ipData = await ipResponse.json();

      // Activate the user account using the same function as LegalComplianceManager
      const { error } = await (supabase as any).rpc('activate_user_account', {
        p_user_id: user.id,
        p_privacy_accepted: true,
        p_terms_accepted: true,
        p_privacy_version: '1.0',
        p_terms_version: '1.0',
        p_ip_address: ipData.ip,
        p_user_agent: navigator.userAgent
      });

      if (error) {
        console.error('Error activating account:', error);
        throw error;
      }

      // Record legal consent for audit trail (same as LegalComplianceManager)
      await (supabase as any).rpc('record_user_consent', {
        p_user_id: user.id,
        p_consent_type: 'privacy_policy',
        p_consent_version: '1.0',
        p_consent_given: true,
        p_ip_address: ipData.ip,
        p_user_agent: navigator.userAgent,
        p_consent_method: 'legal_agreements_page'
      });

      await (supabase as any).rpc('record_user_consent', {
        p_user_id: user.id,
        p_consent_type: 'terms_of_service',
        p_consent_version: '1.0',
        p_consent_given: true,
        p_ip_address: ipData.ip,
        p_user_agent: navigator.userAgent,
        p_consent_method: 'legal_agreements_page'
      });

      // Trigger activation update to notify LegalComplianceManager
      triggerActivationUpdate();

      toast({
        title: "Legal agreements accepted",
        description: "Your account has been activated successfully. Welcome to Grid२Play!",
      });

      // Small delay to ensure state update is processed before navigation
      setTimeout(() => {
        navigate('/');
      }, 100);
    } catch (error) {
      console.error('Error handling legal consent:', error);
      toast({
        title: "Error",
        description: "There was an error processing your consent. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    // Navigate back to login or show a message
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-900 via-black to-emerald-900 text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(16,185,129,0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_49%,rgba(16,185,129,0.05)_50%,transparent_51%)] bg-[length:20px_20px]" />
      </div>

      <div className="relative z-10 container mx-auto px-4 py-8 min-h-screen">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <BlurFade delay={0.1}>
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-2 mb-6">
                <FileText className="h-8 w-8 text-emerald-400" />
                <h1 className="text-3xl md:text-4xl font-bold text-white">Legal Agreements</h1>
              </div>
              <div className="text-2xl md:text-3xl font-black text-white mb-4">
                Welcome to Grid<span className="text-emerald-400">२</span>Play!
              </div>
              <p className="text-emerald-200 max-w-2xl mx-auto">
                To provide you with the best sports booking experience, we need your agreement to our legal terms.
              </p>
            </div>
          </BlurFade>

          {/* Required Notice */}
          <BlurFade delay={0.3}>
            <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4 mb-8">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-blue-300 font-medium mb-1">Required for Account Activation</p>
                  <p className="text-gray-300 text-sm">
                    These agreements are mandatory to use Grid२Play services.
                  </p>
                </div>
              </div>
            </div>
          </BlurFade>

          {/* Legal Documents */}
          <div className="space-y-6 mb-8">
            {/* Privacy Policy */}
            <BlurFade delay={0.5}>
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-emerald-500/30">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Shield className="w-6 h-6 text-emerald-400" />
                    <h3 className="text-xl font-bold text-white">Privacy Policy</h3>
                  </div>
                  <a
                    href="/privacy"
                    target="_blank"
                    className="text-emerald-400 hover:text-emerald-300 flex items-center gap-1 text-sm"
                  >
                    Read Full Policy <ExternalLink className="w-4 h-4" />
                  </a>
                </div>
                <p className="text-gray-300 mb-4">
                  Explains how we collect, use, and protect your personal information, including booking data,
                  payment information, and communication preferences.
                </p>
                <div className="bg-black/20 rounded-lg p-4 text-sm text-gray-400 mb-4">
                  <strong>Key Points:</strong> We protect your data with encryption, never sell your information,
                  and you can access, update, or delete your data at any time.
                </div>
                <div className="py-2">
                  <span className="text-sm font-bold text-white">
                    By clicking "I Accept" below, you agree to our{' '}
                    <a
                      href="/privacy"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-emerald-400 hover:text-emerald-300 underline inline-flex items-center gap-1"
                    >
                      Privacy Policy
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  </span>
                </div>
              </div>
            </BlurFade>

            {/* Terms of Service */}
            <BlurFade delay={0.7}>
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-emerald-500/30">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <FileText className="w-6 h-6 text-blue-400" />
                    <h3 className="text-xl font-bold text-white">Terms of Service</h3>
                  </div>
                  <a
                    href="/terms"
                    target="_blank"
                    className="text-emerald-400 hover:text-emerald-300 flex items-center gap-1 text-sm"
                  >
                    Read Full Terms <ExternalLink className="w-4 h-4" />
                  </a>
                </div>
                <p className="text-gray-300 mb-4">
                  Outlines the rules and guidelines for using Grid२Play, including booking policies,
                  payment terms, cancellation rules, and user responsibilities.
                </p>
                <div className="bg-black/20 rounded-lg p-4 text-sm text-gray-400 mb-4">
                  <strong>Key Points:</strong> Fair booking policies, secure payments via Razorpay,
                  flexible cancellation options, and sports activity safety guidelines.
                </div>
                <div className="py-2">
                  <span className="text-sm font-bold text-white">
                    By clicking "I Accept" below, you agree to our{' '}
                    <a
                      href="/terms"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-emerald-400 hover:text-emerald-300 underline inline-flex items-center gap-1"
                    >
                      Terms of Service
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  </span>
                </div>
              </div>
            </BlurFade>
          </div>

          {/* Data Rights Information */}
          <BlurFade delay={0.9}>
            <div className="bg-emerald-900/20 border border-emerald-600/30 rounded-lg p-6 mb-8">
              <h4 className="font-bold text-emerald-300 mb-3 flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Your Data Rights
              </h4>
              <div className="text-sm text-gray-300 space-y-2">
                <p>• <strong>Access:</strong> View all your personal data we have</p>
                <p>• <strong>Update:</strong> Correct any inaccurate information</p>
                <p>• <strong>Delete:</strong> Request removal of your account and data</p>
                <p>• <strong>Withdraw:</strong> Change your consent preferences anytime</p>
              </div>
              <p className="text-xs text-gray-400 mt-3">
                Contact us at <a href="mailto:<EMAIL>" className="text-emerald-400 underline"><EMAIL></a> to exercise these rights.
              </p>
            </div>
          </BlurFade>

          {/* Action Buttons */}
          <BlurFade delay={1.1}>
            <div className="flex flex-col gap-4">
              <Button
                onClick={handleAcceptAndActivate}
                disabled={isLoading}
                className="w-full h-14 text-lg font-bold bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white border-0 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98]"
                style={{ minHeight: '44px' }}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin mr-2" />
                    Processing...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-5 w-5 mr-2" />
                    I Accept - Activate My Account!
                  </>
                )}
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                className="w-full h-12 text-base border-gray-600 text-gray-300 hover:bg-gray-700 rounded-xl"
                style={{ minHeight: '44px' }}
                disabled={isLoading}
              >
                Cancel
              </Button>
            </div>
          </BlurFade>

          {/* Footer Note */}
          <BlurFade delay={1.3}>
            <div className="mt-6 text-xs text-gray-400 text-center">
              By agreeing, you confirm that you have completed age verification and understand our terms.
              You can review and modify your consent preferences in your account settings.
            </div>
          </BlurFade>
        </div>
      </div>
    </div>
  );
};

export default FirstTimeWelcome;

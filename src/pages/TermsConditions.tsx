
import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Shield, Users, CreditCard, Calendar, AlertTriangle, BookOpen } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';

const TermsConditions: React.FC = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-navy-dark text-white">
      <Header />
      
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <Link 
            to="/" 
            className="inline-flex items-center text-indigo-light hover:text-indigo-dark transition-colors mb-8"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>

          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">Terms & Conditions</h1>
            <p className="text-gray-300 text-lg">
              Please read these terms carefully before using Grid2Play
            </p>
            <p className="text-sm text-gray-400 mt-2">
              Last updated: {new Date().toLocaleDateString()}
            </p>
          </div>

          {/* Quick Navigation */}
          <div className="bg-navy-light rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <BookOpen className="w-5 h-5 mr-2" />
              Quick Navigation
            </h3>
            <div className="grid md:grid-cols-2 gap-2 text-sm">
              <a href="#acceptance" className="text-indigo-light hover:text-indigo-dark">1. Acceptance of Terms</a>
              <a href="#booking" className="text-indigo-light hover:text-indigo-dark">2. Booking & Payment</a>
              <a href="#user-conduct" className="text-indigo-light hover:text-indigo-dark">3. User Conduct</a>
              <a href="#venue-responsibility" className="text-indigo-light hover:text-indigo-dark">4. Venue Responsibility</a>
              <a href="#liability" className="text-indigo-light hover:text-indigo-dark">5. Liability</a>
              <a href="#privacy" className="text-indigo-light hover:text-indigo-dark">6. Privacy & Data</a>
            </div>
          </div>

          {/* Terms Content */}
          <div className="space-y-8">
            {/* Section 1 */}
            <section id="acceptance" className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Shield className="w-6 h-6 text-green-400 mr-3" />
                1. Acceptance of Terms
              </h2>
              <div className="space-y-4 text-gray-300">
                <p>
                  By accessing and using Grid2Play ("the Platform"), you accept and agree to be bound by the terms and provision of this agreement.
                </p>
                <p>
                  If you do not agree to abide by the above, please do not use this service. Grid2Play reserves the right to modify these terms at any time without prior notice.
                </p>
              </div>
            </section>

            {/* Section 2 */}
            <section id="booking" className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Calendar className="w-6 h-6 text-indigo-400 mr-3" />
                2. Booking & Payment Terms
              </h2>
              <div className="space-y-4 text-gray-300">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Booking Process</h3>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>All bookings must be made through the Grid2Play platform</li>
                    <li>Payment confirmation is required to secure your slot</li>
                    <li>Booking confirmations will be sent via email and app notifications</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Payment Policy</h3>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>All payments are processed securely through Razorpay</li>
                    <li>No cash payments accepted at venues</li>
                    <li>Prices are subject to change without notice</li>
                    <li>Additional charges may apply for premium time slots</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Section 3 */}
            <section id="user-conduct" className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Users className="w-6 h-6 text-blue-400 mr-3" />
                3. User Conduct & Responsibilities
              </h2>
              <div className="space-y-4 text-gray-300">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Expected Behavior</h3>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Arrive on time for your booked slot</li>
                    <li>Respect venue facilities and other users</li>
                    <li>Follow all venue-specific rules and regulations</li>
                    <li>Maintain sportsman-like conduct at all times</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Prohibited Activities</h3>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Creating fake accounts or providing false information</li>
                    <li>Engaging in disruptive or inappropriate behavior</li>
                    <li>Damaging venue property or equipment</li>
                    <li>Using the platform for commercial purposes without authorization</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Section 4 */}
            <section id="venue-responsibility" className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <CreditCard className="w-6 h-6 text-purple-400 mr-3" />
                4. Venue Responsibility
              </h2>
              <div className="space-y-4 text-gray-300">
                <p>
                  Venue partners are responsible for maintaining their facilities, ensuring safety standards, and providing accurate availability information.
                </p>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Venue Obligations</h3>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Maintain clean and safe playing conditions</li>
                    <li>Honor confirmed bookings</li>
                    <li>Provide accurate facility descriptions and amenities</li>
                    <li>Communicate any schedule changes promptly</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Section 5 */}
            <section id="liability" className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <AlertTriangle className="w-6 h-6 text-yellow-400 mr-3" />
                5. Limitation of Liability
              </h2>
              <div className="space-y-4 text-gray-300">
                <p>
                  Grid2Play acts as a platform connecting users with venue partners. We are not responsible for:
                </p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Injuries sustained during sports activities</li>
                  <li>Loss or damage to personal property</li>
                  <li>Venue-specific issues or disputes</li>
                  <li>Weather-related cancellations</li>
                  <li>Third-party service failures</li>
                </ul>
                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mt-4">
                  <p className="text-yellow-200">
                    <strong>Important:</strong> Users participate in sports activities at their own risk. We strongly recommend having appropriate insurance coverage.
                  </p>
                </div>
              </div>
            </section>

            {/* Section 6 */}
            <section id="privacy" className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Shield className="w-6 h-6 text-green-400 mr-3" />
                6. Privacy & Data Protection
              </h2>
              <div className="space-y-4 text-gray-300">
                <p>
                  Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information.
                </p>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Data Usage</h3>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Personal information is used only for platform services</li>
                    <li>Payment data is securely processed by our payment partners</li>
                    <li>We may use analytics to improve our services</li>
                    <li>Marketing communications require your consent</li>
                  </ul>
                </div>
              </div>
            </section>
          </div>





          <section id="age-verification" className="bg-navy-light rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 flex items-center">
              <Users className="w-6 h-6 text-blue-400 mr-3" />
              7. Age Requirements & Verification
            </h2>
            <div className="space-y-4 text-gray-300">
              <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <h4 className="font-semibold text-blue-300 mb-2">Minimum Age Requirement</h4>
                <p className="text-sm">
                  You must be at least 18 years old to create an account and make bookings on Grid२Play.
                  Users under 18 require parental consent and supervision.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Minor User Policy</h3>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Users under 18 must have parent/guardian create and manage their account</li>
                  <li>Parental consent is required for all bookings and data processing</li>
                  <li>Parents are responsible for supervising minor users during sports activities</li>
                  <li>Special privacy protections apply to users under 18</li>
                </ul>
              </div>
            </div>
          </section>

          <section id="payment-terms" className="bg-navy-light rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 flex items-center">
              <CreditCard className="w-6 h-6 text-green-400 mr-3" />
              8. Payment Terms & Refund Policy
            </h2>
            <div className="space-y-4 text-gray-300">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Payment Processing</h3>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>All payments are processed securely through Razorpay</li>
                  <li>We accept UPI, credit/debit cards, net banking, and digital wallets</li>
                  <li>Payment confirmation is required to secure your booking</li>
                  <li>Platform fees and taxes are included in the displayed price</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Refund Policy</h3>
                <div className="bg-navy rounded-lg p-4">
                  <div className="grid md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <h4 className="font-semibold text-white mb-2">Cancellation Timeline</h4>
                      <ul className="space-y-1">
                        <li>• 24+ hours: 100% refund</li>
                        <li>• 12-24 hours: 75% refund</li>
                        <li>• 6-12 hours: 50% refund</li>
                        <li>• Less than 6 hours: No refund</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white mb-2">Refund Processing</h4>
                      <ul className="space-y-1">
                        <li>• Processed within 5-7 business days</li>
                        <li>• Refunded to original payment method</li>
                        <li>• Platform fees may be deducted</li>
                        <li>• Weather cancellations: Full refund</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Contact Section */}
          <div className="bg-emerald-900/10 border border-emerald-600/30 rounded-lg p-6 text-center mt-12">
            <h3 className="text-xl font-semibold mb-3">Questions About These Terms?</h3>
            <p className="text-gray-300 mb-4">
              If you have any questions about these Terms & Conditions, please contact our support team.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
              >
                Contact Support
              </a>
              <a
                href="tel:+919211848599"
                className="inline-flex items-center px-6 py-3 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors"
              >
                Call Us
              </a>
              <Link
                to="/privacy"
                className="inline-flex items-center px-6 py-3 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors"
              >
                Privacy Policy
              </Link>
            </div>
            <div className="mt-6 text-sm text-gray-400">
              <p><strong>DROP SHOTS SPORTS ACADEMY</strong></p>
              <p>Shop no 54 WZ-10 First Floor, Jwala Heri, Pachim Vihar, West Delhi, 110063</p>
              <p>Phone: +91 92118 48599 | Email: <EMAIL></p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default TermsConditions;

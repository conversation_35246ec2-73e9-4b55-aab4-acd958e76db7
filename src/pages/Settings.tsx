import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Settings as SettingsIcon, ArrowLeft, <PERSON>ie, Calendar, Shield, Bell, User, Lock, CheckCircle } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import AgeVerificationSettings from '../components/AgeVerificationSettings';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';

const Settings: React.FC = () => {
  const { user } = useAuth();
  const [showCookieModal, setShowCookieModal] = useState(false);
  const [activationStatus, setActivationStatus] = useState<{
    isActivated: boolean;
    isLoading: boolean;
    hasChecked: boolean;
  }>({
    isActivated: false,
    isLoading: false,
    hasChecked: false
  });

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
    if (user) {
      checkActivationStatus();
    }
  }, [user]);

  const checkActivationStatus = async () => {
    if (!user) return;

    setActivationStatus(prev => ({ ...prev, isLoading: true }));

    try {
      const { data, error } = await supabase
        .from('account_activation')
        .select('is_activated')
        .eq('user_id', user.id)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      setActivationStatus({
        isActivated: data?.is_activated || false,
        isLoading: false,
        hasChecked: true
      });
    } catch (error) {
      console.error('Error checking activation status:', error);
      setActivationStatus({
        isActivated: false,
        isLoading: false,
        hasChecked: true
      });
    }
  };

  const handleCookieSettings = () => {
    // Trigger the cookie settings modal
    window.dispatchEvent(new CustomEvent('openCookieSettings'));
  };

  const handleManualActivation = async () => {
    if (!user) return;

    try {
      const ipResponse = await fetch('https://api.ipify.org?format=json');
      const ipData = await ipResponse.json();

      const { error } = await supabase.rpc('activate_user_account', {
        p_user_id: user.id,
        p_privacy_accepted: true,
        p_terms_accepted: true,
        p_privacy_version: '1.0',
        p_terms_version: '1.0',
        p_ip_address: ipData.ip,
        p_user_agent: navigator.userAgent
      });

      if (error) throw error;

      setActivationStatus(prev => ({ ...prev, isActivated: true }));

      toast({
        title: "Account Activated",
        description: "Your account has been successfully activated.",
      });
    } catch (error) {
      console.error('Error activating account:', error);
      toast({
        title: "Activation Failed",
        description: "There was an error activating your account. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-navy-dark text-white">
      <Header />
      
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <Link 
            to="/" 
            className="inline-flex items-center text-emerald-400 hover:text-emerald-300 transition-colors mb-8"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>

          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4 flex items-center justify-center gap-3">
              <SettingsIcon className="w-10 h-10 text-emerald-400" />
              Privacy & Settings
            </h1>
            <p className="text-gray-300 text-lg max-w-3xl mx-auto">
              Manage your privacy preferences, age verification, and account settings for Grid२Play.
            </p>
          </div>

          {/* Settings Sections */}
          <div className="space-y-8">
            {/* Account Status Section */}
            {user && (
              <section className="bg-navy-light rounded-lg p-6">
                <h2 className="text-2xl font-bold mb-6 flex items-center">
                  <CheckCircle className="w-6 h-6 text-emerald-400 mr-3" />
                  Account Status
                </h2>

                <div className="bg-navy rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-semibold text-white mb-1">Account Activation</h3>
                      <p className="text-gray-300 text-sm">
                        Your account activation status determines if legal agreements are required.
                      </p>
                    </div>
                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                      activationStatus.isActivated
                        ? 'bg-green-900/30 text-green-400 border border-green-600/30'
                        : 'bg-red-900/30 text-red-400 border border-red-600/30'
                    }`}>
                      {activationStatus.isLoading ? 'Checking...' :
                       activationStatus.isActivated ? 'Activated' : 'Not Activated'}
                    </div>
                  </div>

                  {!activationStatus.isActivated && activationStatus.hasChecked && (
                    <div className="mt-4">
                      <p className="text-yellow-300 text-sm mb-3">
                        Your account is not activated. This means you'll see legal agreement modals until you complete them.
                      </p>
                      <button
                        onClick={handleManualActivation}
                        className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors min-h-[44px]"
                      >
                        Activate Account Manually
                      </button>
                    </div>
                  )}

                  {activationStatus.isActivated && (
                    <div className="mt-4 text-green-300 text-sm">
                      ✅ Your account is activated. You won't see legal agreement modals anymore.
                    </div>
                  )}
                </div>
              </section>
            )}

            {/* Privacy & Legal Section */}
            <section className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-6 flex items-center">
                <Shield className="w-6 h-6 text-emerald-400 mr-3" />
                Privacy & Legal
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-navy rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Cookie className="w-5 h-5 text-blue-400" />
                    <h3 className="font-semibold text-white">Cookie Preferences</h3>
                  </div>
                  <p className="text-gray-300 text-sm mb-4">
                    Manage your cookie preferences and control how we use cookies on Grid२Play.
                  </p>
                  <button
                    onClick={handleCookieSettings}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors min-h-[44px]"
                  >
                    Manage Cookie Settings
                  </button>
                </div>

                <div className="bg-navy rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Lock className="w-5 h-5 text-purple-400" />
                    <h3 className="font-semibold text-white">Legal Documents</h3>
                  </div>
                  <p className="text-gray-300 text-sm mb-4">
                    Review our privacy policy, terms of service, and other legal documents.
                  </p>
                  <div className="space-y-2">
                    <Link 
                      to="/privacy" 
                      className="block w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center min-h-[44px] flex items-center justify-center"
                    >
                      Privacy Policy
                    </Link>
                    <Link 
                      to="/terms" 
                      className="block w-full px-4 py-2 bg-navy border border-purple-600/30 text-white rounded-lg hover:bg-navy-light transition-colors text-center min-h-[44px] flex items-center justify-center"
                    >
                      Terms of Service
                    </Link>
                  </div>
                </div>
              </div>
            </section>

            {/* Age Verification Section */}
            <section>
              <h2 className="text-2xl font-bold mb-6 flex items-center">
                <Calendar className="w-6 h-6 text-emerald-400 mr-3" />
                Age Verification (Optional)
              </h2>
              <AgeVerificationSettings />
            </section>

            {/* Account Settings Section */}
            <section className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-6 flex items-center">
                <User className="w-6 h-6 text-emerald-400 mr-3" />
                Account Settings
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-navy rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <User className="w-5 h-5 text-emerald-400" />
                    <h3 className="font-semibold text-white">Profile Management</h3>
                  </div>
                  <p className="text-gray-300 text-sm mb-4">
                    Update your profile information, sports preferences, and account details.
                  </p>
                  <Link
                    to="/profile"
                    className="block w-full px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors text-center min-h-[44px] flex items-center justify-center"
                  >
                    Manage Profile
                  </Link>
                </div>

                <div className="bg-navy rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Bell className="w-5 h-5 text-orange-400" />
                    <h3 className="font-semibold text-white">Notifications</h3>
                  </div>
                  <p className="text-gray-300 text-sm mb-4">
                    Control your notification preferences for bookings, promotions, and updates.
                  </p>
                  <button
                    className="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors min-h-[44px]"
                    disabled
                  >
                    Coming Soon
                  </button>
                </div>
              </div>
            </section>

            {/* Data Rights Section */}
            <section className="bg-emerald-900/10 border border-emerald-600/30 rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-6 flex items-center">
                <Shield className="w-6 h-6 text-emerald-400 mr-3" />
                Your Data Rights
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-white mb-3">Data Access & Control</h3>
                  <ul className="text-gray-300 space-y-2 text-sm">
                    <li>• <strong>Access:</strong> View all your personal data we have</li>
                    <li>• <strong>Update:</strong> Correct any inaccurate information</li>
                    <li>• <strong>Delete:</strong> Request removal of your account and data</li>
                    <li>• <strong>Withdraw:</strong> Change your consent preferences anytime</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-semibold text-white mb-3">Contact for Data Rights</h3>
                  <p className="text-gray-300 text-sm mb-4">
                    To exercise your data rights or for any privacy-related questions, contact our support team.
                  </p>
                  <div className="space-y-2">
                    <a 
                      href="mailto:<EMAIL>" 
                      className="block w-full px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors text-center min-h-[44px] flex items-center justify-center"
                    >
                      Email Support
                    </a>
                    <a 
                      href="tel:+************" 
                      className="block w-full px-4 py-2 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors text-center min-h-[44px] flex items-center justify-center"
                    >
                      Call +91 92118 48599
                    </a>
                  </div>
                </div>
              </div>
            </section>

            {/* Help & Support */}
            <section className="bg-navy-light rounded-lg p-6 text-center">
              <h3 className="text-xl font-semibold mb-3">Need Help?</h3>
              <p className="text-gray-300 mb-4">
                If you have questions about these settings or need assistance, our support team is here to help.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link 
                  to="/help" 
                  className="inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
                >
                  Help Center
                </Link>
                <Link 
                  to="/contact" 
                  className="inline-flex items-center px-6 py-3 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors"
                >
                  Contact Support
                </Link>
                <Link 
                  to="/faq" 
                  className="inline-flex items-center px-6 py-3 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors"
                >
                  FAQ
                </Link>
              </div>
            </section>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Settings;

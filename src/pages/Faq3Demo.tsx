
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { HelpCircle, ArrowLeft, Search, ChevronDown, ChevronUp, Phone, Mail, MessageSquare, Calendar, CreditCard, Users, MapPin, Clock, Shield } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';

interface FAQItem {
  id: string;
  category: string;
  question: string;
  answer: string;
}

const FAQ: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [openItems, setOpenItems] = useState<string[]>([]);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const faqData: FAQItem[] = [
    // Booking & Reservations
    {
      id: 'booking-1',
      category: 'booking',
      question: 'How far in advance can I book a sports venue?',
      answer: 'Regular users can book up to 14 days in advance, while venue admins can book up to 30 days ahead. Popular time slots fill up quickly, especially during peak hours and weekends, so we recommend booking early to secure your preferred time.'
    },
    {
      id: 'booking-2',
      category: 'booking',
      question: 'Can I book multiple consecutive time slots?',
      answer: 'Yes! Grid२Play allows you to book multiple consecutive time slots for extended play sessions. Simply select your starting time and choose how many consecutive hours you need. You cannot book non-consecutive slots in a single booking.'
    },
    {
      id: 'booking-3',
      category: 'booking',
      question: 'What happens if I need to cancel my booking?',
      answer: 'Our cancellation policy is based on timing: 24+ hours before = 100% refund, 12-24 hours = 75% refund, 6-12 hours = 50% refund, less than 6 hours = no refund. Weather-related cancellations receive full refunds.'
    },
    {
      id: 'booking-4',
      category: 'booking',
      question: 'Can I modify my booking after confirmation?',
      answer: 'You can modify your booking (change time, date, or add participants) up to 6 hours before your scheduled slot, subject to availability. Contact our support team at +91 92118 48599 for assistance with modifications.'
    },

    // Payments & Refunds
    {
      id: 'payment-1',
      category: 'payment',
      question: 'What payment methods do you accept?',
      answer: 'We accept all major payment methods through Razorpay: UPI (Google Pay, PhonePe, Paytm), Credit/Debit Cards (Visa, Mastercard, RuPay), Net Banking (all major banks), and Digital Wallets (Paytm, Amazon Pay, etc.).'
    },
    {
      id: 'payment-2',
      category: 'payment',
      question: 'Is my payment information secure?',
      answer: 'Absolutely! All payments are processed through Razorpay, a PCI DSS compliant payment gateway. Grid२Play never stores your complete payment card details. All transactions use SSL encryption for maximum security.'
    },
    {
      id: 'payment-3',
      category: 'payment',
      question: 'How long do refunds take to process?',
      answer: 'Refunds are processed within 5-7 business days and credited back to your original payment method. You will receive an email notification once the refund is initiated. Bank processing times may vary.'
    },
    {
      id: 'payment-4',
      category: 'payment',
      question: 'Are there any additional fees or charges?',
      answer: 'The price displayed includes all platform fees and applicable taxes. There are no hidden charges. However, payment gateway charges (if any) are as per Razorpay\'s standard rates.'
    },

    // Account & Profile
    {
      id: 'account-1',
      category: 'account',
      question: 'How do I create an account on Grid२Play?',
      answer: 'Registration is simple and phone-first: Enter your mobile number, verify with SMS OTP, complete your profile with name and sports preferences. Email verification is optional but recommended for booking confirmations.'
    },
    {
      id: 'account-2',
      category: 'account',
      question: 'Can I change my registered phone number?',
      answer: 'For security reasons, verified phone numbers cannot be changed directly. Please contact our support <NAME_EMAIL> with your request and proper verification documents.'
    },
    {
      id: 'account-3',
      category: 'account',
      question: 'How do I view my booking history?',
      answer: 'Go to your Profile section and click on "My Bookings" to view all past and upcoming reservations. You can also download booking receipts and track payment status from this section.'
    },
    {
      id: 'account-4',
      category: 'account',
      question: 'Can I delete my account and data?',
      answer: 'Yes, you have the right to delete your account and personal data. Contact <NAME_EMAIL> with your deletion request. Note that some transaction records may be retained for legal compliance.'
    },

    // Venues & Sports
    {
      id: 'venue-1',
      category: 'venue',
      question: 'How do I find venues near my location?',
      answer: 'Grid२Play automatically detects your location (with permission) and shows nearby venues. You can also manually search by area, venue name, or sport type using our search and filter options.'
    },
    {
      id: 'venue-2',
      category: 'venue',
      question: 'What sports are available on Grid२Play?',
      answer: 'We offer a wide range of sports including Badminton, Tennis, Cricket, Football, Basketball, Swimming, Squash, Table Tennis, and more. Available sports vary by venue and location.'
    },
    {
      id: 'venue-3',
      category: 'venue',
      question: 'How do I know if a venue has good facilities?',
      answer: 'Each venue page includes photos, amenities list, user reviews, and ratings. We also provide information about equipment availability, parking, changing rooms, and other facilities.'
    },
    {
      id: 'venue-4',
      category: 'venue',
      question: 'Can I visit a venue before booking?',
      answer: 'Yes! Most venues welcome visits during their operating hours. Contact the venue directly using the information provided on their Grid२Play page, or call our support team for assistance.'
    },

    // Technical Support
    {
      id: 'technical-1',
      category: 'technical',
      question: 'Is there a mobile app for Grid२Play?',
      answer: 'Grid२Play is optimized for mobile browsers and works seamlessly on all devices. We\'re currently developing a dedicated mobile app for iOS and Android. Stay tuned for updates!'
    },
    {
      id: 'technical-2',
      category: 'technical',
      question: 'I\'m having trouble with the website. What should I do?',
      answer: 'Try refreshing the page, clearing your browser cache, or using a different browser. If issues persist, contact our technical support at +91 92118 48599 or <EMAIL> with details about the problem.'
    },
    {
      id: 'technical-3',
      category: 'technical',
      question: 'Do I need to download anything to use Grid२Play?',
      answer: 'No downloads required! Grid२Play works directly in your web browser on any device - desktop, tablet, or mobile. Just visit our website and start booking.'
    },

    // General
    {
      id: 'general-1',
      category: 'general',
      question: 'What is Grid२Play and how does it work?',
      answer: 'Grid२Play is India\'s premier sports venue booking platform operated by DROP SHOTS SPORTS ACADEMY. We connect sports enthusiasts with quality venues across the country, making it easy to find and book sports facilities online.'
    },
    {
      id: 'general-2',
      category: 'general',
      question: 'What are your customer support hours?',
      answer: 'Our support team is available Monday through Sunday, 9:00 AM to 9:00 PM IST. For urgent booking issues, call +91 92118 48599. For general inquiries, email <EMAIL>.'
    },
    {
      id: 'general-3',
      category: 'general',
      question: 'Do you offer corporate or group booking packages?',
      answer: 'Yes! We offer special packages for corporate events, team practices, tournaments, and group bookings. Contact our sales <NAME_EMAIL> for customized packages and bulk booking discounts.'
    }
  ];

  const categories = [
    { id: 'all', name: 'All Categories', icon: HelpCircle },
    { id: 'booking', name: 'Booking & Reservations', icon: Calendar },
    { id: 'payment', name: 'Payments & Refunds', icon: CreditCard },
    { id: 'account', name: 'Account & Profile', icon: Users },
    { id: 'venue', name: 'Venues & Sports', icon: MapPin },
    { id: 'technical', name: 'Technical Support', icon: Shield },
    { id: 'general', name: 'General', icon: HelpCircle }
  ];

  const filteredFAQs = faqData.filter(faq => {
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const toggleItem = (id: string) => {
    setOpenItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <div className="min-h-screen bg-navy-dark text-white">
      <Header />

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <Link
            to="/"
            className="inline-flex items-center text-emerald-400 hover:text-emerald-300 transition-colors mb-8"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>

          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4 flex items-center justify-center gap-3">
              <HelpCircle className="w-10 h-10 text-emerald-400" />
              Frequently Asked Questions
            </h1>
            <p className="text-gray-300 text-lg max-w-3xl mx-auto">
              Everything you need to know about booking sports venues with Grid२Play.
              Can't find what you're looking for? Our support team is here to help.
            </p>
            <div className="mt-4 text-sm text-gray-400">
              <p><strong>Business:</strong> DROP SHOTS SPORTS ACADEMY</p>
              <p><strong>Total Questions:</strong> {faqData.length} answered</p>
            </div>
          </div>

          {/* Search and Filter */}
          <div className="bg-navy-light rounded-lg p-6 mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search FAQs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 bg-navy border border-gray-600 rounded-lg text-white focus:border-emerald-500 focus:outline-none"
                  />
                </div>
              </div>
              <div className="md:w-64">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 bg-navy border border-gray-600 rounded-lg text-white focus:border-emerald-500 focus:outline-none"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="mt-4 text-sm text-gray-400">
              Showing {filteredFAQs.length} of {faqData.length} questions
            </div>
          </div>

          {/* Category Tabs */}
          <div className="flex flex-wrap gap-2 mb-8">
            {categories.map(category => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-emerald-600 text-white'
                      : 'bg-navy-light text-gray-300 hover:bg-navy hover:text-white'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {category.name}
                </button>
              );
            })}
          </div>

          {/* FAQ Items */}
          <div className="space-y-4 mb-12">
            {filteredFAQs.length === 0 ? (
              <div className="text-center py-12 bg-navy-light rounded-lg">
                <HelpCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">No FAQs Found</h3>
                <p className="text-gray-300 mb-4">
                  Try adjusting your search terms or category filter.
                </p>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('all');
                  }}
                  className="text-emerald-400 hover:text-emerald-300 underline"
                >
                  Clear Filters
                </button>
              </div>
            ) : (
              filteredFAQs.map(faq => (
                <div key={faq.id} className="bg-navy-light rounded-lg overflow-hidden">
                  <button
                    onClick={() => toggleItem(faq.id)}
                    className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-navy transition-colors"
                  >
                    <h3 className="font-semibold text-white pr-4">{faq.question}</h3>
                    {openItems.includes(faq.id) ? (
                      <ChevronUp className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-400 flex-shrink-0" />
                    )}
                  </button>
                  {openItems.includes(faq.id) && (
                    <div className="px-6 pb-4">
                      <div className="border-t border-gray-600 pt-4">
                        <p className="text-gray-300 leading-relaxed">{faq.answer}</p>
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>

          {/* Contact Support Section */}
          <div className="bg-emerald-900/10 border border-emerald-600/30 rounded-lg p-8 text-center">
            <h3 className="text-2xl font-bold mb-3 flex items-center justify-center gap-2">
              <MessageSquare className="w-6 h-6 text-emerald-400" />
              Still Need Help?
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Can't find the answer you're looking for? Our support team is ready to help with any questions
              about bookings, payments, or technical issues.
            </p>

            <div className="grid md:grid-cols-3 gap-4 mb-6">
              <a
                href="tel:+919211848599"
                className="flex flex-col items-center p-4 bg-navy-light rounded-lg hover:bg-navy transition-colors"
              >
                <Phone className="w-8 h-8 text-emerald-400 mb-2" />
                <h4 className="font-semibold text-white mb-1">Call Us</h4>
                <p className="text-sm text-gray-300">+91 92118 48599</p>
                <p className="text-xs text-gray-400 mt-1">Mon-Sun: 9 AM - 9 PM</p>
              </a>

              <a
                href="mailto:<EMAIL>"
                className="flex flex-col items-center p-4 bg-navy-light rounded-lg hover:bg-navy transition-colors"
              >
                <Mail className="w-8 h-8 text-blue-400 mb-2" />
                <h4 className="font-semibold text-white mb-1">Email Us</h4>
                <p className="text-sm text-gray-300"><EMAIL></p>
                <p className="text-xs text-gray-400 mt-1">Response within 2 hours</p>
              </a>

              <Link
                to="/contact"
                className="flex flex-col items-center p-4 bg-navy-light rounded-lg hover:bg-navy transition-colors"
              >
                <MessageSquare className="w-8 h-8 text-purple-400 mb-2" />
                <h4 className="font-semibold text-white mb-1">Contact Form</h4>
                <p className="text-sm text-gray-300">Detailed inquiries</p>
                <p className="text-xs text-gray-400 mt-1">Get personalized help</p>
              </Link>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link
                to="/help"
                className="inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
              >
                <HelpCircle className="w-4 h-4 mr-2" />
                Visit Help Center
              </Link>
              <Link
                to="/contact"
                className="inline-flex items-center px-6 py-3 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors"
              >
                <Mail className="w-4 h-4 mr-2" />
                Contact Support
              </Link>
            </div>

            <div className="mt-6 text-sm text-gray-400">
              <p><strong>DROP SHOTS SPORTS ACADEMY</strong></p>
              <p>Shop no 54 WZ-10 First Floor, Jwala Heri, Pachim Vihar, West Delhi, 110063</p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default FAQ;

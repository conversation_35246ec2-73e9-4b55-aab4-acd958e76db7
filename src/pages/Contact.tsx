
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Mail, Phone, Send, ArrowLeft, MapPin, MessageSquare, Clock, CheckCircle, AlertCircle, User, FileText, Headphones } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';

const Contact: React.FC = () => {
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    category: 'general',
    message: ''
  });

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Simulate form submission
    setTimeout(() => {
      setSubmitted(true);
      setLoading(false);
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-navy-dark text-white">
      <Header />

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <Link
            to="/"
            className="inline-flex items-center text-emerald-400 hover:text-emerald-300 transition-colors mb-8"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>

          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4 flex items-center justify-center gap-3">
              <Mail className="w-10 h-10 text-emerald-400" />
              Contact Us
            </h1>
            <p className="text-gray-300 text-lg max-w-3xl mx-auto">
              Get in touch with the Grid२Play support team. We're here to help with bookings, payments,
              technical issues, and any questions about our sports venue platform.
            </p>
            <div className="mt-4 text-sm text-gray-400">
              <p><strong>Business:</strong> DROP SHOTS SPORTS ACADEMY</p>
              <p><strong>Response Time:</strong> Usually within 2 hours during business hours</p>
            </div>
          </div>
          {/* Contact Methods */}
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            <div className="bg-navy-light rounded-lg p-6 text-center">
              <Phone className="w-12 h-12 text-emerald-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Phone Support</h3>
              <p className="text-gray-300 text-sm mb-4">
                Get immediate assistance for urgent booking or payment issues
              </p>
              <a
                href="tel:+************"
                className="inline-flex items-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
              >
                <Phone className="w-4 h-4 mr-2" />
                +91 92118 48599
              </a>
              <div className="mt-3 text-xs text-gray-400">
                <Clock className="w-3 h-3 inline mr-1" />
                Mon-Sun: 9 AM - 9 PM IST
              </div>
            </div>

            <div className="bg-navy-light rounded-lg p-6 text-center">
              <Mail className="w-12 h-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Email Support</h3>
              <p className="text-gray-300 text-sm mb-4">
                Send detailed inquiries and get comprehensive responses
              </p>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Mail className="w-4 h-4 mr-2" />
                Email Us
              </a>
              <div className="mt-3 text-xs text-gray-400">
                <Clock className="w-3 h-3 inline mr-1" />
                Response within 2 hours
              </div>
            </div>

            <div className="bg-navy-light rounded-lg p-6 text-center">
              <MessageSquare className="w-12 h-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Live Chat</h3>
              <p className="text-gray-300 text-sm mb-4">
                Chat with our support team in real-time
              </p>
              <button className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                <MessageSquare className="w-4 h-4 mr-2" />
                Start Chat
              </button>
              <div className="mt-3 text-xs text-gray-400">
                <Clock className="w-3 h-3 inline mr-1" />
                Available 24/7
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Contact Form */}
            <div className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-6 flex items-center">
                <FileText className="w-6 h-6 text-emerald-400 mr-3" />
                Send us a Message
              </h2>

              {submitted ? (
                <div className="text-center py-8">
                  <CheckCircle className="w-16 h-16 text-emerald-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">Message Sent Successfully!</h3>
                  <p className="text-gray-300 mb-4">
                    Thank you for contacting Grid२Play. Our support team will get back to you within 2 hours during business hours.
                  </p>
                  <button
                    onClick={() => setSubmitted(false)}
                    className="text-emerald-400 hover:text-emerald-300 underline"
                  >
                    Send Another Message
                  </button>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                        Full Name *
                      </label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={form.name}
                          onChange={handleChange}
                          required
                          className="w-full pl-10 pr-3 py-2 bg-navy border border-gray-600 rounded-lg text-white focus:border-emerald-500 focus:outline-none"
                          placeholder="Your full name"
                        />
                      </div>
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                        Email Address *
                      </label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={form.email}
                          onChange={handleChange}
                          required
                          className="w-full pl-10 pr-3 py-2 bg-navy border border-gray-600 rounded-lg text-white focus:border-emerald-500 focus:outline-none"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
                        Phone Number
                      </label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={form.phone}
                          onChange={handleChange}
                          className="w-full pl-10 pr-3 py-2 bg-navy border border-gray-600 rounded-lg text-white focus:border-emerald-500 focus:outline-none"
                          placeholder="+91 XXXXX XXXXX"
                        />
                      </div>
                    </div>
                    <div>
                      <label htmlFor="category" className="block text-sm font-medium text-gray-300 mb-2">
                        Category
                      </label>
                      <select
                        id="category"
                        name="category"
                        value={form.category}
                        onChange={handleChange}
                        className="w-full px-3 py-2 bg-navy border border-gray-600 rounded-lg text-white focus:border-emerald-500 focus:outline-none"
                      >
                        <option value="general">General Inquiry</option>
                        <option value="booking">Booking Support</option>
                        <option value="payment">Payment Issues</option>
                        <option value="technical">Technical Support</option>
                        <option value="feedback">Feedback</option>
                        <option value="partnership">Partnership</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-300 mb-2">
                      Subject *
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={form.subject}
                      onChange={handleChange}
                      required
                      className="w-full px-3 py-2 bg-navy border border-gray-600 rounded-lg text-white focus:border-emerald-500 focus:outline-none"
                      placeholder="Brief description of your inquiry"
                    />
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={form.message}
                      onChange={handleChange}
                      required
                      rows={5}
                      className="w-full px-3 py-2 bg-navy border border-gray-600 rounded-lg text-white focus:border-emerald-500 focus:outline-none"
                      placeholder="Please provide details about your inquiry..."
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full flex items-center justify-center gap-2 bg-emerald-600 px-6 py-3 rounded-lg text-white font-semibold hover:bg-emerald-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        Send Message
                      </>
                    )}
                  </button>
                </form>
              )}
            </div>

            {/* Office Information */}
            <div className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-6 flex items-center">
                <MapPin className="w-6 h-6 text-red-400 mr-3" />
                Visit Our Office
              </h2>

              <div className="space-y-6">
                <div className="bg-navy rounded-lg p-4">
                  <h3 className="font-semibold text-white mb-3">DROP SHOTS SPORTS ACADEMY</h3>
                  <div className="text-gray-300 space-y-1">
                    <p>Shop no 54 WZ-10 First Floor</p>
                    <p>Jwala Heri, Pachim Vihar</p>
                    <p>West Delhi, 110063</p>
                    <p className="text-emerald-400 font-medium">India</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-emerald-400" />
                    <div>
                      <p className="text-white font-medium">Phone Support</p>
                      <a href="tel:+************" className="text-emerald-400 hover:text-emerald-300">
                        +91 92118 48599
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-blue-400" />
                    <div>
                      <p className="text-white font-medium">Email Support</p>
                      <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-purple-400" />
                    <div>
                      <p className="text-white font-medium">Business Hours</p>
                      <p className="text-gray-300">Monday - Sunday</p>
                      <p className="text-gray-300">9:00 AM - 9:00 PM IST</p>
                    </div>
                  </div>
                </div>

                <div className="bg-emerald-900/20 border border-emerald-600/30 rounded-lg p-4">
                  <h4 className="font-semibold text-emerald-300 mb-2">Quick Response Guarantee</h4>
                  <p className="text-sm text-gray-300">
                    We respond to all inquiries within 2 hours during business hours.
                    For urgent booking issues, please call us directly.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Help Resources */}
          <div className="mt-12 bg-emerald-900/10 border border-emerald-600/30 rounded-lg p-6 text-center">
            <h3 className="text-xl font-semibold mb-3 flex items-center justify-center gap-2">
              <Headphones className="w-5 h-5 text-emerald-400" />
              Need Immediate Help?
            </h3>
            <p className="text-gray-300 mb-4">
              Check out our other support resources for quick answers
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link
                to="/help"
                className="inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
              >
                <FileText className="w-4 h-4 mr-2" />
                Help Center
              </Link>
              <Link
                to="/faq"
                className="inline-flex items-center px-6 py-3 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors"
              >
                <FileText className="w-4 h-4 mr-2" />
                FAQ
              </Link>
              <a
                href="tel:+************"
                className="inline-flex items-center px-6 py-3 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors"
              >
                <Phone className="w-4 h-4 mr-2" />
                Call Now
              </a>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Contact;

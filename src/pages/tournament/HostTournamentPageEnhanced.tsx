import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeftIcon, TrophyIcon, InfoIcon, HomeIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { HostTournamentFormEnhanced } from '@/components/tournament/HostTournamentFormEnhanced';

export function HostTournamentPageEnhanced() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Header */}
      <div className="bg-gray-900 border-b border-gray-800 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/')}
                className="flex items-center gap-2 text-white hover:text-emerald-400"
              >
                <HomeIcon className="h-4 w-4" />
                <span className="hidden sm:inline">Grid२Play Home</span>
              </Button>

              <Button
                variant="ghost"
                onClick={() => navigate('/tournaments')}
                className="flex items-center gap-2 text-white hover:text-emerald-400"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                <span className="hidden sm:inline">Tournament Hub</span>
              </Button>
            </div>

            <div className="flex items-center gap-2 text-emerald-400">
              <TrophyIcon className="h-5 w-5" />
              <span className="font-semibold">Host Tournament</span>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-600 to-emerald-700 text-white py-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Host Your Tournament
            </h1>
            <p className="text-xl text-emerald-100 max-w-2xl mx-auto">
              Create and manage professional tournaments with Grid२Play's advanced tournament platform. 
              Reach thousands of players and build your sports community.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-8 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="text-center h-full bg-gray-800 border-gray-700">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-emerald-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <TrophyIcon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-white mb-2">Professional Management</h3>
                  <p className="text-sm text-gray-300">
                    Automated bracket generation, match scheduling, and result tracking
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="text-center h-full bg-gray-800 border-gray-700">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <InfoIcon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-white mb-2">Wide Reach</h3>
                  <p className="text-sm text-gray-300">
                    Access to thousands of registered players across India
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="text-center h-full bg-gray-800 border-gray-700">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-white font-bold text-xl">₹</span>
                  </div>
                  <h3 className="font-semibold text-white mb-2">Revenue Sharing</h3>
                  <p className="text-sm text-gray-300">
                    Transparent revenue sharing with competitive platform fees
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Important Information */}
      <section className="py-6 bg-black">
        <div className="container mx-auto px-4">
          <Alert>
            <InfoIcon className="h-4 w-4" />
            <AlertDescription>
              <strong>Tournament Approval Process:</strong> All tournaments go through a review process to ensure quality and compliance. 
              You'll receive an email confirmation once your tournament is approved and published.
            </AlertDescription>
          </Alert>
        </div>
      </section>

      {/* Tournament Creation Form */}
      <section className="py-8">
        <HostTournamentFormEnhanced />
      </section>

      {/* Support Section */}
      <section className="py-12 bg-emerald-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold mb-4">Need Help?</h2>
            <p className="text-emerald-100 mb-6 max-w-2xl mx-auto">
              Our tournament experts are here to help you create successful tournaments. 
              Get guidance on format selection, pricing strategies, and promotion.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                variant="outline" 
                className="border-white text-white hover:bg-white hover:text-emerald-900"
              >
                Contact Support
              </Button>
              <Button 
                variant="outline" 
                className="border-white text-white hover:bg-white hover:text-emerald-900"
              >
                View Tournament Guide
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}

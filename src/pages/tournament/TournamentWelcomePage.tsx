import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  TrophyIcon,
  UsersIcon,
  CalendarIcon,
  MapPinIcon,
  StarIcon,
  PlayIcon,
  PlusIcon,
  SearchIcon,
  FilterIcon,
  TrendingUpIcon,
  AwardIcon,
  ZapIcon,
  ArrowLeftIcon,
  HomeIcon
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useEnhancedTournaments } from '@/hooks/use-tournament-enhanced';
import { TournamentEnhancedCard } from '@/components/tournament/TournamentEnhancedCard';
import { TournamentFilters } from '@/components/tournament/TournamentFilters';
import { TournamentStats } from '@/components/tournament/TournamentStats';

export function TournamentWelcomePage() {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: ['registration_open', 'upcoming'],
    featured_only: false,
  });

  const { data: tournaments = [], isLoading } = useEnhancedTournaments({
    ...filters,
    search_query: searchQuery,
  });

  const featuredTournaments = tournaments.filter(t => t.featured).slice(0, 3);
  const upcomingTournaments = tournaments.filter(t =>
    t.status === 'registration_open' || t.status === 'published'
  ).slice(0, 6);

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Header with Back Button */}
      <div className="bg-gray-900 border-b border-gray-800 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => navigate('/')}
              className="flex items-center gap-2 text-white hover:text-emerald-400"
            >
              <HomeIcon className="h-4 w-4" />
              <span className="hidden sm:inline">Back to Grid२Play</span>
              <span className="sm:hidden">Home</span>
            </Button>

            <div className="text-emerald-400 text-sm font-medium">
              Grid२Play Tournament Hub
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-emerald-600 via-emerald-500 to-emerald-600">
        {/* Background Video */}
        <div className="absolute inset-0">
          <video
            autoPlay
            loop
            muted
            playsInline
            preload="metadata"
            className="w-full h-full object-cover"
            poster="/tournament-bg.jpg"
            onError={(e) => {
              // Fallback to background image if video fails to load
              const target = e.target as HTMLVideoElement;
              target.style.display = 'none';
              const fallback = target.nextElementSibling as HTMLElement;
              if (fallback) fallback.style.display = 'block';
            }}
          >
            <source
              src="https://lrtirloetmulgmdxnusl.supabase.co/storage/v1/object/public/vedios//mixkit-one-on-one-in-a-soccer-game-43483-full-hd%20(1).mp4"
              type="video/mp4"
            />
          </video>
          {/* Fallback background image */}
          <div
            className="w-full h-full bg-[url('/tournament-bg.jpg')] bg-cover bg-center hidden"
            style={{ display: 'none' }}
          ></div>
        </div>

        {/* Video Overlay for Text Readability */}
        <div className="absolute inset-0 bg-black/50"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-900/30 via-emerald-800/20 to-emerald-900/30"></div>
        
        <div className="relative container mx-auto px-4 py-20 z-20">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center text-white"
          >
            <div className="mb-4">
              <div className="text-emerald-200 text-lg md:text-xl font-medium mb-2">
                Grid२Play
              </div>
              <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-emerald-200 bg-clip-text text-transparent">
                Tournament Hub
              </h1>
            </div>
            <p className="text-xl md:text-2xl mb-8 text-emerald-100 max-w-3xl mx-auto">
              Join the ultimate sports competition platform. Compete, win, and become a champion in your favorite sports.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button 
                size="lg" 
                className="bg-white text-emerald-900 hover:bg-emerald-50 px-8 py-4 text-lg font-semibold"
                onClick={() => navigate('/tournaments/browse')}
              >
                <TrophyIcon className="mr-2 h-5 w-5" />
                Browse Tournaments
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="border-white text-white hover:bg-white hover:text-emerald-900 px-8 py-4 text-lg font-semibold"
                onClick={() => navigate('/tournaments/host')}
              >
                <PlusIcon className="mr-2 h-5 w-5" />
                Host Tournament
              </Button>
            </div>

            {/* Quick Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-2">500+</div>
                <div className="text-emerald-200">Active Tournaments</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-2">10K+</div>
                <div className="text-emerald-200">Players</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-2">50+</div>
                <div className="text-emerald-200">Venues</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-2">₹5L+</div>
                <div className="text-emerald-200">Prize Money</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-8 bg-gray-900 border-b border-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  placeholder="Search tournaments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-12 bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-emerald-500"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="h-12 border-gray-700 text-white hover:bg-gray-800 hover:text-emerald-400"
              >
                <FilterIcon className="mr-2 h-4 w-4" />
                Filters
              </Button>

              <Button
                variant={filters.featured_only ? "default" : "outline"}
                onClick={() => setFilters(prev => ({ ...prev, featured_only: !prev.featured_only }))}
                className={`h-12 ${filters.featured_only
                  ? 'bg-emerald-600 hover:bg-emerald-700 text-white'
                  : 'border-gray-700 text-white hover:bg-gray-800 hover:text-emerald-400'
                }`}
              >
                <StarIcon className="mr-2 h-4 w-4" />
                Featured
              </Button>
            </div>
          </div>
          
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4"
            >
              <TournamentFilters filters={filters} onFiltersChange={setFilters} />
            </motion.div>
          )}
        </div>
      </section>

      {/* Featured Tournaments */}
      {featuredTournaments.length > 0 && (
        <section className="py-12 bg-gray-900">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="flex items-center justify-between mb-8">
                <div>
                  <h2 className="text-3xl font-bold text-white mb-2">Featured Tournaments</h2>
                  <p className="text-gray-300">Don't miss these exciting competitions</p>
                </div>
                <Badge variant="secondary" className="bg-emerald-600 text-white">
                  <StarIcon className="mr-1 h-3 w-3" />
                  Premium Events
                </Badge>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredTournaments.map((tournament) => (
                  <motion.div
                    key={tournament.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.1 }}
                  >
                    <TournamentEnhancedCard tournament={tournament} featured />
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </section>
      )}

      {/* Tournament Categories */}
      <section className="py-12 bg-black">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-center text-white mb-12">
              Tournament Categories
            </h2>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {[
                { name: 'Cricket', icon: '🏏', count: 45, color: 'bg-blue-500' },
                { name: 'Football', icon: '⚽', count: 32, color: 'bg-green-500' },
                { name: 'Badminton', icon: '🏸', count: 28, color: 'bg-purple-500' },
                { name: 'Tennis', icon: '🎾', count: 21, color: 'bg-orange-500' },
              ].map((category, index) => (
                <motion.div
                  key={category.name}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className="hover:shadow-lg hover:shadow-emerald-500/20 transition-all cursor-pointer group bg-gray-800 border-gray-700">
                    <CardContent className="p-6 text-center">
                      <div className={`w-16 h-16 ${category.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>
                        <span className="text-2xl">{category.icon}</span>
                      </div>
                      <h3 className="font-semibold text-white mb-2">{category.name}</h3>
                      <p className="text-sm text-gray-300">{category.count} tournaments</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Upcoming Tournaments */}
      <section className="py-12 bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center justify-between mb-8">
              <div>
                <h2 className="text-3xl font-bold text-white mb-2">Upcoming Tournaments</h2>
                <p className="text-gray-300">Register now for these exciting competitions</p>
              </div>
              <Button
                variant="outline"
                onClick={() => navigate('/tournaments/browse')}
                className="border-gray-700 text-white hover:bg-gray-800 hover:text-emerald-400"
              >
                View All
              </Button>
            </div>
            
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="bg-gray-700 rounded-lg h-64"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {upcomingTournaments.map((tournament, index) => (
                  <motion.div
                    key={tournament.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                  >
                    <TournamentEnhancedCard tournament={tournament} />
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Tournament Statistics */}
      <section className="py-12 bg-emerald-900 text-white">
        <div className="container mx-auto px-4">
          <TournamentStats />
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-emerald-600 to-emerald-700 relative">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              Ready to Compete?
            </h2>
            <p className="text-xl text-emerald-100 mb-8 max-w-2xl mx-auto">
              Join thousands of players in exciting tournaments. Whether you're a beginner or a pro, there's a tournament for you.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="bg-white text-emerald-900 hover:bg-emerald-50 px-8 py-4"
                onClick={() => navigate('/tournaments/browse')}
              >
                <PlayIcon className="mr-2 h-5 w-5" />
                Join Tournament
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="border-white text-white hover:bg-white hover:text-emerald-900 px-8 py-4"
                onClick={() => navigate('/tournaments/host')}
              >
                <TrophyIcon className="mr-2 h-5 w-5" />
                Host Your Own
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}

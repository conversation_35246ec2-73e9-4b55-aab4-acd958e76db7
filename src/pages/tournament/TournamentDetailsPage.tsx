
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { format } from 'date-fns';
import {
  ArrowLeftIcon,
  HomeIcon,
  TrophyIcon,
  UsersIcon,
  CalendarIcon,
  MapPinIcon,
  CoinsIcon,
  ClockIcon,
  SettingsIcon
} from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "react-hot-toast";
import { TournamentHeroSection } from '@/components/tournament/TournamentHeroSection';
import { TournamentTabs } from '@/components/tournament/TournamentTabs';
import { TournamentRegistrationModal } from '@/components/tournament/TournamentRegistrationModal';
import type { Database } from '@/integrations/supabase/types';

type TournamentRow = Database['public']['Tables']['tournaments']['Row'];

interface Tournament extends TournamentRow {
  sport_name?: string;
  venue_name?: string;
  venue_location?: string;
}

const TournamentDetailsPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [tournament, setTournament] = useState<Tournament | null>(null);
  const [loading, setLoading] = useState(true);
  const [registrationCount, setRegistrationCount] = useState(0);
  const [isRegistered, setIsRegistered] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [isOrganizer, setIsOrganizer] = useState(false);

  useEffect(() => {
    const fetchTournamentDetails = async () => {
      try {
        setLoading(true);
        if (!slug) return;

        const { data, error } = await supabase
          .from('tournaments')
          .select(`
            *,
            sports(name),
            venues(name, location)
          `)
          .eq('slug', slug)
          .eq('status', 'upcoming')
          .single();

        if (error) throw error;

        const tournamentData: Tournament = {
          ...data,
          sport_name: data.sports?.name,
          venue_name: data.venues?.name,
          venue_location: data.venues?.location
        };

        setTournament(tournamentData);

        // Check if current user is the organizer
        if (user && data.organizer_id === user.id) {
          setIsOrganizer(true);
        }
      } catch (error) {
        console.error('Error fetching tournament details:', error);
        toast.error("Failed to load tournament details.");
      } finally {
        setLoading(false);
      }
    };

    fetchTournamentDetails();
  }, [slug, user]);

  useEffect(() => {
    const fetchRegistrationCount = async () => {
      if (!tournament) return;

      const { count, error } = await supabase
        .from('tournament_registrations')
        .select('*', { count: 'exact', head: false })
        .eq('tournament_id', tournament.id);

      if (error) {
        console.error('Error fetching registration count:', error);
        return;
      }

      setRegistrationCount(count || 0);
    };

    fetchRegistrationCount();
  }, [tournament]);

  useEffect(() => {
    const checkRegistration = async () => {
      if (!user || !tournament) return;

      const { data, error } = await supabase
        .from('tournament_registrations')
        .select('id')
        .eq('tournament_id', tournament.id)
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error checking registration:', error);
        return;
      }

      setIsRegistered(!!data);
    };

    checkRegistration();
  }, [user, tournament]);

  const handleRegister = () => {
    if (!user) {
      navigate('/login');
      return;
    }
    setShowRegistrationModal(true);
  };

  const handleRegistrationSuccess = () => {
    setIsRegistered(true);
    setRegistrationCount(prev => prev + 1);
    setShowRegistrationModal(false);
    toast.success('Registration successful!');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Header */}
      <div className="bg-gray-900 border-b border-gray-800 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/')}
                className="flex items-center gap-2 text-white hover:text-emerald-400"
              >
                <HomeIcon className="h-4 w-4" />
                <span className="hidden sm:inline">Grid२Play</span>
              </Button>

              <Button
                variant="ghost"
                onClick={() => navigate('/tournaments')}
                className="flex items-center gap-2 text-white hover:text-emerald-400"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                <span className="hidden sm:inline">Tournament Hub</span>
              </Button>
            </div>

            {/* Organizer Dashboard Button */}
            {isOrganizer && (
              <Button
                onClick={() => navigate('/tournaments/organizer')}
                className="flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white"
              >
                <SettingsIcon className="h-4 w-4" />
                <span className="hidden sm:inline">Manage Tournament</span>
                <span className="sm:hidden">Manage</span>
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-b from-emerald-600 to-emerald-700 py-12 relative">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="container mx-auto px-4 relative z-10">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 text-center">Tournament Details</h1>
          <div className="text-emerald-200 text-center">Grid२Play Legacy Tournament</div>
        </div>
      </div>
      <div className="container mx-auto px-4 py-8">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-400"></div>
          </div>
        ) : !tournament ? (
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-4">Tournament not found</h2>
            <Button
              onClick={() => navigate('/tournaments')}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              Back to Tournaments
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="space-y-6">
                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <TrophyIcon className="h-6 w-6 text-emerald-400" />
                      <h2 className="text-2xl font-bold text-white">{tournament.name}</h2>
                      <Badge className="bg-emerald-600 text-white">
                        {tournament.status}
                      </Badge>
                    </div>
                    <p className="text-gray-300 mb-6">{tournament.description}</p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <TrophyIcon className="h-4 w-4 text-emerald-400" />
                          <span className="text-gray-400">Sport:</span>
                          <span className="text-white font-medium">{tournament.sport_name}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <MapPinIcon className="h-4 w-4 text-emerald-400" />
                          <span className="text-gray-400">Venue:</span>
                          <span className="text-white font-medium">{tournament.venue_name}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <UsersIcon className="h-4 w-4 text-emerald-400" />
                          <span className="text-gray-400">Participants:</span>
                          <span className="text-white font-medium">{registrationCount}/{tournament.max_participants}</span>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <CalendarIcon className="h-4 w-4 text-emerald-400" />
                          <span className="text-gray-400">Start Date:</span>
                          <span className="text-white font-medium">{format(new Date(tournament.start_date), 'MMM dd, yyyy')}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <CalendarIcon className="h-4 w-4 text-emerald-400" />
                          <span className="text-gray-400">End Date:</span>
                          <span className="text-white font-medium">{format(new Date(tournament.end_date), 'MMM dd, yyyy')}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <CoinsIcon className="h-4 w-4 text-emerald-400" />
                          <span className="text-gray-400">Entry Fee:</span>
                          <span className="text-white font-medium">
                            {tournament.entry_fee ? `₹${tournament.entry_fee}` : 'Free'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
            
            <div className="lg:col-span-1">
              <Card className="bg-gray-800 border-gray-700 shadow-lg sticky top-24">
                <CardContent className="p-6">
                  <h2 className="text-2xl font-bold text-white mb-4">Tournament Registration</h2>
                  <div className="space-y-4 mb-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-gray-700 rounded-lg">
                        <div className="text-2xl font-bold text-emerald-400">{registrationCount}</div>
                        <div className="text-gray-300 text-sm">Registered</div>
                      </div>
                      <div className="text-center p-4 bg-gray-700 rounded-lg">
                        <div className="text-2xl font-bold text-white">{tournament.max_participants}</div>
                        <div className="text-gray-300 text-sm">Max Participants</div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300">Entry Fee:</span>
                        <span className="text-white font-semibold">
                          {tournament.entry_fee ? `₹${tournament.entry_fee}` : 'Free'}
                        </span>
                      </div>

                      {tournament.registration_deadline && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-300">Registration Deadline:</span>
                          <span className="text-white font-semibold text-sm">
                            {format(new Date(tournament.registration_deadline), 'MMM dd, yyyy')}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {isRegistered ? (
                    <div className="text-center p-4 bg-emerald-900/20 border border-emerald-600/30 rounded-lg">
                      <div className="text-emerald-300 font-semibold flex items-center justify-center gap-2">
                        <TrophyIcon className="h-4 w-4" />
                        You are registered!
                      </div>
                    </div>
                  ) : (
                    <Button
                      onClick={handleRegister}
                      disabled={isRegistering || registrationCount >= tournament.max_participants}
                      className="w-full bg-emerald-600 hover:bg-emerald-700 text-white font-semibold"
                    >
                      {isRegistering ? 'Processing...' :
                       registrationCount >= tournament.max_participants ? 'Tournament Full' :
                       'Register Now'}
                    </Button>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Registration Modal */}
        {tournament && (
          <TournamentRegistrationModal
            tournament={tournament}
            isOpen={showRegistrationModal}
            onClose={() => setShowRegistrationModal(false)}
            onSuccess={handleRegistrationSuccess}
          />
        )}
      </div>
    </div>
  );
};

export default TournamentDetailsPage;

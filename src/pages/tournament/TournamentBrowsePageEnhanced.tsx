import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  SearchIcon,
  FilterIcon,
  GridIcon,
  ListIcon,
  SortAscIcon,
  SortDescIcon,
  TrophyIcon,
  CalendarIcon,
  MapPinIcon,
  UsersIcon,
  StarIcon,
  ArrowLeftIcon,
  RefreshCwIcon,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useEnhancedTournaments } from '@/hooks/use-tournament-enhanced';
import { TournamentEnhancedCard } from '@/components/tournament/TournamentEnhancedCard';
import { TournamentFilters } from '@/components/tournament/TournamentFilters';
import { TournamentFilters as TournamentFiltersType } from '@/types/tournament-enhanced';

export function TournamentBrowsePageEnhanced() {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'date' | 'popularity' | 'name' | 'participants'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [activeTab, setActiveTab] = useState('all');
  
  const [filters, setFilters] = useState<TournamentFiltersType>({
    search_query: '',
  });

  const { data: tournaments = [], isLoading, refetch } = useEnhancedTournaments({
    ...filters,
    search_query: searchQuery,
  });

  // Filter tournaments by status for tabs
  const filteredTournaments = useMemo(() => {
    let filtered = tournaments;
    
    if (activeTab !== 'all') {
      filtered = tournaments.filter(t => t.status === activeTab);
    }
    
    // Sort tournaments
    filtered = [...filtered].sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.tournament_start_date).getTime() - new Date(b.tournament_start_date).getTime();
          break;
        case 'popularity':
          comparison = (b.view_count + b.registration_count) - (a.view_count + a.registration_count);
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'participants':
          comparison = b.registration_count - a.registration_count;
          break;
      }
      
      return sortOrder === 'desc' ? -comparison : comparison;
    });
    
    return filtered;
  }, [tournaments, activeTab, sortBy, sortOrder]);

  const getTabCount = (status: string) => {
    if (status === 'all') return tournaments.length;
    return tournaments.filter(t => t.status === status).length;
  };

  const handleRefresh = () => {
    refetch();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Header */}
      <div className="bg-gray-900 border-b border-gray-800 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/tournaments')}
                className="flex items-center gap-2 text-white hover:text-emerald-400"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                Back to Hub
              </Button>
              
              <div className="hidden md:block">
                <div className="text-emerald-400 text-sm font-medium">Grid२Play</div>
                <h1 className="text-xl font-bold text-white">Browse Tournaments</h1>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                className="text-white hover:text-emerald-400"
              >
                <RefreshCwIcon className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="text-white hover:text-emerald-400"
              >
                {viewMode === 'grid' ? <ListIcon className="h-4 w-4" /> : <GridIcon className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Controls */}
      <section className="py-6 bg-gray-900 border-b border-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  placeholder="Search tournaments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-12 bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-emerald-500"
                />
              </div>
            </div>
            
            {/* Controls */}
            <div className="flex items-center gap-3">
              {/* Sort */}
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-40 bg-gray-800 border-gray-700 text-white">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700 text-white">
                  <SelectItem value="date">Date</SelectItem>
                  <SelectItem value="popularity">Popularity</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="participants">Participants</SelectItem>
                </SelectContent>
              </Select>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="text-white hover:text-emerald-400"
              >
                {sortOrder === 'asc' ? <SortAscIcon className="h-4 w-4" /> : <SortDescIcon className="h-4 w-4" />}
              </Button>
              
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="border-gray-700 text-white hover:bg-gray-800 hover:text-emerald-400"
              >
                <FilterIcon className="mr-2 h-4 w-4" />
                Filters
                {Object.keys(filters).length > 1 && (
                  <Badge variant="secondary" className="ml-2 bg-emerald-600 text-white">
                    {Object.keys(filters).length - 1}
                  </Badge>
                )}
              </Button>
            </div>
          </div>
          
          {/* Filters Panel */}
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-6"
            >
              <TournamentFilters 
                filters={filters} 
                onFiltersChange={setFilters}
                onClose={() => setShowFilters(false)}
              />
            </motion.div>
          )}
        </div>
      </section>

      {/* Tournament Tabs and Content */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            {/* Tab Navigation */}
            <div className="flex items-center justify-between">
              <TabsList className="bg-gray-800 border-gray-700">
                <TabsTrigger value="all" className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white text-gray-300">
                  All ({getTabCount('all')})
                </TabsTrigger>
                <TabsTrigger value="registration_open" className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white text-gray-300">
                  Open ({getTabCount('registration_open')})
                </TabsTrigger>
                <TabsTrigger value="published" className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white text-gray-300">
                  Upcoming ({getTabCount('published')})
                </TabsTrigger>
                <TabsTrigger value="ongoing" className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white text-gray-300">
                  Live ({getTabCount('ongoing')})
                </TabsTrigger>
                <TabsTrigger value="completed" className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white text-gray-300">
                  Completed ({getTabCount('completed')})
                </TabsTrigger>
              </TabsList>
              
              <div className="text-sm text-gray-400">
                {filteredTournaments.length} tournament{filteredTournaments.length !== 1 ? 's' : ''} found
              </div>
            </div>

            {/* Tournament Content */}
            <TabsContent value={activeTab} className="space-y-6">
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="bg-gray-700 rounded-lg h-64"></div>
                    </div>
                  ))}
                </div>
              ) : filteredTournaments.length === 0 ? (
                <Card className="bg-gray-800 border-gray-700">
                  <CardContent className="p-12 text-center">
                    <TrophyIcon className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-white mb-2">No Tournaments Found</h3>
                    <p className="text-gray-400 mb-6">
                      {searchQuery || Object.keys(filters).length > 1
                        ? 'Try adjusting your search or filters'
                        : 'No tournaments available in this category'}
                    </p>
                    {(searchQuery || Object.keys(filters).length > 1) && (
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSearchQuery('');
                          setFilters({ search_query: '' });
                        }}
                        className="border-gray-700 text-white hover:bg-gray-700"
                      >
                        Clear Filters
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className={
                    viewMode === 'grid'
                      ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                      : 'space-y-4'
                  }
                >
                  {filteredTournaments.map((tournament, index) => (
                    <motion.div
                      key={tournament.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      <TournamentEnhancedCard 
                        tournament={tournament} 
                        featured={tournament.featured}
                        compact={viewMode === 'list'}
                      />
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </section>
    </div>
  );
}

import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import {
  ArrowLeftIcon,
  CalendarIcon,
  MapPinIcon,
  UsersIcon,
  TrophyIcon,
  CoinsIcon,
  ClockIcon,
  EyeIcon,
  ShareIcon,
  HeartIcon,
  PlayIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  InfoIcon,
  StarIcon,
  PhoneIcon,
  MailIcon,
  HomeIcon,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { useEnhancedTournamentDetails, useTournamentParticipants, useTournamentMatches, useIsUserRegistered } from '@/hooks/use-tournament-enhanced';
import { useAuth } from '@/context/AuthContext';
import { TournamentRegistrationModal } from '@/components/tournament/TournamentRegistrationModal';
import { TournamentBracketView } from '@/components/tournament/TournamentBracketView';
import { TournamentParticipantsList } from '@/components/tournament/TournamentParticipantsList';

export function TournamentDetailsPageEnhanced() {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [isOrganizer, setIsOrganizer] = useState(false);

  const { data: tournament, isLoading } = useEnhancedTournamentDetails(slug);
  const { data: participants = [] } = useTournamentParticipants(tournament?.id);
  const { data: matches = [] } = useTournamentMatches(tournament?.id);

  // Check if current user is the organizer
  React.useEffect(() => {
    if (user && tournament && tournament.organizer_id === user.id) {
      setIsOrganizer(true);
    } else {
      setIsOrganizer(false);
    }
  }, [user, tournament]);
  const { data: isRegistered = false } = useIsUserRegistered(tournament?.id, user?.id);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-600"></div>
      </div>
    );
  }

  if (!tournament) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Tournament Not Found</h1>
          <Button onClick={() => navigate('/tournaments')}>
            Back to Tournaments
          </Button>
        </div>
      </div>
    );
  }

  const registrationProgress = (tournament.registration_count / tournament.max_participants) * 100;
  const isRegistrationOpen = tournament.status === 'registration_open';
  const isUpcoming = tournament.status === 'upcoming';
  const isOngoing = tournament.status === 'ongoing';
  const isCompleted = tournament.status === 'completed';

  const getStatusColor = () => {
    switch (tournament.status) {
      case 'registration_open': return 'bg-green-500';
      case 'upcoming': return 'bg-blue-500';
      case 'ongoing': return 'bg-orange-500';
      case 'completed': return 'bg-gray-500';
      default: return 'bg-gray-400';
    }
  };

  const getStatusText = () => {
    switch (tournament.status) {
      case 'registration_open': return 'Registration Open';
      case 'upcoming': return 'Upcoming';
      case 'ongoing': return 'Live';
      case 'completed': return 'Completed';
      default: return tournament.status;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Header */}
      <div className="bg-gray-900 border-b border-gray-800 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/')}
                className="flex items-center gap-2 text-white hover:text-emerald-400"
              >
                <HomeIcon className="h-4 w-4" />
                <span className="hidden sm:inline">Grid२Play</span>
              </Button>

              <Button
                variant="ghost"
                onClick={() => navigate('/tournaments')}
                className="flex items-center gap-2 text-white hover:text-emerald-400"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                <span className="hidden sm:inline">Tournament Hub</span>
              </Button>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Organizer Dashboard Button */}
              {isOrganizer && (
                <Button
                  onClick={() => navigate('/tournaments/organizer')}
                  className="flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white"
                >
                  <SettingsIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Manage Tournament</span>
                  <span className="sm:hidden">Manage</span>
                </Button>
              )}

              <Button variant="outline" size="sm" className="border-gray-700 text-white hover:bg-gray-800">
                <ShareIcon className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm">
                <HeartIcon className="h-4 w-4 mr-2" />
                Save
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-600 to-emerald-700 text-white">
        <div className="container mx-auto px-4 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-start justify-between mb-6">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-4">
                  <div className={`h-3 w-3 rounded-full ${getStatusColor()}`}></div>
                  <Badge className={`${getStatusColor()} text-white`}>
                    {getStatusText()}
                  </Badge>
                  {tournament.featured && (
                    <Badge className="bg-yellow-500 text-white">
                      <StarIcon className="h-3 w-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                  {tournament.live_streaming && (
                    <Badge className="bg-red-500 text-white">
                      <PlayIcon className="h-3 w-3 mr-1" />
                      Live Stream
                    </Badge>
                  )}
                </div>
                
                <h1 className="text-4xl md:text-5xl font-bold mb-4">{tournament.name}</h1>
                
                <div className="flex flex-wrap items-center gap-6 text-emerald-100">
                  <div className="flex items-center gap-2">
                    <TrophyIcon className="h-5 w-5" />
                    <span>{tournament.sport_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPinIcon className="h-5 w-5" />
                    <span>{tournament.venue_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CalendarIcon className="h-5 w-5" />
                    <span>
                      {format(new Date(tournament.tournament_start_date), 'MMM d')} - {' '}
                      {format(new Date(tournament.tournament_end_date), 'MMM d, yyyy')}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <EyeIcon className="h-5 w-5" />
                    <span>{tournament.view_count} views</span>
                  </div>
                </div>
              </div>
              
              {/* Registration CTA */}
              <div className="ml-8">
                <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                  <CardContent className="p-6">
                    <div className="text-center mb-4">
                      <div className="text-2xl font-bold">
                        {tournament.registration_count} / {tournament.max_participants}
                      </div>
                      <div className="text-sm text-emerald-100">Participants</div>
                    </div>
                    
                    <Progress value={registrationProgress} className="mb-4 bg-white/20" />
                    
                    {isRegistrationOpen && !isRegistered && (
                      <Button 
                        className="w-full bg-white text-emerald-600 hover:bg-emerald-50"
                        onClick={() => setShowRegistrationModal(true)}
                      >
                        Register Now
                      </Button>
                    )}
                    
                    {isRegistered && (
                      <Button className="w-full bg-green-600 hover:bg-green-700" disabled>
                        <CheckCircleIcon className="h-4 w-4 mr-2" />
                        Registered
                      </Button>
                    )}
                    
                    {!isRegistrationOpen && (
                      <Button className="w-full bg-gray-600" disabled>
                        Registration Closed
                      </Button>
                    )}
                    
                    {tournament.entry_fee > 0 && (
                      <div className="mt-3 text-center text-sm text-emerald-100">
                        Entry Fee: ₹{tournament.entry_fee}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
            
            {tournament.description && (
              <p className="text-lg text-emerald-100 max-w-3xl">
                {tournament.description}
              </p>
            )}
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <section className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="participants">Participants</TabsTrigger>
            <TabsTrigger value="bracket">Bracket</TabsTrigger>
            <TabsTrigger value="matches">Matches</TabsTrigger>
            <TabsTrigger value="info">Info</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Tournament Details */}
              <div className="lg:col-span-2 space-y-6">
                {/* Quick Stats */}
                <Card>
                  <CardHeader>
                    <CardTitle>Tournament Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-emerald-600">{tournament.registration_count}</div>
                        <div className="text-sm text-gray-600">Registered</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-emerald-600">{tournament.max_participants}</div>
                        <div className="text-sm text-gray-600">Max Players</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-emerald-600">
                          {tournament.format_type === 'team' ? tournament.team_size : '1'}
                        </div>
                        <div className="text-sm text-gray-600">Team Size</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-emerald-600 capitalize">
                          {tournament.tournament_type}
                        </div>
                        <div className="text-sm text-gray-600">Format</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Prize Pool */}
                {tournament.prize_pool > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <TrophyIcon className="h-5 w-5 text-emerald-600" />
                        Prize Pool
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center">
                        <div className="text-4xl font-bold text-emerald-600 mb-2">
                          ₹{tournament.prize_pool.toLocaleString()}
                        </div>
                        <div className="text-gray-600">Total Prize Money</div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Tournament Rules */}
                {tournament.rules && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Tournament Rules</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="prose prose-sm max-w-none">
                        <p className="whitespace-pre-wrap">{tournament.rules}</p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Registration Status */}
                {isRegistrationOpen && (
                  <Alert>
                    <ClockIcon className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Registration closes:</strong><br />
                      {format(new Date(tournament.registration_end_date), 'PPP p')}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Organizer Info */}
                <Card>
                  <CardHeader>
                    <CardTitle>Organizer</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-3 mb-4">
                      <Avatar>
                        <AvatarImage src="" />
                        <AvatarFallback>
                          {tournament.organizer_name?.charAt(0) || 'O'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{tournament.organizer_name}</div>
                        <div className="text-sm text-gray-600">Tournament Organizer</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Venue Info */}
                <Card>
                  <CardHeader>
                    <CardTitle>Venue Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <div className="font-medium">{tournament.venue_name}</div>
                      <div className="text-sm text-gray-600">{tournament.venue_location}</div>
                    </div>
                    <Separator />
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <MapPinIcon className="h-4 w-4 text-emerald-600" />
                        <span>View on Map</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <PhoneIcon className="h-4 w-4 text-emerald-600" />
                        <span>Contact Venue</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Other tabs content will be added here */}
          <TabsContent value="participants">
            <TournamentParticipantsList participants={participants} />
          </TabsContent>

          <TabsContent value="bracket">
            <TournamentBracketView tournament={tournament} matches={matches} />
          </TabsContent>

          <TabsContent value="matches">
            <div>Matches content coming soon...</div>
          </TabsContent>

          <TabsContent value="info">
            <div>Additional info content coming soon...</div>
          </TabsContent>
        </Tabs>
      </section>

      {/* Registration Modal */}
      {showRegistrationModal && tournament && (
        <TournamentRegistrationModal
          tournament={tournament}
          isOpen={showRegistrationModal}
          onClose={() => setShowRegistrationModal(false)}
        />
      )}
    </div>
  );
}

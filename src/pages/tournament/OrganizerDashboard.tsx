import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  PlusIcon,
  TrophyIcon,
  UsersIcon,
  CalendarIcon,
  BarChart3Icon,
  SettingsIcon,
  EyeIcon,
  EditIcon,
  TrashIcon,
  PlayIcon,
  PauseIcon,
  DollarSignIcon,
  TrendingUpIcon,
  AwardIcon,
  HomeIcon,
  ArrowLeftIcon,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { useEnhancedTournaments } from '@/hooks/use-tournament-enhanced';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'react-hot-toast';

export function OrganizerDashboard() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data for organizer tournaments
  const { data: tournaments = [] } = useEnhancedTournaments({
    // Filter by organizer when we have the relationship
  });

  // Real organizer stats
  const [organizerStats, setOrganizerStats] = useState({
    totalTournaments: 0,
    activeTournaments: 0,
    totalParticipants: 0,
    totalRevenue: 0,
    averageRating: 0,
    completionRate: 0,
  });
  const [organizerTournaments, setOrganizerTournaments] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch real organizer data
  useEffect(() => {
    const fetchOrganizerData = async () => {
      if (!user?.id) return;

      try {
        setLoading(true);

        // Fetch tournaments created by this organizer
        const { data: tournamentsData, error: tournamentsError } = await supabase
          .from('tournaments_enhanced')
          .select(`
            *,
            sports(name),
            venues(name, location)
          `)
          .eq('organizer_id', user.id)
          .order('created_at', { ascending: false });

        if (tournamentsError) throw tournamentsError;

        setOrganizerTournaments(tournamentsData || []);

        // Calculate statistics
        const totalTournaments = tournamentsData?.length || 0;
        const activeTournaments = tournamentsData?.filter(t =>
          t.status === 'registration_open' || t.status === 'ongoing'
        ).length || 0;

        // Fetch participant counts for each tournament
        let totalParticipants = 0;
        let totalRevenue = 0;
        let completedTournaments = 0;

        for (const tournament of tournamentsData || []) {
          // Get participant count
          const { count: participantCount } = await supabase
            .from('tournament_participants')
            .select('*', { count: 'exact', head: true })
            .eq('tournament_id', tournament.id);

          totalParticipants += participantCount || 0;

          // Calculate revenue (entry fee * participants)
          if (tournament.entry_fee && participantCount) {
            totalRevenue += tournament.entry_fee * participantCount;
          }

          if (tournament.status === 'completed') {
            completedTournaments++;
          }
        }

        const completionRate = totalTournaments > 0
          ? Math.round((completedTournaments / totalTournaments) * 100)
          : 0;

        setOrganizerStats({
          totalTournaments,
          activeTournaments,
          totalParticipants,
          totalRevenue,
          averageRating: 4.7, // This would come from a ratings table
          completionRate,
        });

      } catch (error) {
        console.error('Error fetching organizer data:', error);
        toast.error('Failed to load organizer data');
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizerData();
  }, [user?.id]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'registration_open': return 'bg-green-500';
      case 'ongoing': return 'bg-orange-500';
      case 'completed': return 'bg-gray-500';
      case 'upcoming': return 'bg-blue-500';
      default: return 'bg-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'registration_open': return 'Registration Open';
      case 'ongoing': return 'Live';
      case 'completed': return 'Completed';
      case 'upcoming': return 'Upcoming';
      default: return status;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Header */}
      <div className="bg-gray-900 border-b border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  onClick={() => window.location.href = '/'}
                  className="flex items-center gap-2 text-white hover:text-emerald-400"
                >
                  <HomeIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Grid२Play</span>
                </Button>

                <Button
                  variant="ghost"
                  onClick={() => window.location.href = '/tournaments'}
                  className="flex items-center gap-2 text-white hover:text-emerald-400"
                >
                  <ArrowLeftIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Tournament Hub</span>
                </Button>
              </div>

              <div>
                <div className="text-emerald-400 text-sm font-medium">Grid२Play</div>
                <h1 className="text-3xl font-bold text-white">Organizer Dashboard</h1>
                <p className="text-gray-300 mt-1">Manage your tournaments and track performance</p>
              </div>
            </div>

            <Button asChild className="bg-emerald-600 hover:bg-emerald-700">
              <Link to="/tournaments/host">
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Tournament
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tournaments">Tournaments</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Tournaments</p>
                        <p className="text-3xl font-bold text-gray-900">{organizerStats.totalTournaments}</p>
                      </div>
                      <div className="p-3 bg-emerald-100 rounded-lg">
                        <TrophyIcon className="h-6 w-6 text-emerald-600" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center text-sm">
                      <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-green-500">+2 this month</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Active Tournaments</p>
                        <p className="text-3xl font-bold text-gray-900">{organizerStats.activeTournaments}</p>
                      </div>
                      <div className="p-3 bg-orange-100 rounded-lg">
                        <PlayIcon className="h-6 w-6 text-orange-600" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center text-sm">
                      <span className="text-gray-500">Currently running</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Participants</p>
                        <p className="text-3xl font-bold text-gray-900">{organizerStats.totalParticipants}</p>
                      </div>
                      <div className="p-3 bg-blue-100 rounded-lg">
                        <UsersIcon className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center text-sm">
                      <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-green-500">+15% growth</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                        <p className="text-3xl font-bold text-gray-900">₹{organizerStats.totalRevenue.toLocaleString()}</p>
                      </div>
                      <div className="p-3 bg-green-100 rounded-lg">
                        <DollarSignIcon className="h-6 w-6 text-green-600" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center text-sm">
                      <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-green-500">+22% this month</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Recent Tournaments */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Recent Tournaments</span>
                  <Button variant="outline" size="sm" asChild>
                    <Link to="#tournaments">View All</Link>
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {loading ? (
                    <div className="flex justify-center items-center h-32">
                      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-emerald-400"></div>
                    </div>
                  ) : organizerTournaments.length === 0 ? (
                    <div className="text-center py-8">
                      <TrophyIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-white mb-2">No Tournaments Yet</h3>
                      <p className="text-gray-400 mb-4">Create your first tournament to get started</p>
                      <Button asChild className="bg-emerald-600 hover:bg-emerald-700">
                        <Link to="/tournaments/host">
                          <PlusIcon className="h-4 w-4 mr-2" />
                          Create Tournament
                        </Link>
                      </Button>
                    </div>
                  ) : organizerTournaments.slice(0, 5).map((tournament, index) => (
                    <motion.div
                      key={tournament.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`h-3 w-3 rounded-full ${getStatusColor(tournament.status)}`}></div>
                        <div>
                          <h3 className="font-medium text-white">{tournament.name}</h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-300">
                            <span>{tournament.registration_count || 0}/{tournament.max_participants} participants</span>
                            <span>₹{((tournament.entry_fee || 0) * (tournament.registration_count || 0)).toLocaleString()} revenue</span>
                          </div>
                          <div className="text-xs text-gray-400 mt-1">
                            {tournament.sports?.name} • {tournament.venues?.name}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={getStatusColor(tournament.status).replace('bg-', 'text-')}>
                          {getStatusText(tournament.status)}
                        </Badge>
                        <Button variant="ghost" size="sm">
                          <EyeIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <EditIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AwardIcon className="h-5 w-5 text-emerald-600" />
                    Performance Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Average Rating</span>
                    <span className="font-medium">{organizerStats.averageRating}/5.0</span>
                  </div>
                  <Progress value={organizerStats.averageRating * 20} className="h-2" />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Completion Rate</span>
                    <span className="font-medium">{organizerStats.completionRate}%</span>
                  </div>
                  <Progress value={organizerStats.completionRate} className="h-2" />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full justify-start" variant="outline" asChild>
                    <Link to="/tournaments/host">
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Create New Tournament
                    </Link>
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <BarChart3Icon className="h-4 w-4 mr-2" />
                    View Analytics
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <UsersIcon className="h-4 w-4 mr-2" />
                    Manage Participants
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <SettingsIcon className="h-4 w-4 mr-2" />
                    Account Settings
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Tournaments Tab */}
          <TabsContent value="tournaments">
            <Card>
              <CardHeader>
                <CardTitle>All Tournaments</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tournament</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Participants</TableHead>
                      <TableHead>Revenue</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-emerald-400 mx-auto"></div>
                        </TableCell>
                      </TableRow>
                    ) : organizerTournaments.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          <div className="text-gray-400">No tournaments found</div>
                        </TableCell>
                      </TableRow>
                    ) : organizerTournaments.map((tournament) => (
                      <TableRow key={tournament.id}>
                        <TableCell className="font-medium text-white">{tournament.name}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={getStatusColor(tournament.status).replace('bg-', 'text-')}>
                            {getStatusText(tournament.status)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-gray-300">{tournament.registration_count || 0}/{tournament.max_participants}</TableCell>
                        <TableCell className="text-gray-300">₹{((tournament.entry_fee || 0) * (tournament.registration_count || 0)).toLocaleString()}</TableCell>
                        <TableCell className="text-gray-300">{new Date(tournament.tournament_start_date).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <EyeIcon className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <EditIcon className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <TrashIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics">
            <div className="text-center py-12">
              <BarChart3Icon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Analytics Dashboard</h3>
              <p className="text-gray-600">Detailed analytics and insights coming soon...</p>
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings">
            <div className="text-center py-12">
              <SettingsIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Organizer Settings</h3>
              <p className="text-gray-600">Account and organization settings coming soon...</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

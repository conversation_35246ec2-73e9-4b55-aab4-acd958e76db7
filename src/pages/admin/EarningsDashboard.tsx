import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import {
  Calendar, DollarSign, TrendingUp, Clock,
  Banknote, Activity, Eye, ChevronRight,
  Loader2, AlertCircle, CheckCircle, RefreshCw,
  ArrowLeft, ArrowRight, Download, Filter, Settings, BookOpen
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { format, startOfWeek, endOfWeek, addDays, subDays, parseISO } from 'date-fns';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "react-hot-toast";
import SettlementManagementModal from '@/components/admin/SettlementManagementModal';
import * as XLSX from 'xlsx';
import { getActiveDaysDisplay, getActiveDaysLabel } from '@/utils/weeklyUtils';

interface DailyEarnings {
  venue_id: string;
  venue_name: string;
  cycle_date: string;
  cycle_day_name: string;
  total_bookings: number;
  confirmed_bookings: number;

  // Legacy columns (now contain online values only for settlement compatibility)
  gross_revenue: number;
  platform_fee_amount: number;
  net_revenue: number;

  // PHASE 3: New online/offline separation columns
  online_gross_revenue?: number;
  online_platform_fees?: number;
  online_net_revenue?: number;
  online_bookings?: number;
  offline_gross_revenue?: number;
  offline_platform_fees?: number;
  offline_net_revenue?: number;
  offline_bookings?: number;

  is_current_day: boolean;
  is_frozen: boolean;
}

interface Settlement {
  settlement_id: string;
  venue_id: string;
  venue_name: string;
  settlement_week_start: string;
  settlement_week_end: string;
  settlement_reference: string;
  total_gross_revenue: number;
  total_platform_fees: number;
  total_net_revenue: number;
  total_bookings: number;
  status: 'pending' | 'processed' | 'settled';
  expected_settlement_date: string;
  created_at: string;
  notes?: string;
}

interface EarningsDashboardProps {
  userRole: string;
  adminVenues: Array<{ venue_id: string }>;
}

const EarningsDashboard: React.FC<EarningsDashboardProps> = ({ userRole, adminVenues }) => {
  const { user } = useAuth();

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dailyEarnings, setDailyEarnings] = useState<DailyEarnings[]>([]);
  const [settlements, setSettlements] = useState<Settlement[]>([]);
  const [selectedVenue, setSelectedVenue] = useState<string>('all');
  const [venuesWithNames, setVenuesWithNames] = useState<Array<{ venue_id: string; venue_name: string }>>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentWeekStart, setCurrentWeekStart] = useState(startOfWeek(new Date(), { weekStartsOn: 1 }));
  const [selectedSettlement, setSelectedSettlement] = useState<Settlement | null>(null);
  const [settlementDetailsOpen, setSettlementDetailsOpen] = useState(false);
  const [managementOpen, setManagementOpen] = useState(false);
  const [downloadingReport, setDownloadingReport] = useState(false);
  const [couponAnalytics, setCouponAnalytics] = useState<{
    totalCouponBookings: number;
    totalDiscountGiven: number;
    couponBreakdown: Array<{code: string; count: number; totalDiscount: number}>;
  } | null>(null);
  // HUDLE APPROACH: Online vs Offline Metrics State
  const [onlineMetrics, setOnlineMetrics] = useState<{
    count: number;
    grossRevenue: number;
    netSettlement: number;
  } | null>(null);
  const [offlineMetrics, setOfflineMetrics] = useState<{
    count: number;
    grossRevenue: number;
    netAmount: number;
  } | null>(null);
  // PHASE 3: Cancelled Booking Count State
  const [cancelledBookingCount, setCancelledBookingCount] = useState<number | null>(null);

  // Fetch earnings data
  const fetchEarningsData = async (showRefreshIndicator = false) => {
    if (!user?.id) return;
    
    if (showRefreshIndicator) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      const weekEnd = endOfWeek(currentWeekStart, { weekStartsOn: 1 });
      
      // Fetch daily earnings for current week
      const { data: earningsData, error: earningsError } = await supabase
        .rpc('get_admin_daily_earnings', {
          p_start_date: format(currentWeekStart, 'yyyy-MM-dd'),
          p_end_date: format(weekEnd, 'yyyy-MM-dd'),
          p_venue_id: selectedVenue === 'all' ? null : selectedVenue
        });

      if (earningsError) throw earningsError;

      setDailyEarnings(earningsData || []);

      // Fetch settlements
      const { data: settlementsData, error: settlementsError } = await supabase
        .rpc('get_admin_settlements', {
          p_venue_id: selectedVenue === 'all' ? null : selectedVenue,
          p_status: null,
          p_limit: 20
        });

      if (settlementsError) throw settlementsError;

      setSettlements(settlementsData || []);

    } catch (error) {
      console.error('Error fetching earnings data:', error);
      toast.error('Failed to load earnings data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch venue names
  useEffect(() => {
    const fetchVenueNames = async () => {
      if (adminVenues.length === 0) return;

      try {
        const venueIds = adminVenues.map(v => v.venue_id);
        const { data: venueDetails, error } = await supabase
          .from('venues')
          .select('id, name')
          .in('id', venueIds)
          .eq('is_active', true);

        if (!error && venueDetails) {
          const venuesWithNamesData = adminVenues.map(v => ({
            venue_id: v.venue_id,
            venue_name: venueDetails.find(vd => vd.id === v.venue_id)?.name || `Venue ${v.venue_id.slice(0, 8)}`
          }));
          setVenuesWithNames(venuesWithNamesData);
        }
      } catch (error) {
        console.error('Error fetching venue names:', error);
      }
    };

    fetchVenueNames();
  }, [adminVenues]);

  useEffect(() => {
    fetchEarningsData();
  }, [user?.id, currentWeekStart, selectedVenue]);

  // Settlement management functions
  const openManagement = (settlement: Settlement) => {
    setSelectedSettlement(settlement);
    setManagementOpen(true);
  };

  const handleSettlementUpdate = () => {
    fetchEarningsData(true);
    setManagementOpen(false);
    setSelectedSettlement(null);
  };

  // HUDLE APPROACH: Handle closing settlement details modal
  const closeSettlementDetails = (open: boolean) => {
    setSettlementDetailsOpen(open);
    if (!open) {
      // Reset metrics when closing
      setOnlineMetrics(null);
      setOfflineMetrics(null);
      setCouponAnalytics(null);
      setSelectedSettlement(null);
      // PHASE 3: Reset cancelled booking count
      setCancelledBookingCount(null);
    }
  };

  // Fetch coupon analytics for selected settlement
  const fetchCouponAnalytics = async (settlement: Settlement) => {
    try {
      console.log('🔄 Fetching coupon analytics for settlement:', settlement.settlement_reference);

      // Use RPC function to get booking data with coupons for this settlement
      const { data: rpcData, error } = await supabase
        .rpc('get_bookings_with_coupons', {
          start_date: settlement.settlement_week_start,
          end_date: settlement.settlement_week_end
        });

      if (error) throw error;

      // Filter bookings for this specific venue and status
      const settlementBookings = rpcData?.filter(booking =>
        booking.court_data?.venue?.id === settlement.venue_id &&
        ['confirmed', 'completed'].includes(booking.status)
      ) || [];

      // Calculate coupon analytics
      const bookingsWithCoupons = settlementBookings.filter(booking =>
        booking.coupon_data && Array.isArray(booking.coupon_data) && booking.coupon_data.length > 0
      );

      const totalDiscountGiven = bookingsWithCoupons.reduce((sum, booking) => {
        const couponUsage = booking.coupon_data[0];
        return sum + (Number(couponUsage.discount_applied) || 0);
      }, 0);

      // Group by coupon code
      const couponBreakdown = bookingsWithCoupons.reduce((acc, booking) => {
        const couponUsage = booking.coupon_data[0];
        const code = couponUsage.coupon?.code || 'Unknown';
        const discount = Number(couponUsage.discount_applied) || 0;

        if (!acc[code]) {
          acc[code] = { code, count: 0, totalDiscount: 0 };
        }
        acc[code].count++;
        acc[code].totalDiscount += discount;
        return acc;
      }, {} as Record<string, {code: string; count: number; totalDiscount: number}>);

      setCouponAnalytics({
        totalCouponBookings: bookingsWithCoupons.length,
        totalDiscountGiven,
        couponBreakdown: Object.values(couponBreakdown)
      });

      console.log('📊 Coupon analytics calculated:', {
        totalBookings: settlementBookings.length,
        couponBookings: bookingsWithCoupons.length,
        totalDiscount: totalDiscountGiven,
        breakdown: Object.values(couponBreakdown)
      });

    } catch (error) {
      console.error('Error fetching coupon analytics:', error);
      setCouponAnalytics(null);
    }
  };

  // HUDLE APPROACH: Calculate Online vs Offline Metrics + PHASE 3: Cancelled Bookings
  const calculateOnlineOfflineMetrics = async (settlement: Settlement) => {
    try {
      // PHASE 3: Fetch booking data including cancelled bookings
      const { data: rpcData, error: bookingError } = await supabase
        .rpc('get_bookings_with_coupons_including_cancelled', {
          start_date: settlement.settlement_week_start,
          end_date: settlement.settlement_week_end
        });

      if (bookingError) {
        console.error('Error fetching booking data:', bookingError);
        return;
      }

      // Filter all bookings for this specific venue
      const allVenueBookings = rpcData?.filter(booking =>
        booking.court_data?.venue?.id === settlement.venue_id
      ) || [];

      // Separate confirmed/completed bookings from cancelled bookings
      const settlementBookings = allVenueBookings.filter(booking =>
        ['confirmed', 'completed'].includes(booking.status)
      );

      // PHASE 3: Count cancelled bookings for this venue
      const cancelledBookings = allVenueBookings.filter(booking =>
        booking.status === 'cancelled'
      );

      // Separate online and offline bookings
      const onlineBookings = settlementBookings.filter(b => b.payment_method === 'online');
      const offlineBookings = settlementBookings.filter(b => b.payment_method === 'cash');

      // Calculate online metrics
      let onlineGrossRevenue = 0;
      let onlineNetSettlement = 0;

      onlineBookings.forEach(booking => {
        const couponUsageArray = booking.coupon_data;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(booking.total_price) || 0);
        const totalPrice = Number(booking.total_price) || 0;
        const platformFeePercent = booking.court_data?.venue?.platform_fee_percentage || 5;
        const platformFee = originalAmount * (platformFeePercent / 100);

        onlineGrossRevenue += originalAmount;
        onlineNetSettlement += (totalPrice - platformFee);
      });

      // Calculate offline metrics
      let offlineGrossRevenue = 0;
      let offlineNetAmount = 0;

      offlineBookings.forEach(booking => {
        const couponUsageArray = booking.coupon_data;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(booking.total_price) || 0);
        const totalPrice = Number(booking.total_price) || 0;

        offlineGrossRevenue += originalAmount;
        offlineNetAmount += totalPrice;
      });

      // Set the metrics
      setOnlineMetrics({
        count: onlineBookings.length,
        grossRevenue: onlineGrossRevenue,
        netSettlement: onlineNetSettlement
      });

      setOfflineMetrics({
        count: offlineBookings.length,
        grossRevenue: offlineGrossRevenue,
        netAmount: offlineNetAmount
      });

      // PHASE 3: Set cancelled booking count
      setCancelledBookingCount(cancelledBookings.length);

      console.log('📊 Desktop Settlement metrics calculated:', {
        online: { count: onlineBookings.length, grossRevenue: onlineGrossRevenue, netSettlement: onlineNetSettlement },
        offline: { count: offlineBookings.length, grossRevenue: offlineGrossRevenue, netAmount: offlineNetAmount },
        cancelled: cancelledBookings.length
      });

    } catch (error) {
      console.error('Error calculating online/offline metrics:', error);
    }
  };

  // Download settlement report function (standardized to match mobile format exactly)
  const downloadSettlementReport = async (settlement: Settlement) => {
    if (!settlement || (settlement.status !== 'processed' && settlement.status !== 'settled')) {
      toast.error('Report is only available for processed or settled settlements');
      return;
    }

    setDownloadingReport(true);
    try {
      console.log('Downloading settlement report for:', settlement.settlement_reference);

      // PHASE 3: Fetch booking data including cancelled bookings for Excel export
      console.log('🔄 Settlement - Using RPC function to fetch booking data with coupons including cancelled...');
      const { data: rpcData, error: bookingError } = await supabase
        .rpc('get_bookings_with_coupons_including_cancelled', {
          start_date: settlement.settlement_week_start,
          end_date: settlement.settlement_week_end
        });

      if (bookingError) {
        console.error('Error fetching booking data:', bookingError);
        throw bookingError;
      }

      // Transform RPC data to match expected structure and filter by venue (INCLUDES CANCELLED)
      const allBookingsData = rpcData?.filter(booking =>
        booking.court_data?.venue?.id === settlement.venue_id
      ).map(booking => ({
        ...booking,
        court: booking.court_data,
        coupon_usage: booking.coupon_data,
        cancellation_data: booking.cancellation_data // PHASE 3: Include cancellation data
      })) || [];

      // PHASE 3: Separate confirmed/completed bookings from cancelled bookings
      const confirmedBookings = allBookingsData.filter(booking =>
        ['confirmed', 'completed'].includes(booking.status)
      );
      const cancelledBookings = allBookingsData.filter(booking =>
        booking.status === 'cancelled'
      );

      console.log('🔍 Settlement - Fetched booking data:', {
        totalRpcBookings: rpcData?.length,
        allBookings: allBookingsData.length,
        confirmedBookings: confirmedBookings.length,
        cancelledBookings: cancelledBookings.length,
        settlementVenueId: settlement.venue_id
      });

      // Step 2: Extract unique user IDs and batch fetch profiles - EXACT MOBILE MATCH
      const userIds = [...new Set(
        allBookingsData
          ?.filter(booking => booking.user_id)
          .map(booking => booking.user_id)
      )].filter(Boolean);

      let profilesMap = new Map();

      if (userIds.length > 0) {
        const { data: profilesData, error: profileError } = await supabase
          .from('profiles')
          .select('id, full_name, phone')
          .in('id', userIds);

        if (profileError) {
          console.error('Error fetching profiles:', profileError);
          throw profileError;
        }

        console.log('Fetched profiles:', profilesData);

        // Create lookup map for O(1) access
        profilesData?.forEach(profile => {
          profilesMap.set(profile.id, profile);
        });
      }

      // PHASE 3: Include ALL bookings (registered users + guest bookings) for settlement reports
      const bookingsWithProfiles = allBookingsData?.map(booking => {
        // For registered users, try to get profile data
        if (booking.user_id && profilesMap.has(booking.user_id)) {
          const profile = profilesMap.get(booking.user_id);
          return {
            ...booking,
            customer_name: profile.full_name || booking.guest_name || 'N/A',
            customer_phone: profile.phone || 'N/A'
          };
        }
        // For guest bookings or missing profiles, use guest data
        return {
          ...booking,
          customer_name: booking.guest_name || 'Guest User',
          customer_phone: 'N/A'
        };
      }) || [];

      console.log('Bookings with valid profiles:', bookingsWithProfiles);

      // Prepare Excel data using EXACT MOBILE FORMAT (Array of Arrays)
      const excelData = [];

      // CORRECT STRUCTURE: Settlement Summary Header
      excelData.push([
        'SETTLEMENT SUMMARY',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        ''
      ]);

      // CORRECT STRUCTURE: Add proper header row for settlement data
      excelData.push([
        'Settlement Reference',
        'Venue',
        'Period',
        'Status',
        'Total Bookings',
        'Gross Revenue',
        'Platform Fees',
        'TDS Amount',
        'Net Revenue',
        'Expected Settlement Date',
        '',
        ''
      ]);

      // CORRECT STRUCTURE: Settlement data row (will be updated with calculated online-only values)
      excelData.push([
        settlement.settlement_reference,
        settlement.venue_name,
        `${format(parseISO(settlement.settlement_week_start), 'dd MMM')} - ${format(parseISO(settlement.settlement_week_end), 'dd MMM, yyyy')}`,
        settlement.status.charAt(0).toUpperCase() + settlement.status.slice(1),
        settlement.total_bookings, // Will be updated with calculated values
        settlement.total_gross_revenue, // Will be updated with calculated values
        settlement.total_platform_fees, // Will be updated with calculated values
        settlement.total_tds_amount || 0, // Will be updated with calculated values
        settlement.total_net_revenue, // Will be updated with calculated values
        format(parseISO(settlement.expected_settlement_date), 'dd MMM, yyyy'),
        '',
        ''
      ]);

      // Add empty row - EXACT MOBILE MATCH
      excelData.push(['', '', '', '', '', '', '', '', '', '', '', '']);

      // Add booking details header - EXACT MOBILE MATCH
      excelData.push([
        'BOOKING DETAILS',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        ''
      ]);

      excelData.push([
        'Booking Date',
        'Customer Name',
        'Customer Phone',
        'Court',
        'Start Time',
        'End Time',
        'Coupon Applied',
        'Coupon Code',
        'Original Amount (₹)',
        'Discount Amount (₹)',
        'Final Amount (₹)',
        'User Payment (₹)',
        'Platform Fee (₹)',
        'TDS Amount (₹)',
        'Net Settlement (₹)',
        'Payment Method',
        'Booking Reference'
      ]);

      // PHASE 3: Add individual booking rows including cancelled bookings
      if (bookingsWithProfiles.length > 0) {
        bookingsWithProfiles.forEach(booking => {
          const profile = profilesMap.get(booking.user_id);

          // Get coupon information - handle null or array
          const couponUsageArray = (booking as any).coupon_usage;
          const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
          const hasCoupon = !!couponUsage;

          // PHASE 3: Check if booking is cancelled
          const isCancelled = booking.status === 'cancelled';
          const cancellationData = (booking as any).cancellation_data;

          const platformFeePercent = booking.court?.venue?.platform_fee_percentage || 5;
          const totalPrice = parseFloat(booking.total_price) || 0;

          // PHASE 1: OPTION B - Platform fee calculated on original amount
          const originalAmount = hasCoupon ? (Number(couponUsage.original_price) || 0) : totalPrice;
          const discountAmount = hasCoupon ? (Number(couponUsage.discount_applied) || 0) : 0;
          const finalAmount = hasCoupon ? (Number(couponUsage.final_price) || 0) : totalPrice;
          const tdsRate = booking.court?.venue?.tds_rate || 1.0;
          const platformFee = originalAmount * (platformFeePercent / 100);
          const tdsAmount = booking.payment_method === 'online' ? (platformFee * (tdsRate / 100)) : 0;
          const netSettlement = totalPrice - platformFee - tdsAmount;

          console.log('🔍 Desktop Settlement - Processing booking:', booking.id, 'status:', booking.status, 'isCancelled:', isCancelled);

          excelData.push([
            format(parseISO(booking.booking_date), 'dd MMM, yyyy'),
            profile?.full_name || 'N/A',
            profile?.phone || 'N/A',
            booking.court?.name || 'N/A',
            booking.start_time || 'N/A',
            booking.end_time || 'N/A',
            hasCoupon ? 'YES' : 'NO',
            hasCoupon ? (couponUsage.coupon?.code || 'N/A') : 'N/A',
            // PHASE 3: Mark cancelled booking amounts as "CANCELLED"
            isCancelled ? 'CANCELLED' : originalAmount.toFixed(2),
            isCancelled ? 'CANCELLED' : discountAmount.toFixed(2),
            isCancelled ? 'CANCELLED' : finalAmount.toFixed(2),
            isCancelled ? 'CANCELLED' : totalPrice.toFixed(2),
            isCancelled ? 'CANCELLED' : platformFee.toFixed(2),
            isCancelled ? 'CANCELLED' : tdsAmount.toFixed(2),
            isCancelled ? 'CANCELLED' : netSettlement.toFixed(2),
            booking.payment_method || 'N/A',
            booking.booking_reference || 'N/A'
          ]);
        });
      } else {
        excelData.push([
          'No bookings with valid user profiles found for this settlement period',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          ''
        ]);
      }

      // TASK 2: Calculate correct online-only values for header (EXCLUDES CANCELLED)
      let headerOnlineGrossRevenue = 0;
      let headerOnlinePlatformFee = 0;
      let headerOnlineNetSettlement = 0;
      let headerOnlineBookings = 0;

      if (bookingsWithProfiles.length > 0) {
        // PHASE 3: Separate confirmed/completed bookings from cancelled bookings for summary
        const confirmedBookingsWithProfiles = bookingsWithProfiles.filter(b =>
          ['confirmed', 'completed'].includes(b.status)
        );
        const cancelledBookingsWithProfiles = bookingsWithProfiles.filter(b =>
          b.status === 'cancelled'
        );

        const onlineBookings = confirmedBookingsWithProfiles.filter(b => b.payment_method === 'online');
        const offlineBookings = confirmedBookingsWithProfiles.filter(b => b.payment_method === 'cash');

        // Online calculations (Settlement-affecting)
        let onlineGrossRevenue = 0;
        let onlinePlatformFee = 0;
        let onlineTdsAmount = 0;
        let onlineNetSettlement = 0;

        onlineBookings.forEach(booking => {
          const couponUsageArray = (booking as any).coupon_usage;
          const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
          const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(booking.total_price) || 0);
          const totalPrice = Number(booking.total_price) || 0;
          const platformFeePercent = booking.court?.venue?.platform_fee_percentage || 5;
          const tdsRate = booking.court?.venue?.tds_rate || 1.0;
          const platformFee = originalAmount * (platformFeePercent / 100);
          const tdsAmount = platformFee * (tdsRate / 100);

          onlineGrossRevenue += originalAmount;
          onlinePlatformFee += platformFee;
          onlineTdsAmount += tdsAmount;
          onlineNetSettlement += (totalPrice - platformFee - tdsAmount);
        });

        // TASK 2: Set header values to online-only calculations
        headerOnlineGrossRevenue = onlineGrossRevenue;
        headerOnlinePlatformFee = onlinePlatformFee;
        headerOnlineNetSettlement = onlineNetSettlement;
        headerOnlineBookings = onlineBookings.length;

        // TASK 2: Update the settlement data row with calculated online-only values
        // CORRECT STRUCTURE: Now it's index 2 (0=SETTLEMENT SUMMARY, 1=header row, 2=data row)
        excelData[2] = [
          settlement.settlement_reference,
          settlement.venue_name,
          `${format(parseISO(settlement.settlement_week_start), 'dd MMM')} - ${format(parseISO(settlement.settlement_week_end), 'dd MMM, yyyy')}`,
          settlement.status.charAt(0).toUpperCase() + settlement.status.slice(1),
          headerOnlineBookings, // Use calculated online bookings count
          headerOnlineGrossRevenue.toFixed(2), // Use calculated online gross revenue
          headerOnlinePlatformFee.toFixed(2), // Use calculated online platform fees
          onlineTdsAmount.toFixed(2), // Use calculated online TDS amount
          headerOnlineNetSettlement.toFixed(2), // Use calculated online net settlement
          format(parseISO(settlement.expected_settlement_date), 'dd MMM, yyyy'),
          '',
          '',
          ''
        ];

        // Offline calculations (Informational only)
        let offlineGrossRevenue = 0;
        let offlineNetAmount = 0;

        offlineBookings.forEach(booking => {
          const couponUsageArray = (booking as any).coupon_usage;
          const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
          const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(booking.total_price) || 0);
          const totalPrice = Number(booking.total_price) || 0;

          offlineGrossRevenue += originalAmount;
          offlineNetAmount += totalPrice;
        });

        // TASK 3: Calculate coupon statistics for enhanced summary
        const totalCouponBookings = confirmedBookingsWithProfiles.filter(b => {
          const couponUsageArray = (b as any).coupon_usage;
          return couponUsageArray && Array.isArray(couponUsageArray) && couponUsageArray.length > 0;
        }).length;

        const totalDiscountGiven = confirmedBookingsWithProfiles.reduce((sum, b) => {
          const couponUsageArray = (b as any).coupon_usage;
          const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
          return sum + (couponUsage ? (Number(couponUsage.discount_applied) || 0) : 0);
        }, 0);

        // Add enhanced summary rows
        excelData.push(['', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['=== SETTLEMENT SUMMARY ===', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['Total Bookings', bookingsWithProfiles.length.toString(), '', '', '', '', '', '', '', '', '']);
        // PHASE 3: Add cancelled booking count
        excelData.push(['Cancelled Booking Count', cancelledBookingsWithProfiles.length.toString(), '', '', '', '', '', '', '', '', '']);
        excelData.push(['Confirmed/Completed Bookings', confirmedBookingsWithProfiles.length.toString(), '', '', '', '', '', '', '', '', '']);
        // TASK 3: Add missing columns from AdminHome_Mobile.tsx reference
        excelData.push(['Bookings with Coupons', totalCouponBookings.toString(), '', '', '', '', '', '', '', '', '']);
        excelData.push(['Total Discount Given', `₹${totalDiscountGiven.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Gross Revenue', `₹${onlineGrossRevenue.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Platform Fee', `₹${onlinePlatformFee.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Net Settlement (What Venue Receives)', `₹${onlineNetSettlement.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['=== ONLINE BOOKINGS (Settlement-Affecting) ===', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['Online Bookings Count', onlineBookings.length.toString(), '', '', '', '', '', '', '', '', '']);
        excelData.push(['Online Gross Revenue', `₹${onlineGrossRevenue.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Online Platform Fee', `₹${onlinePlatformFee.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Online Net Settlement', `₹${onlineNetSettlement.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['=== OFFLINE BOOKINGS (Informational Only) ===', '', '', '', '', '', '', '', '', '', '']);
        excelData.push(['Offline Bookings Count (Cash)', offlineBookings.length.toString(), '', '', '', '', '', '', '', '', '']);
        excelData.push(['Offline Gross Revenue (Cash)', `₹${offlineGrossRevenue.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Offline Net Amount (Cash)', `₹${offlineNetAmount.toFixed(2)}`, '', '', '', '', '', '', '', '', '']);
        excelData.push(['Note', 'Offline bookings are venue-managed and NOT part of Grid२Play settlements', '', '', '', '', '', '', '', '', '']);
      }

      // CORRECT STRUCTURE: Add booking details section (without misplaced headers)
      excelData.push(['', '', '', '', '', '', '', '', '', '', '']);
      excelData.push(['=== BOOKING DETAILS ===', '', '', '', '', '', '', '', '', '', '']);

      // Create and download Excel file using EXACT MOBILE METHOD
      const ws = XLSX.utils.aoa_to_sheet(excelData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Settlement Report');

      // Generate filename using EXACT MOBILE FORMAT
      const filename = `${settlement.settlement_reference}_Settlement_Report.xlsx`;

      // Download file
      XLSX.writeFile(wb, filename);

      toast.success('Settlement report downloaded successfully!');
    } catch (error) {
      console.error('Error downloading settlement report:', error);
      toast.error('Failed to download settlement report');
    } finally {
      setDownloadingReport(false);
    }
  };

  // Navigate to previous/next week
  const navigateWeek = (direction: 'prev' | 'next') => {
    const newWeekStart = direction === 'prev' 
      ? subDays(currentWeekStart, 7)
      : addDays(currentWeekStart, 7);
    setCurrentWeekStart(newWeekStart);
  };

  // Get status badge for settlements
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return { color: 'bg-yellow-500', icon: <Clock className="w-3 h-3" />, text: 'Pending' };
      case 'processed':
        return { color: 'bg-blue-500', icon: <AlertCircle className="w-3 h-3" />, text: 'Processed' };
      case 'settled':
        return { color: 'bg-green-500', icon: <CheckCircle className="w-3 h-3" />, text: 'Settled' };
      default:
        return { color: 'bg-gray-500', icon: <Clock className="w-3 h-3" />, text: status };
    }
  };

  // PHASE 3: Calculate weekly summary with online/offline separation
  const weeklySummary = {
    // Online metrics (settlement-affecting)
    online_gross: dailyEarnings.reduce((sum, day) => sum + (day.online_gross_revenue || day.gross_revenue || 0), 0),
    online_net: dailyEarnings.reduce((sum, day) => sum + (day.online_net_revenue || day.net_revenue || 0), 0),
    online_bookings: dailyEarnings.reduce((sum, day) => sum + (day.online_bookings || 0), 0),

    // Offline metrics (informational only)
    offline_gross: dailyEarnings.reduce((sum, day) => sum + (day.offline_gross_revenue || 0), 0),
    offline_net: dailyEarnings.reduce((sum, day) => sum + (day.offline_net_revenue || 0), 0),
    offline_bookings: dailyEarnings.reduce((sum, day) => sum + (day.offline_bookings || 0), 0),

    // Total metrics
    total_bookings: dailyEarnings.reduce((sum, day) => sum + (day.total_bookings || 0), 0),
    days_with_data: dailyEarnings.filter(day => day.total_bookings > 0).length
  };

  // Get today's earnings
  const todayEarnings = dailyEarnings.find(day => day.is_current_day);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-emerald-500 mx-auto mb-4" />
          <p className="text-emerald-200">Loading earnings data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Earnings & Settlements</h1>
          <p className="text-emerald-200">Track daily earnings and weekly settlements</p>
        </div>
        <div className="flex items-center space-x-3">
          <Select value={selectedVenue} onValueChange={setSelectedVenue}>
            <SelectTrigger className="w-48 bg-black/40 border-white/20 text-white">
              <SelectValue placeholder="Select venue" />
            </SelectTrigger>
            <SelectContent className="bg-navy-dark border-white/20">
              {userRole === 'super_admin' && (
                <SelectItem value="all" className="text-white">All Venues</SelectItem>
              )}
              {venuesWithNames.map((venue) => (
                <SelectItem key={venue.venue_id} value={venue.venue_id} className="text-white">
                  {venue.venue_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => fetchEarningsData(true)}
            disabled={refreshing}
            className="text-white hover:bg-white/10"
          >
            <RefreshCw className={`h-5 w-5 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-emerald-900/50 rounded-xl">
          <TabsTrigger value="overview" className="data-[state=active]:bg-emerald-500 data-[state=active]:text-white text-emerald-200">
            Overview
          </TabsTrigger>
          <TabsTrigger value="daily" className="data-[state=active]:bg-emerald-500 data-[state=active]:text-white text-emerald-200">
            Daily Earnings
          </TabsTrigger>
          <TabsTrigger value="settlements" className="data-[state=active]:bg-emerald-500 data-[state=active]:text-white text-emerald-200">
            Settlements
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          {/* PHASE 3: Tabbed Online/Offline View */}
          <Tabs defaultValue="online" className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-gradient-to-r from-slate-800/60 to-slate-700/40 backdrop-blur-sm rounded-2xl border border-emerald-500/20">
              <TabsTrigger
                value="online"
                className="text-sm py-3 rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:text-white data-[state=active]:shadow-lg text-emerald-200 transition-all duration-300"
              >
                🟢 Online Revenue
                <span className="ml-2 text-xs bg-emerald-600/30 px-2 py-1 rounded-full">Settlement</span>
              </TabsTrigger>
              <TabsTrigger
                value="offline"
                className="text-sm py-3 rounded-xl data-[state=active]:bg-gray-500 data-[state=active]:text-white data-[state=active]:shadow-lg text-gray-300 transition-all duration-300"
              >
                🔘 Cash Revenue
                <span className="ml-2 text-xs bg-gray-600/30 px-2 py-1 rounded-full">Info Only</span>
              </TabsTrigger>
            </TabsList>

            {/* Online Revenue Tab */}
            <TabsContent value="online" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Today's Online Revenue */}
                <Card className="bg-gradient-to-r from-emerald-500/20 to-green-500/20 border-emerald-500/30">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-emerald-300 flex items-center text-sm">
                      <Activity className="w-4 h-4 mr-2" />
                      Today's Online Revenue
                      <span className="ml-2 text-xs bg-emerald-600/30 px-2 py-1 rounded-full">Settlement</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">
                      ₹{(todayEarnings?.online_gross_revenue || todayEarnings?.gross_revenue || 0).toFixed(0)}
                    </div>
                    <div className="text-sm text-emerald-200">
                      Net: ₹{(todayEarnings?.online_net_revenue || todayEarnings?.net_revenue || 0).toFixed(0)}
                    </div>
                    <div className="text-xs text-emerald-300 mt-1">
                      {todayEarnings?.online_bookings || 0} online bookings
                    </div>
                  </CardContent>
                </Card>

                {/* Week Online Gross */}
                <Card className="bg-black/40 border-emerald-500/30">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white flex items-center text-sm">
                      <TrendingUp className="w-4 h-4 mr-2" />
                      Week Online Gross
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-emerald-300">
                      ₹{weeklySummary.online_gross.toFixed(0)}
                    </div>
                    <div className="text-sm text-emerald-200">
                      {weeklySummary.online_bookings} online bookings
                    </div>
                    <div className="text-xs text-emerald-300">
                      Goes to settlement
                    </div>
                  </CardContent>
                </Card>

                {/* Week Online Net */}
                <Card className="bg-black/40 border-emerald-500/30">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white flex items-center text-sm">
                      <Banknote className="w-4 h-4 mr-2" />
                      Week Net Settlement
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-300">
                      ₹{weeklySummary.online_net.toFixed(0)}
                    </div>
                    <div className="text-sm text-green-200">
                      After platform fees
                    </div>
                    <div className="text-xs text-green-300">
                      Your payout amount
                    </div>
                  </CardContent>
                </Card>

                {/* Active Days */}
                <Card className="bg-black/40 border-emerald-500/30">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white flex items-center text-sm">
                      <Calendar className="w-4 h-4 mr-2" />
                      Active Days
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-300">
                      {getActiveDaysDisplay(currentWeekStart, weeklySummary.days_with_data)}
                    </div>
                    <div className="text-sm text-blue-200">
                      {getActiveDaysLabel(currentWeekStart)}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Offline Revenue Tab */}
            <TabsContent value="offline" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Today's Cash Revenue */}
                <Card className="bg-gradient-to-r from-gray-500/20 to-gray-600/20 border-gray-500/30">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-gray-300 flex items-center text-sm">
                      <Activity className="w-4 h-4 mr-2" />
                      Today's Cash Revenue
                      <span className="ml-2 text-xs bg-gray-600/30 px-2 py-1 rounded-full">Info Only</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">
                      ₹{(todayEarnings?.offline_gross_revenue || 0).toFixed(0)}
                    </div>
                    <div className="text-sm text-gray-200">
                      Venue keeps: ₹{(todayEarnings?.offline_gross_revenue || 0).toFixed(0)}
                    </div>
                    <div className="text-xs text-gray-300 mt-1">
                      {todayEarnings?.offline_bookings || 0} cash bookings
                    </div>
                  </CardContent>
                </Card>

                {/* Week Cash Gross */}
                <Card className="bg-black/40 border-gray-500/30">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white flex items-center text-sm">
                      <TrendingUp className="w-4 h-4 mr-2" />
                      Week Cash Gross
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-gray-300">
                      ₹{weeklySummary.offline_gross.toFixed(0)}
                    </div>
                    <div className="text-sm text-gray-200">
                      {weeklySummary.offline_bookings} cash bookings
                    </div>
                    <div className="text-xs text-gray-300">
                      Not in settlement
                    </div>
                  </CardContent>
                </Card>

                {/* Week Cash Net */}
                <Card className="bg-black/40 border-gray-500/30">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white flex items-center text-sm">
                      <Banknote className="w-4 h-4 mr-2" />
                      Week Cash Total
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-gray-300">
                      ₹{weeklySummary.offline_gross.toFixed(0)}
                    </div>
                    <div className="text-sm text-gray-200">
                      Venue keeps 100%
                    </div>
                    <div className="text-xs text-gray-300">
                      No platform fees
                    </div>
                  </CardContent>
                </Card>

                {/* Total Cash Days */}
                <Card className="bg-black/40 border-gray-500/30">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white flex items-center text-sm">
                      <Calendar className="w-4 h-4 mr-2" />
                      Cash Booking Days
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-gray-300">
                      {dailyEarnings.filter(day => (day.offline_bookings || 0) > 0).length}
                    </div>
                    <div className="text-sm text-gray-200">
                      Days with cash bookings
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>


        </TabsContent>

        <TabsContent value="daily" className="mt-6">
          {/* Week Navigation */}
          <div className="flex items-center justify-between bg-black/40 rounded-lg p-4 mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateWeek('prev')}
              className="text-white hover:bg-white/10"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Previous Week
            </Button>
            <div className="text-center">
              <div className="font-semibold text-white">
                {format(currentWeekStart, 'MMM dd')} - {format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'MMM dd, yyyy')}
              </div>
              <div className="text-sm text-emerald-300">Weekly Cycle</div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateWeek('next')}
              className="text-white hover:bg-white/10"
            >
              Next Week
              <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </div>

          {/* Daily Earnings Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {dailyEarnings.map((day) => (
              <Card key={`${day.venue_id}-${day.cycle_date}`} className="bg-black/40 border-white/20">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white text-sm flex items-center justify-between">
                    <span>{day.cycle_day_name}</span>
                    {day.is_current_day && <Badge className="bg-emerald-500 text-white text-xs">Today</Badge>}
                  </CardTitle>
                  <div className="text-xs text-gray-400">{format(parseISO(day.cycle_date), 'MMM dd')}</div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {/* PHASE 3: Online Revenue (Settlement-affecting) */}
                    <div className="flex justify-between">
                      <span className="text-sm text-emerald-300 flex items-center">
                        🟢 Online:
                        <span className="ml-1 text-xs bg-emerald-600/30 px-1 py-0.5 rounded text-emerald-200">Settlement</span>
                      </span>
                      <span className="text-sm font-semibold text-emerald-300">
                        ₹{(day.online_gross_revenue || day.gross_revenue || 0).toFixed(0)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-green-300">Net Settlement:</span>
                      <span className="text-sm font-semibold text-green-300">
                        ₹{(day.online_net_revenue || day.net_revenue || 0).toFixed(0)}
                      </span>
                    </div>

                    {/* PHASE 3: Offline Revenue (Informational) - Always show */}
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-400 flex items-center">
                        🔘 Cash:
                        <span className="ml-1 text-xs bg-gray-600/30 px-1 py-0.5 rounded text-gray-300">Info</span>
                      </span>
                      <span className="text-sm font-semibold text-gray-400">
                        ₹{(day.offline_gross_revenue || 0).toFixed(0)}
                      </span>
                    </div>

                    {/* PHASE 3: Booking breakdown */}
                    <div className="flex justify-between">
                      <span className="text-sm text-blue-300">Bookings:</span>
                      <span className="text-sm font-semibold text-blue-300">
                        {(day.online_bookings || 0) + (day.offline_bookings || 0) || day.total_bookings}
                        <span className="text-xs text-gray-400 ml-1">
                          ({day.online_bookings || 0}+{day.offline_bookings || 0})
                        </span>
                      </span>
                    </div>

                    {day.venue_name && (
                      <div className="text-xs text-gray-400 truncate">{day.venue_name}</div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="settlements" className="mt-6">
          {/* Settlements List */}
          <div className="space-y-4">
            {settlements.length === 0 ? (
              <Card className="bg-black/40 border-white/20">
                <CardContent className="p-8 text-center">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <div className="text-lg text-gray-300 mb-2">No settlements found</div>
                  <div className="text-sm text-gray-400">Settlements will appear here once weekly cycles are processed</div>
                </CardContent>
              </Card>
            ) : (
              settlements.map((settlement) => {
                const statusBadge = getStatusBadge(settlement.status);
                
                return (
                  <Card 
                    key={settlement.settlement_id}
                    className="bg-black/40 border-white/20 cursor-pointer hover:bg-black/60 transition-colors"
                    onClick={() => {
                      setSelectedSettlement(settlement);
                      setSettlementDetailsOpen(true);
                      fetchCouponAnalytics(settlement);
                      // HUDLE APPROACH: Calculate online/offline metrics when opening details
                      calculateOnlineOfflineMetrics(settlement);
                    }}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-3">
                            <Badge className={`${statusBadge.color} text-white flex items-center space-x-1`}>
                              {statusBadge.icon}
                              <span>{statusBadge.text}</span>
                            </Badge>
                            <span className="text-sm font-mono text-gray-300">{settlement.settlement_reference}</span>
                          </div>
                          
                          <div className="text-white font-semibold">{settlement.venue_name}</div>
                          
                          <div className="text-sm text-gray-400">
                            {format(parseISO(settlement.settlement_week_start), 'MMM dd')} - {format(parseISO(settlement.settlement_week_end), 'MMM dd, yyyy')}
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="text-2xl font-bold text-emerald-400">₹{settlement.total_net_revenue.toFixed(0)}</div>
                          <div className="text-sm text-gray-400">Net Revenue</div>
                          <div className="text-xs text-gray-500">{settlement.total_bookings} bookings</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Enhanced Settlement Details Modal */}
      <Dialog open={settlementDetailsOpen} onOpenChange={closeSettlementDetails}>
        <DialogContent className="bg-navy-dark border-white/20 text-white max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="text-emerald-300 flex items-center justify-between">
              Settlement Details
              {userRole === 'super_admin' && selectedSettlement && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSettlementDetailsOpen(false);
                    openManagement(selectedSettlement);
                  }}
                  className="text-yellow-300 hover:bg-yellow-500/20"
                >
                  <Settings className="w-4 h-4 mr-1" />
                  Manage
                </Button>
              )}
            </DialogTitle>
          </DialogHeader>

          <ScrollArea className="flex-1 overflow-y-auto pr-4">
            {selectedSettlement && (
              <div className="space-y-6">
                {/* Super Admin Management Notice */}
                {userRole === 'super_admin' && (
                  <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                    <div className="flex items-start space-x-2">
                      <Settings className="w-4 h-4 text-yellow-400 mt-0.5 shrink-0" />
                      <div className="text-xs text-yellow-300">
                        <strong>Super Admin:</strong> You can manage this settlement's status, notes, and dates using the "Manage" button above.
                      </div>
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm text-gray-400">Reference</label>
                      <div className="font-mono text-sm">{selectedSettlement.settlement_reference}</div>
                    </div>

                    <div>
                      <label className="text-sm text-gray-400">Venue</label>
                      <div className="font-semibold">{selectedSettlement.venue_name}</div>
                    </div>

                    <div>
                      <label className="text-sm text-gray-400">Settlement Period</label>
                      <div>
                        {format(parseISO(selectedSettlement.settlement_week_start), 'MMM dd')} - {format(parseISO(selectedSettlement.settlement_week_end), 'MMM dd, yyyy')}
                      </div>
                    </div>

                    <div>
                      <label className="text-sm text-gray-400">Status</label>
                      <div className="mt-1">
                        <Badge className={`${getStatusBadge(selectedSettlement.status).color} text-white`}>
                          {getStatusBadge(selectedSettlement.status).text}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="text-sm text-gray-400">Expected Settlement Date</label>
                      <div>{format(parseISO(selectedSettlement.expected_settlement_date), 'MMM dd, yyyy')}</div>
                    </div>

                    <div>
                      <label className="text-sm text-gray-400">Total Bookings</label>
                      <div className="font-semibold text-blue-300">{selectedSettlement.total_bookings}</div>
                    </div>

                    <div>
                      <label className="text-sm text-gray-400">Created</label>
                      <div>{format(parseISO(selectedSettlement.created_at), 'MMM dd, yyyy HH:mm')}</div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Revenue Breakdown */}
                <div className="bg-emerald-500/20 rounded-lg p-6 space-y-4">
                  <h3 className="text-lg font-semibold text-emerald-300 mb-4">Financial Summary</h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-emerald-300">₹{selectedSettlement.total_gross_revenue.toFixed(0)}</div>
                      <div className="text-sm text-emerald-200">Gross Revenue</div>
                    </div>

                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-300">₹{selectedSettlement.total_platform_fees.toFixed(0)}</div>
                      <div className="text-sm text-red-200">Platform Fees</div>
                    </div>

                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-300">₹{selectedSettlement.total_net_revenue.toFixed(0)}</div>
                      <div className="text-sm text-green-200">Net Revenue</div>
                    </div>
                  </div>

                  <div className="border-t border-emerald-400/30 pt-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-emerald-200">Platform Fee Rate:</span>
                      <span className="font-semibold">{((selectedSettlement.total_platform_fees / selectedSettlement.total_gross_revenue) * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-emerald-200">Average per Booking:</span>
                      <span className="font-semibold">₹{(selectedSettlement.total_gross_revenue / selectedSettlement.total_bookings).toFixed(0)}</span>
                    </div>
                  </div>
                </div>

                {/* PHASE 3: Bookings Section */}
                <div className="bg-blue-500/20 rounded-lg p-6 space-y-4">
                  <h3 className="text-lg font-semibold text-blue-300 mb-4 flex items-center">
                    <BookOpen className="w-5 h-5 mr-2 text-blue-400" />
                    Bookings Overview
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex justify-between">
                      <span className="text-blue-200">Total Bookings:</span>
                      <span className="font-semibold text-blue-300">{selectedSettlement.total_bookings}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-200">Cancelled Booking Count:</span>
                      <span className="font-semibold text-red-300">{cancelledBookingCount !== null ? cancelledBookingCount : 'Loading...'}</span>
                    </div>
                  </div>
                </div>

                {/* HUDLE APPROACH: Online vs Offline Booking Breakdown */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Online Bookings Section (Settlement-Affecting) */}
                  <div className="bg-emerald-500/20 rounded-lg p-6 space-y-4">
                    <h3 className="text-lg font-semibold text-emerald-300 mb-4 flex items-center">
                      <span className="w-3 h-3 bg-emerald-400 rounded-full mr-2"></span>
                      Online Bookings (Settlement-Affecting)
                    </h3>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-emerald-200">Bookings Count:</span>
                        <span className="font-semibold text-emerald-300">{onlineMetrics ? onlineMetrics.count : 'Loading...'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-emerald-200">Gross Revenue:</span>
                        <span className="font-semibold text-emerald-300">₹{onlineMetrics ? onlineMetrics.grossRevenue.toFixed(2) : 'Loading...'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-emerald-200">Net Settlement:</span>
                        <span className="font-semibold text-emerald-300">₹{onlineMetrics ? onlineMetrics.netSettlement.toFixed(2) : 'Loading...'}</span>
                      </div>
                    </div>

                    <div className="text-xs text-emerald-200 mt-4 italic border-t border-emerald-400/30 pt-3">
                      These bookings are included in Grid२Play settlements
                    </div>
                  </div>

                  {/* Offline Bookings Section (Informational Only) */}
                  <div className="bg-gray-500/20 rounded-lg p-6 space-y-4">
                    <h3 className="text-lg font-semibold text-gray-300 mb-4 flex items-center">
                      <span className="w-3 h-3 bg-gray-400 rounded-full mr-2"></span>
                      Offline Bookings (Informational Only)
                    </h3>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-200">Bookings Count:</span>
                        <span className="font-semibold text-gray-300">{offlineMetrics ? offlineMetrics.count : 'Loading...'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-200">Gross Revenue:</span>
                        <span className="font-semibold text-gray-300">₹{offlineMetrics ? offlineMetrics.grossRevenue.toFixed(2) : 'Loading...'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-200">Net Amount:</span>
                        <span className="font-semibold text-gray-300">₹{offlineMetrics ? offlineMetrics.netAmount.toFixed(2) : 'Loading...'}</span>
                      </div>
                    </div>

                    <div className="text-xs text-gray-300 mt-4 italic border-t border-gray-400/30 pt-3">
                      Cash bookings - venue-managed, NOT part of Grid२Play settlements
                    </div>
                  </div>
                </div>

                {/* Coupon Usage Summary */}
                {couponAnalytics && (
                  <div className="bg-purple-500/20 rounded-lg p-6 space-y-4">
                    <h3 className="text-lg font-semibold text-purple-300 mb-4 flex items-center">
                      <span className="mr-2">🎫</span>
                      Coupon Usage Summary
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-300">{couponAnalytics.totalCouponBookings}</div>
                        <div className="text-sm text-purple-200">Bookings with Coupons</div>
                        <div className="text-xs text-gray-400 mt-1">
                          {selectedSettlement.total_bookings > 0
                            ? `${((couponAnalytics.totalCouponBookings / selectedSettlement.total_bookings) * 100).toFixed(1)}% of total`
                            : '0% of total'
                          }
                        </div>
                      </div>

                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-300">₹{couponAnalytics.totalDiscountGiven.toFixed(0)}</div>
                        <div className="text-sm text-red-200">Total Discount Given</div>
                        <div className="text-xs text-gray-400 mt-1">
                          {selectedSettlement.total_gross_revenue > 0
                            ? `${((couponAnalytics.totalDiscountGiven / selectedSettlement.total_gross_revenue) * 100).toFixed(1)}% of gross revenue`
                            : '0% of gross revenue'
                          }
                        </div>
                      </div>

                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-300">₹{(selectedSettlement.total_gross_revenue + couponAnalytics.totalDiscountGiven).toFixed(0)}</div>
                        <div className="text-sm text-orange-200">Revenue Before Discounts</div>
                        <div className="text-xs text-gray-400 mt-1">
                          Original booking value
                        </div>
                      </div>
                    </div>

                    {couponAnalytics.couponBreakdown.length > 0 && (
                      <div className="border-t border-purple-400/30 pt-4">
                        <h4 className="text-sm font-semibold text-purple-200 mb-3">Coupon Breakdown</h4>
                        <div className="space-y-2">
                          {couponAnalytics.couponBreakdown.map((coupon, index) => (
                            <div key={index} className="flex justify-between items-center bg-black/30 rounded p-2">
                              <div className="flex items-center space-x-2">
                                <span className="font-mono text-sm bg-purple-600/30 px-2 py-1 rounded">{coupon.code}</span>
                                <span className="text-sm text-gray-300">{coupon.count} booking{coupon.count !== 1 ? 's' : ''}</span>
                              </div>
                              <span className="font-semibold text-red-300">-₹{coupon.totalDiscount.toFixed(0)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="border-t border-purple-400/30 pt-4">
                      <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3">
                        <div className="text-xs text-blue-300">
                          <strong>Impact on Settlement:</strong> Coupons reduced gross revenue by ₹{couponAnalytics.totalDiscountGiven.toFixed(0)},
                          which means the venue would have earned ₹{(selectedSettlement.total_net_revenue + (couponAnalytics.totalDiscountGiven * (1 - (selectedSettlement.total_platform_fees / selectedSettlement.total_gross_revenue)))).toFixed(0)}
                          without discount promotions.
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {selectedSettlement.notes && (
                  <div className="bg-gray-500/20 rounded-lg p-4">
                    <label className="text-sm text-gray-400">Admin Notes</label>
                    <div className="text-white mt-2">{selectedSettlement.notes}</div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-3">
                  {/* Download Report Button */}
                  {(selectedSettlement.status === 'processed' || selectedSettlement.status === 'settled') && (
                    <Button
                      onClick={() => downloadSettlementReport(selectedSettlement)}
                      disabled={downloadingReport}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                    >
                      {downloadingReport ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Generating Report...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4 mr-2" />
                          Download Detailed Report
                        </>
                      )}
                    </Button>
                  )}

                  <Button
                    onClick={() => window.alert('Feature coming soon!')}
                    className="w-full"
                    variant="outline"
                  >
                    Contact Support
                  </Button>
                </div>
              </div>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Settlement Management Modal (Super Admin Only) */}
      <SettlementManagementModal
        isOpen={managementOpen}
        onClose={() => {
          setManagementOpen(false);
          setSelectedSettlement(null);
        }}
        settlement={selectedSettlement}
        onUpdate={handleSettlementUpdate}
      />
    </div>
  );
};

export default EarningsDashboard;

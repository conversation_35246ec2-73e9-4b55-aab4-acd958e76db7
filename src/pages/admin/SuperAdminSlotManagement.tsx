import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Navigate } from 'react-router-dom';
import { toast } from '@/components/ui/use-toast';
import SuperAdminSlotManagement from '@/components/admin/SuperAdminSlotManagement';
import { ArrowLeft, Shield, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { getDisplayName } from '@/utils/security';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const SuperAdminSlotManagementPage: React.FC = () => {
  const { user } = useAuth();
  const [userRole, setUserRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [authorized, setAuthorized] = useState(false);

  useEffect(() => {
    checkUserRole();
  }, [user]);

  const checkUserRole = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching user role:', error);
        toast({
          title: 'Error',
          description: 'Failed to verify user permissions',
          variant: 'destructive'
        });
        setLoading(false);
        return;
      }

      setUserRole(data?.role || null);
      
      // Only super_admin can access this page
      if (data?.role === 'super_admin') {
        setAuthorized(true);
      } else {
        toast({
          title: 'Access Denied',
          description: 'This page is only accessible to super administrators',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error checking user role:', error);
      toast({
        title: 'Error',
        description: 'Failed to verify user permissions',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-navy-900 to-navy-800 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto mb-4"></div>
          <p className="text-white">Verifying permissions...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (!authorized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-navy-900 to-navy-800 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <CardTitle className="text-red-600">Access Denied</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              This page is restricted to super administrators only.
            </p>
            <p className="text-sm text-gray-500">
              Current role: {userRole || 'Unknown'}
            </p>
            <Button 
              onClick={() => window.history.back()}
              variant="outline"
              className="w-full"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-navy-900 to-navy-800">
      {/* Header */}
      <header className="bg-gradient-to-r from-black/90 to-navy-900/90 backdrop-blur-md shadow-lg border-b border-navy-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.history.back()}
                className="text-gray-300 hover:text-white"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-emerald-600 rounded-lg">
                  <Settings className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">
                    Super Admin Slot Management
                  </h1>
                  <p className="text-sm text-gray-300">
                    Comprehensive slot availability management across all venues
                  </p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="text-right">
                <p className="text-sm font-medium text-white">{getDisplayName(user?.user_metadata?.full_name, user.email)}</p>
                <p className="text-xs text-emerald-400 flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  Super Administrator
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-xl shadow-xl border border-navy-200/20 overflow-hidden">
          <div className="p-6">
            <SuperAdminSlotManagement />
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="mt-12 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-gray-400 text-sm">
            <p>Grid2Play Super Admin Panel - Slot Management System</p>
            <p className="mt-1">
              Manage slot availability across all venues with advanced controls and bulk operations
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SuperAdminSlotManagementPage;

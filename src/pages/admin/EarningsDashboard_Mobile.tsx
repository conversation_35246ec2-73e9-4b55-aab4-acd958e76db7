
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Link, useNavigate } from 'react-router-dom';
import {
  ArrowLeft, Calendar, DollarSign, TrendingUp, 
  Clock, Banknote, Activity, Eye, ChevronRight,
  Loader2, AlertCircle, CheckCircle, RefreshCw, Info
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { supabase } from '@/integrations/supabase/client';
import { format, startOfWeek, endOfWeek, addDays, subDays } from 'date-fns';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "react-hot-toast";
import { getActiveDaysDisplay, getActiveDaysLabel } from '@/utils/weeklyUtils';

interface DailyEarnings {
  venue_id: string;
  venue_name: string;
  cycle_date: string;
  cycle_day_name: string;
  total_bookings: number;
  confirmed_bookings: number;

  // Legacy columns (now contain online values only for settlement compatibility)
  gross_revenue: number;
  platform_fee_amount: number;
  net_revenue: number;

  // PHASE 3: New online/offline separation columns
  online_gross_revenue?: number;
  online_platform_fees?: number;
  online_net_revenue?: number;
  online_bookings?: number;
  offline_gross_revenue?: number;
  offline_platform_fees?: number;
  offline_net_revenue?: number;
  offline_bookings?: number;

  is_current_day: boolean;
  is_frozen: boolean;
}

interface WeeklySummary {
  week_start: string;
  week_end: string;

  // PHASE 3: Online/offline separation
  online_gross: number;
  online_net: number;
  online_bookings: number;
  offline_gross: number;
  offline_net: number;
  offline_bookings: number;

  // Legacy totals
  total_gross: number;
  total_net: number;
  total_bookings: number;
  days_with_data: number;
}

interface EarningsDashboardMobileProps {
  userRole?: string;
  adminVenues?: Array<{ venue_id: string; venue_name?: string }>;
}

const EarningsDashboard_Mobile: React.FC<EarningsDashboardMobileProps> = ({
  userRole: propUserRole,
  adminVenues: propAdminVenues
}) => {
  const { user, userRole: contextUserRole } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  // Use props if provided, otherwise use context
  const userRole = propUserRole || contextUserRole;
  const [adminVenues, setAdminVenues] = useState<Array<{ venue_id: string; venue_name?: string }>>(propAdminVenues || []);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dailyEarnings, setDailyEarnings] = useState<DailyEarnings[]>([]);
  const [weeklySummary, setWeeklySummary] = useState<WeeklySummary | null>(null);
  const [selectedVenue, setSelectedVenue] = useState<string>('all');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentWeekStart, setCurrentWeekStart] = useState(startOfWeek(new Date(), { weekStartsOn: 1 }));

  // Redirect to desktop if not mobile
  useEffect(() => {
    if (!isMobile) {
      navigate('/admin/earnings');
    }
  }, [isMobile, navigate]);

  // FIX: Fetch admin venues for venue filter
  useEffect(() => {
    const fetchAdminVenues = async () => {
      if (!user?.id || propAdminVenues) return; // Skip if props provided

      try {
        if (userRole === 'admin') {
          const { data, error } = await supabase.rpc('get_admin_venues');
          if (!error && data) {
            // Fetch venue names
            const venueIds = data.map((v: { venue_id: string }) => v.venue_id);
            const { data: venueDetails, error: venueError } = await supabase
              .from('venues')
              .select('id, name')
              .in('id', venueIds)
              .eq('is_active', true);

            if (!venueError && venueDetails) {
              const venuesWithNames = data.map((v: { venue_id: string }) => ({
                venue_id: v.venue_id,
                venue_name: venueDetails.find(vd => vd.id === v.venue_id)?.name
              }));
              setAdminVenues(venuesWithNames);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching admin venues:', error);
      }
    };

    fetchAdminVenues();
  }, [user?.id, userRole, propAdminVenues]);

  // Check if we're viewing current week or historical week
  const isCurrentWeek = () => {
    const today = new Date();
    const currentWeekStartDate = startOfWeek(today, { weekStartsOn: 1 });
    return format(currentWeekStart, 'yyyy-MM-dd') === format(currentWeekStartDate, 'yyyy-MM-dd');
  };

  // Fetch earnings data
  const fetchEarningsData = async (showRefreshIndicator = false) => {
    if (!user?.id) return;

    if (showRefreshIndicator) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      // PHASE 3: Use get_admin_daily_earnings function (same as desktop)
      const weekStart = format(currentWeekStart, 'yyyy-MM-dd');
      const weekEnd = format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'yyyy-MM-dd');

      const { data: earningsData, error: earningsError } = await supabase
        .rpc('get_admin_daily_earnings', {
          p_start_date: weekStart,
          p_end_date: weekEnd,
          p_venue_id: selectedVenue === 'all' ? null : selectedVenue
        });

      if (earningsError) {
        console.error('Error fetching daily earnings:', earningsError);
        throw earningsError;
      }

      if (!earningsData || earningsData.length === 0) {
        console.log('No earnings data found');
        setDailyEarnings([]);
        setWeeklySummary({
          week_start: weekStart,
          week_end: weekEnd,
          online_gross: 0,
          online_net: 0,
          online_bookings: 0,
          offline_gross: 0,
          offline_net: 0,
          offline_bookings: 0,
          total_gross: 0,
          total_net: 0,
          total_bookings: 0,
          days_with_data: 0
        });
        return;
      }

      // PHASE 3: Process daily earnings data (same as desktop approach)
      console.log('Daily earnings data found:', earningsData);

      // PHASE 3: Use daily_earnings data directly (same as desktop)
      const processedEarnings = earningsData;

      // PHASE 3: Calculate weekly summary with online/offline separation (same as desktop)
      if (processedEarnings && processedEarnings.length > 0) {
        const summary: WeeklySummary = {
          week_start: weekStart,
          week_end: weekEnd,

          // PHASE 3: Online metrics (settlement-affecting)
          online_gross: processedEarnings.reduce((sum, day: any) => sum + ((day.online_gross_revenue || day.gross_revenue) || 0), 0),
          online_net: processedEarnings.reduce((sum, day: any) => sum + ((day.online_net_revenue || day.net_revenue) || 0), 0),
          online_bookings: processedEarnings.reduce((sum, day: any) => sum + (day.online_bookings || 0), 0),

          // PHASE 3: Offline metrics (informational)
          offline_gross: processedEarnings.reduce((sum, day: any) => sum + (day.offline_gross_revenue || 0), 0),
          offline_net: processedEarnings.reduce((sum, day: any) => sum + (day.offline_net_revenue || 0), 0),
          offline_bookings: processedEarnings.reduce((sum, day: any) => sum + (day.offline_bookings || 0), 0),

          // Legacy totals
          total_gross: processedEarnings.reduce((sum, day: any) => sum + (day.gross_revenue || 0), 0),
          total_net: processedEarnings.reduce((sum, day: any) => sum + (day.net_revenue || 0), 0),
          total_bookings: processedEarnings.reduce((sum, day: any) => sum + (day.total_bookings || 0), 0),
          days_with_data: processedEarnings.filter((day: any) => day.total_bookings > 0).length
        };
        setWeeklySummary(summary);

        // PHASE 3: Use processed earnings directly for daily earnings display
        setDailyEarnings(processedEarnings);
      } else {
        setWeeklySummary({
          week_start: format(currentWeekStart, 'yyyy-MM-dd'),
          week_end: format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'yyyy-MM-dd'),
          online_gross: 0,
          online_net: 0,
          online_bookings: 0,
          offline_gross: 0,
          offline_net: 0,
          offline_bookings: 0,
          total_gross: 0,
          total_net: 0,
          total_bookings: 0,
          days_with_data: 0
        });
        setDailyEarnings([]);
      }

    } catch (error) {
      console.error('Error fetching earnings data:', error);
      toast.error('Failed to load earnings data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchEarningsData();
  }, [user?.id, currentWeekStart, selectedVenue]);

  // Navigate to previous/next week
  const navigateWeek = (direction: 'prev' | 'next') => {
    const newWeekStart = direction === 'prev' 
      ? subDays(currentWeekStart, 7)
      : addDays(currentWeekStart, 7);
    setCurrentWeekStart(newWeekStart);
  };

  // Get today's earnings
  const todayEarnings = dailyEarnings.find(day => day.is_current_day);

  if (loading) {
    return (
      <div className="min-h-screen bg-navy-dark text-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-emerald-500 mx-auto mb-4" />
          <p className="text-emerald-200">Loading earnings data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-navy-dark text-white">
      {/* Header */}
      <div className="bg-black/80 shadow-md sticky top-0 z-10">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/admin/mobile-home')}
                className="mr-2 text-white hover:bg-white/10"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="font-bold text-lg text-white">Earnings Dashboard</h1>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => fetchEarningsData(true)}
              disabled={refreshing}
              className="text-white hover:bg-white/10"
            >
              <RefreshCw className={`h-5 w-5 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>

          {/* FIX: Add Venue Filter for Mobile */}
          {adminVenues.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-emerald-200 font-medium">Venue:</span>
              <Select value={selectedVenue} onValueChange={setSelectedVenue}>
                <SelectTrigger className="flex-1 bg-black/40 border-white/20 text-white text-sm">
                  <SelectValue placeholder="Select venue" />
                </SelectTrigger>
                <SelectContent className="bg-navy-dark border-white/20">
                  <SelectItem value="all" className="text-white">All Venues</SelectItem>
                  {adminVenues.map((venue) => (
                    <SelectItem key={venue.venue_id} value={venue.venue_id} className="text-white">
                      {venue.venue_name || `Venue ${venue.venue_id.slice(0, 8)}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Time Period Context Banner */}
        <Card className={`border-2 ${
          isCurrentWeek() 
            ? 'bg-emerald-500/10 border-emerald-500/30' 
            : 'bg-blue-500/10 border-blue-500/30'
        }`}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-full ${
                isCurrentWeek() 
                  ? 'bg-emerald-500/20' 
                  : 'bg-blue-500/20'
              }`}>
                {isCurrentWeek() ? (
                  <Activity className="w-5 h-5 text-emerald-400" />
                ) : (
                  <Clock className="w-5 h-5 text-blue-400" />
                )}
              </div>
              <div className="flex-1">
                <div className={`font-semibold ${
                  isCurrentWeek() ? 'text-emerald-300' : 'text-blue-300'
                }`}>
                  {isCurrentWeek() ? 'Current Week Earnings' : 'Historical Week Data'}
                </div>
                <div className="text-sm text-gray-300">
                  {isCurrentWeek() 
                    ? 'Live earnings data - updates in real-time as bookings are made'
                    : 'Historical data from completed week - final amounts may differ from settlements'
                  }
                </div>
              </div>
              {!isCurrentWeek() && (
                <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                  <Info className="w-3 h-3 mr-1" />
                  Historical
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Today's Performance - only show for current week */}
        {isCurrentWeek() && (
          <Card className="bg-gradient-to-r from-emerald-500/20 to-green-500/20 border-emerald-500/30">
            <CardHeader className="pb-3">
              <CardTitle className="text-emerald-300 flex items-center">
                <Activity className="w-5 h-5 mr-2" />
                Today's Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              {todayEarnings ? (
                <div className="space-y-3">
                  {/* PHASE 3: Online Revenue (Settlement-affecting) */}
                  <div className="bg-emerald-500/10 rounded-lg p-3 border border-emerald-500/20">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-emerald-300 text-sm font-medium">🟢 Online Revenue</span>
                      <span className="text-xs bg-emerald-600/30 px-2 py-1 rounded text-emerald-200">Settlement</span>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center">
                        <div className="text-xl font-bold text-emerald-300">₹{((todayEarnings as any).online_gross_revenue || todayEarnings.gross_revenue || 0).toFixed(0)}</div>
                        <div className="text-xs text-emerald-200">Online Gross</div>
                      </div>
                      <div className="text-center">
                        <div className="text-xl font-bold text-emerald-300">₹{((todayEarnings as any).online_net_revenue || todayEarnings.net_revenue || 0).toFixed(0)}</div>
                        <div className="text-xs text-emerald-200">Net Settlement</div>
                      </div>
                    </div>
                    <div className="text-center mt-2">
                      <div className="text-sm text-emerald-300">{(todayEarnings as any).online_bookings || 0} online bookings</div>
                    </div>
                  </div>

                  {/* PHASE 3: Offline Revenue (Informational) - Always show */}
                  <div className="bg-gray-500/10 rounded-lg p-3 border border-gray-500/20">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-gray-300 text-sm font-medium">🔘 Cash Revenue</span>
                      <span className="text-xs bg-gray-600/30 px-2 py-1 rounded text-gray-300">Info Only</span>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center">
                        <div className="text-xl font-bold text-gray-300">₹{((todayEarnings as any).offline_gross_revenue || 0).toFixed(0)}</div>
                        <div className="text-xs text-gray-200">Cash Gross</div>
                      </div>
                      <div className="text-center">
                        <div className="text-xl font-bold text-gray-300">₹{((todayEarnings as any).offline_gross_revenue || 0).toFixed(0)}</div>
                        <div className="text-xs text-gray-200">Venue Keeps</div>
                      </div>
                    </div>
                    <div className="text-center mt-2">
                      <div className="text-sm text-gray-300">{(todayEarnings as any).offline_bookings || 0} cash bookings</div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="text-lg text-emerald-200">No bookings today yet</div>
                  <div className="text-sm text-emerald-300">Revenue will appear when bookings are made</div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Week Navigation */}
        <div className="flex items-center justify-between bg-black/40 rounded-lg p-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateWeek('prev')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Prev Week
          </Button>
          <div className="text-center">
            <div className="font-semibold text-white">
              {format(currentWeekStart, 'MMM dd')} - {format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'MMM dd, yyyy')}
            </div>
            <div className={`text-sm ${
              isCurrentWeek() ? 'text-emerald-300' : 'text-blue-300'
            }`}>
              {isCurrentWeek() ? 'Current Weekly Cycle' : 'Historical Weekly Cycle'}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateWeek('next')}
            className="text-white hover:bg-white/10"
          >
            Next Week
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>

        {/* PHASE 3: Weekly Summary with Online/Offline Tabs */}
        {weeklySummary && (
          <Card className="bg-black/40 border-white/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Week Summary
                {!isCurrentWeek() && (
                  <Badge className="ml-2 bg-blue-500/20 text-blue-300 border-blue-500/30 text-xs">
                    Historical Data
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* PHASE 3: Mobile Tabbed View */}
              <Tabs defaultValue="online" className="w-full">
                <TabsList className="grid w-full grid-cols-2 bg-gradient-to-r from-slate-800/60 to-slate-700/40 backdrop-blur-sm rounded-xl border border-emerald-500/20 mb-4">
                  <TabsTrigger
                    value="online"
                    className="text-xs py-2 rounded-lg data-[state=active]:bg-emerald-500 data-[state=active]:text-white data-[state=active]:shadow-lg text-emerald-200 transition-all duration-300"
                  >
                    🟢 Online
                    <span className="ml-1 text-xs bg-emerald-600/30 px-1 py-0.5 rounded">Settlement</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="offline"
                    className="text-xs py-2 rounded-lg data-[state=active]:bg-gray-500 data-[state=active]:text-white data-[state=active]:shadow-lg text-gray-300 transition-all duration-300"
                  >
                    🔘 Cash
                    <span className="ml-1 text-xs bg-gray-600/30 px-1 py-0.5 rounded">Info</span>
                  </TabsTrigger>
                </TabsList>

                {/* Online Revenue Tab */}
                <TabsContent value="online" className="mt-0">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-emerald-500/20 rounded-lg p-3 text-center border border-emerald-500/30">
                      <div className="text-xl font-bold text-emerald-300">₹{weeklySummary.online_gross.toFixed(0)}</div>
                      <div className="text-xs text-emerald-200">Online Gross</div>
                      <div className="text-xs text-emerald-300 mt-1">Goes to settlement</div>
                    </div>
                    <div className="bg-green-500/20 rounded-lg p-3 text-center border border-green-500/30">
                      <div className="text-xl font-bold text-green-300">₹{weeklySummary.online_net.toFixed(0)}</div>
                      <div className="text-xs text-green-200">Net Settlement</div>
                      <div className="text-xs text-green-300 mt-1">Your payout</div>
                    </div>
                    <div className="bg-blue-500/20 rounded-lg p-3 text-center border border-blue-500/30">
                      <div className="text-xl font-bold text-blue-300">{weeklySummary.online_bookings}</div>
                      <div className="text-xs text-blue-200">Online Bookings</div>
                      <div className="text-xs text-blue-300 mt-1">Settlement-affecting</div>
                    </div>
                    <div className="bg-purple-500/20 rounded-lg p-3 text-center border border-purple-500/30">
                      <div className="text-xl font-bold text-purple-300">{getActiveDaysDisplay(currentWeekStart, weeklySummary.days_with_data)}</div>
                      <div className="text-xs text-purple-200">{getActiveDaysLabel(currentWeekStart)}</div>
                    </div>
                  </div>
                </TabsContent>

                {/* Offline Revenue Tab */}
                <TabsContent value="offline" className="mt-0">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-gray-500/20 rounded-lg p-3 text-center border border-gray-500/30">
                      <div className="text-xl font-bold text-gray-300">₹{weeklySummary.offline_gross.toFixed(0)}</div>
                      <div className="text-xs text-gray-200">Cash Gross</div>
                      <div className="text-xs text-gray-300 mt-1">Not in settlement</div>
                    </div>
                    <div className="bg-gray-600/20 rounded-lg p-3 text-center border border-gray-600/30">
                      <div className="text-xl font-bold text-gray-300">₹{weeklySummary.offline_gross.toFixed(0)}</div>
                      <div className="text-xs text-gray-200">Venue Keeps</div>
                      <div className="text-xs text-gray-300 mt-1">100% yours</div>
                    </div>
                    <div className="bg-gray-400/20 rounded-lg p-3 text-center border border-gray-400/30">
                      <div className="text-xl font-bold text-gray-300">{weeklySummary.offline_bookings}</div>
                      <div className="text-xs text-gray-200">Cash Bookings</div>
                      <div className="text-xs text-gray-300 mt-1">Informational only</div>
                    </div>
                    <div className="bg-gray-500/20 rounded-lg p-3 text-center border border-gray-500/30">
                      <div className="text-xl font-bold text-gray-300">0</div>
                      <div className="text-xs text-gray-200">Platform Fees</div>
                      <div className="text-xs text-gray-300 mt-1">No fees on cash</div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        )}

        {/* Daily Earnings Section */}
        <Card className="bg-black/40 border-white/20">
          <CardHeader className="pb-3">
            <CardTitle className="text-white flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Daily Earnings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dailyEarnings.map((day, index) => (
                <div
                  key={`${day.venue_id}-${day.cycle_date}`}
                  className={`rounded-lg p-4 border ${
                    day.is_current_day
                      ? 'bg-emerald-500/20 border-emerald-500/50'
                      : day.total_bookings > 0
                        ? 'bg-white/5 border-white/20'
                        : 'bg-gray-500/10 border-gray-500/20'
                  }`}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <div className="text-white font-semibold">
                        {day.cycle_day_name}
                      </div>
                      <div className="text-sm text-gray-300 ml-2">
                        {format(new Date(day.cycle_date), 'MMM dd')}
                      </div>
                      {day.is_current_day && (
                        <span className="ml-2 px-2 py-1 bg-emerald-500 text-white text-xs rounded-full">
                          Today
                        </span>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="text-white font-bold">
                        ₹{(day.online_gross_revenue || day.gross_revenue || 0).toFixed(0)}
                        {(day.offline_gross_revenue || 0) > 0 && (
                          <span className="text-gray-400 text-sm ml-1">
                            +₹{(day.offline_gross_revenue || 0).toFixed(0)}
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-300">
                        {day.total_bookings} booking{day.total_bookings !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>

                  {day.total_bookings > 0 ? (
                    <div className="space-y-2">
                      {/* PHASE 3: Online Revenue (Settlement-affecting) */}
                      <div className="flex items-center justify-between bg-emerald-500/10 rounded-lg p-2 border border-emerald-500/20">
                        <div className="flex items-center">
                          <span className="text-emerald-300 text-sm font-medium">🟢 Online</span>
                          <span className="ml-2 text-xs bg-emerald-600/30 px-1 py-0.5 rounded text-emerald-200">Settlement</span>
                        </div>
                        <div className="text-right">
                          <div className="text-emerald-300 font-semibold">₹{(day.online_gross_revenue || day.gross_revenue || 0).toFixed(0)}</div>
                          <div className="text-emerald-200 text-xs">Net: ₹{(day.online_net_revenue || day.net_revenue || 0).toFixed(0)}</div>
                        </div>
                      </div>

                      {/* PHASE 3: Offline Revenue (Informational) - Always show */}
                      <div className="flex items-center justify-between bg-gray-500/10 rounded-lg p-2 border border-gray-500/20">
                        <div className="flex items-center">
                          <span className="text-gray-300 text-sm font-medium">🔘 Cash</span>
                          <span className="ml-2 text-xs bg-gray-600/30 px-1 py-0.5 rounded text-gray-300">Info</span>
                        </div>
                        <div className="text-right">
                          <div className="text-gray-300 font-semibold">₹{(day.offline_gross_revenue || 0).toFixed(0)}</div>
                          <div className="text-gray-200 text-xs">Venue keeps 100%</div>
                        </div>
                      </div>

                      {/* PHASE 3: Booking breakdown */}
                      <div className="text-center text-xs text-gray-400 pt-1">
                        {(day.online_bookings || 0)} online + {(day.offline_bookings || 0)} cash bookings
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-gray-400 text-sm py-2">
                      No bookings
                    </div>
                  )}

                  <div className="text-xs text-gray-400 mt-2">
                    {day.venue_name}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-4">
          <Link
            to="/admin/settlements-mobile"
            className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-500/30 rounded-lg p-4 flex flex-col items-center text-center hover:bg-blue-500/30 transition-colors"
          >
            <Banknote className="w-8 h-8 text-blue-400 mb-2" />
            <div className="font-semibold text-white">Settlements</div>
            <div className="text-sm text-blue-200">View weekly settlements</div>
          </Link>

          <Link
            to="/admin/analytics-mobile"
            className="bg-gradient-to-r from-purple-500/20 to-purple-600/20 border border-purple-500/30 rounded-lg p-4 flex flex-col items-center text-center hover:bg-purple-500/30 transition-colors"
          >
            <Eye className="w-8 h-8 text-purple-400 mb-2" />
            <div className="font-semibold text-white">Analytics</div>
            <div className="text-sm text-purple-200">Detailed insights</div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default EarningsDashboard_Mobile;

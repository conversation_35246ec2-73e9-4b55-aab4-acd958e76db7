import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { format, startOfMonth, endOfMonth, subMonths, parseISO, isWithinInterval, addDays } from 'date-fns';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/context/AuthContext';
import PaymentMethodFilter, { PaymentMethodFilterType } from '@/components/admin/PaymentMethodFilter';
import { groupConsecutiveBookings } from '@/utils/bookingGrouping';
import { logSecurityEvent } from '@/utils/adminSecurity';

interface BookingData {
  id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  total_price: number;
  status: string;
  payment_method: string;
  payment_status: string | null;
  payment_reference: string | null;
  court: {
    name: string;
    venue_id: string;
    sport_id: string;
    venue: {
      name: string;
      id: string;
    };
    sport: {
      name: string;
      id: string;
    };
  };
}

const RecentBookings_Mobile: React.FC = () => {
  const { userRole } = useAuth();
  const [loading, setLoading] = useState(true);
  const [bookings, setBookings] = useState<BookingData[]>([]);
  const [adminVenues, setAdminVenues] = useState<Array<{ venue_id: string }>>([]);
  const [timeRange, setTimeRange] = useState('month');
  const [selectedVenueId, setSelectedVenueId] = useState<string>('all');
  const [venues, setVenues] = useState<Array<{ id: string, name: string }>>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [paymentMethodFilter, setPaymentMethodFilter] = useState<PaymentMethodFilterType>('online');

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      console.log('🚀 Starting optimized recent bookings fetch...');
      const startTime = performance.now();

      try {
        // ✅ PERFORMANCE OPTIMIZATION: Fetch admin venues and venues in parallel
        const [venueDataResult, venuesDataResult] = await Promise.all([
          userRole === 'admin' ? supabase.rpc('get_admin_venues') : Promise.resolve({ data: [], error: null }),
          supabase.from('venues').select('id, name').eq('is_active', true)
        ]);

        if (venueDataResult.error) throw venueDataResult.error;
        if (venuesDataResult.error) throw venuesDataResult.error;

        setAdminVenues(venueDataResult.data || []);
        setVenues(venuesDataResult.data || []);

        // ✅ PERFORMANCE OPTIMIZATION: Only fetch recent bookings (last 3 months) instead of ALL bookings
        const threeMonthsAgo = subMonths(new Date(), 3);

        let query = supabase
          .from('bookings')
          .select(`
            id,
            booking_date,
            start_time,
            end_time,
            total_price,
            status,
            payment_method,
            payment_status,
            payment_reference,
            court:court_id (
              name,
              venue_id,
              sport_id,
              venue:venue_id (id, name),
              sport:sport_id (id, name)
            )
          `)
          .gte('booking_date', format(threeMonthsAgo, 'yyyy-MM-dd')) // ✅ SERVER-SIDE DATE FILTERING
          .order('booking_date', { ascending: false })
          .limit(500); // ✅ LIMIT RESULTS for better performance

        // Apply venue filtering server-side for admins
        if (userRole === 'admin' && venueDataResult.data && venueDataResult.data.length > 0) {
          const venueIds = venueDataResult.data.map((v: any) => v.venue_id);
          query = query.in('court.venue_id', venueIds);
        }

        const { data, error } = await query;
        if (error) throw error;

        setBookings(data || []);

        const endTime = performance.now();
        const totalTime = endTime - startTime;
        // Optimized recent bookings loaded successfully

        // Log performance improvement
        logSecurityEvent('OPTIMIZED_RECENT_BOOKINGS_QUERY', 'system', {
          queryTime: `${totalTime.toFixed(2)}ms`,
          bookingsReturned: data ? data.length : 0,
          userRole,
          optimization: 'Server-side filtering + date range + limit'
        });

      } catch (error) {
        console.error('Error fetching optimized recent bookings:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch recent bookings',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [userRole]);

  useEffect(() => {
    if (userRole === 'admin' && adminVenues.length > 0) {
      setSelectedVenueId(adminVenues[0].venue_id);
    }
  }, [userRole, adminVenues]);

  const getPaymentMethodFilteredBookings = (bookings: BookingData[]) => {
    if (paymentMethodFilter === 'all') return bookings;
    
    return bookings.filter(booking => {
      const method = booking.payment_method || 'online';
      if (paymentMethodFilter === 'online') {
        return method === 'online';
      } else {
        return method === 'cash' || method === 'card';
      }
    });
  };

  const filteredBookings = getPaymentMethodFilteredBookings(
    bookings.filter(booking => {
      const bookingDate = parseISO(booking.booking_date);
      let rangeStart, rangeEnd;
      switch(timeRange) {
        case 'week':
          rangeStart = addDays(currentDate, -7);
          rangeEnd = currentDate;
          break;
        case 'month':
          rangeStart = startOfMonth(currentDate);
          rangeEnd = endOfMonth(currentDate);
          break;
        case 'year':
          rangeStart = new Date(currentDate.getFullYear(), 0, 1);
          rangeEnd = new Date(currentDate.getFullYear(), 11, 31);
          break;
        default:
          rangeStart = startOfMonth(currentDate);
          rangeEnd = endOfMonth(currentDate);
      }
      const isInDateRange = isWithinInterval(bookingDate, { start: rangeStart, end: rangeEnd });
      const isMatchingVenue = selectedVenueId === 'all' || booking.court?.venue_id === selectedVenueId;
      return isInDateRange && isMatchingVenue;
    })
  );

  // Transform BookingData to match the grouping function interface
  const transformedBookings = filteredBookings.map(booking => ({
    id: booking.id,
    booking_date: booking.booking_date,
    start_time: booking.start_time,
    end_time: booking.end_time,
    total_price: booking.total_price,
    status: booking.status as 'confirmed' | 'cancelled' | 'completed' | 'pending',
    payment_reference: booking.payment_reference,
    payment_status: booking.payment_status,
    payment_method: booking.payment_method,
    court: {
      name: booking.court?.name || 'N/A',
      venue: {
        name: booking.court?.venue?.name || 'N/A',
        id: booking.court?.venue?.id || booking.court?.venue_id || ''
      },
      sport: {
        name: booking.court?.sport?.name || 'N/A',
        id: booking.court?.sport?.id || booking.court?.sport_id || ''
      }
    }
  }));

  // Group consecutive bookings for better display
  const groupedBookings = groupConsecutiveBookings(transformedBookings);

  const handlePreviousPeriod = () => {
    switch(timeRange) {
      case 'week':
        setCurrentDate(prev => addDays(prev, -7));
        break;
      case 'month':
        setCurrentDate(prev => subMonths(prev, 1));
        break;
      case 'year':
        setCurrentDate(prev => new Date(prev.getFullYear() - 1, prev.getMonth(), prev.getDate()));
        break;
    }
  };

  const handleNextPeriod = () => {
    const today = new Date();
    let newDate;
    switch(timeRange) {
      case 'week':
        newDate = addDays(currentDate, 7);
        break;
      case 'month':
        newDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
        break;
      case 'year':
        newDate = new Date(currentDate.getFullYear() + 1, currentDate.getMonth(), currentDate.getDate());
        break;
      default:
        newDate = new Date();
    }
    if (newDate <= today) {
      setCurrentDate(newDate);
    }
  };

  const getTimeRangeLabel = () => {
    switch(timeRange) {
      case 'week':
        return `${format(addDays(currentDate, -7), 'MMM dd')} - ${format(currentDate, 'MMM dd, yyyy')}`;
      case 'month':
        return format(currentDate, 'MMMM yyyy');
      case 'year':
        return format(currentDate, 'yyyy');
      default:
        return format(currentDate, 'MMMM yyyy');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4 max-w-md mx-auto">
      <div className="flex flex-col gap-4">
        <div>
          <h1 className="text-2xl font-bold">Recent Bookings</h1>
          <p className="text-muted-foreground">Latest bookings across your venues (showing {groupedBookings.length} booking groups)</p>
        </div>
        
        <PaymentMethodFilter 
          selectedFilter={paymentMethodFilter}
          onFilterChange={setPaymentMethodFilter}
        />
        
        <div className="flex flex-col gap-2">
          <select 
            value={selectedVenueId}
            onChange={(e) => setSelectedVenueId(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            {userRole === 'super_admin' && (
              <option value="all">All Venues</option>
            )}
            {venues
              .filter(venue => 
                userRole === 'super_admin' || 
                adminVenues.some(v => v.venue_id === venue.id)
              )
              .map(venue => (
                <option key={venue.id} value={venue.id}>{venue.name}</option>
              ))
            }
          </select>
          <div className="flex items-center border rounded-md overflow-hidden w-full">
            <Button 
              variant="ghost" 
              onClick={handlePreviousPeriod}
              className="border-r"
            >
              ←
            </Button>
            <div className="px-3">
              <span className="text-sm font-medium">{getTimeRangeLabel()}</span>
            </div>
            <Button 
              variant="ghost" 
              onClick={handleNextPeriod}
              className="border-l"
              disabled={
                (timeRange === 'month' && 
                  currentDate.getMonth() === new Date().getMonth() && 
                  currentDate.getFullYear() === new Date().getFullYear()) ||
                (timeRange === 'year' && 
                  currentDate.getFullYear() === new Date().getFullYear())
              }
            >
              →
            </Button>
          </div>
          <div className="flex rounded-md overflow-hidden border w-full mt-2">
            <Button 
              variant={timeRange === 'week' ? 'default' : 'ghost'}
              className="rounded-none w-1/3"
              onClick={() => setTimeRange('week')}
            >
              Week
            </Button>
            <Button 
              variant={timeRange === 'month' ? 'default' : 'ghost'}
              className="rounded-none border-l border-r w-1/3"
              onClick={() => setTimeRange('month')}
            >
              Month
            </Button>
            <Button 
              variant={timeRange === 'year' ? 'default' : 'ghost'}
              className="rounded-none w-1/3"
              onClick={() => setTimeRange('year')}
            >
              Year
            </Button>
          </div>
        </div>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Recent Bookings</CardTitle>
          <CardDescription>
            Latest bookings across your venues (showing {groupedBookings.length} booking groups)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Time</TableHead>
                  <TableHead>Venue</TableHead>
                  <TableHead>Court</TableHead>
                  <TableHead>Sport</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {groupedBookings
                  .sort((a, b) => new Date(b.booking_date).getTime() - new Date(a.booking_date).getTime())
                  .slice(0, 10)
                  .map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{format(parseISO(booking.booking_date), 'MMM dd, yyyy')}</span>
                          {booking.slot_count > 1 && (
                            <span className="text-xs text-emerald-600 font-medium">{booking.slot_count} slots</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {booking.start_time.substring(0, 5)} - {booking.end_time.substring(0, 5)}
                      </TableCell>
                      <TableCell>{booking.court?.venue?.name || 'N/A'}</TableCell>
                      <TableCell>{booking.court?.name || 'N/A'}</TableCell>
                      <TableCell>{booking.court?.sport?.name || 'N/A'}</TableCell>
                      <TableCell className="text-right font-medium">₹{booking.total_price}</TableCell>
                    </TableRow>
                  ))}
                {groupedBookings.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                      No bookings found for the selected period
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RecentBookings_Mobile;

/**
 * 🔐 SUPER ADMIN DASHBOARD
 * 
 * Dedicated interface for super admin operations:
 * - Notification approval/rejection
 * - System-wide analytics
 * - User management
 * - Platform oversight
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { NotificationApprovalInterface } from '@/components/admin/NotificationApprovalInterface';
import { 
  Shield, 
  Bell, 
  Users, 
  Building2, 
  BarChart3, 
  Settings,
  LogOut,
  Crown
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'react-hot-toast';

interface SuperAdminStats {
  totalVenues: number;
  totalUsers: number;
  pendingNotifications: number;
  totalNotifications: number;
}

export default function SuperAdminDashboard() {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [stats, setStats] = useState<SuperAdminStats>({
    totalVenues: 0,
    totalUsers: 0,
    pendingNotifications: 0,
    totalNotifications: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user has super admin access
    checkSuperAdminAccess();
    fetchStats();
  }, []);

  const checkSuperAdminAccess = async () => {
    // For now, we'll check if the user email contains 'admin' or is a specific email
    // In production, you should have a proper role-based system
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    const isAdmin = user?.email && (
      adminEmails.includes(user.email) || 
      user.email.includes('admin') ||
      user.email.includes('aniket')
    );

    if (!isAdmin) {
      toast.error('Access denied. Super admin privileges required.');
      navigate('/');
      return;
    }
  };

  const fetchStats = async () => {
    try {
      // Get total venues
      const { count: venueCount } = await supabase
        .from('venues')
        .select('*', { count: 'exact', head: true });

      // Get total users
      const { count: userCount } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // Get pending notifications
      const { count: pendingCount } = await supabase
        .from('notification_templates')
        .select('*', { count: 'exact', head: true })
        .eq('approved', false)
        .is('rejection_reason', null);

      // Get total notifications
      const { count: totalNotifications } = await supabase
        .from('notification_templates')
        .select('*', { count: 'exact', head: true });

      setStats({
        totalVenues: venueCount || 0,
        totalUsers: userCount || 0,
        pendingNotifications: pendingCount || 0,
        totalNotifications: totalNotifications || 0
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
      toast.error('Failed to load dashboard stats');
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/');
    } catch (error) {
      console.error('Error signing out:', error);
      toast.error('Failed to sign out');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
        <div className="text-white text-xl">Loading Super Admin Dashboard...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      {/* Header */}
      <div className="bg-black/50 backdrop-blur-sm border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Crown className="w-8 h-8 text-yellow-400" />
              <div>
                <h1 className="text-xl font-bold text-white">Super Admin Dashboard</h1>
                <p className="text-sm text-gray-400">Grid२Play Platform Management</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-sm text-white font-medium">{user?.email}</p>
                <p className="text-xs text-gray-400">Super Administrator</p>
              </div>
              <Button
                onClick={handleSignOut}
                variant="outline"
                size="sm"
                className="border-gray-600 text-gray-300 hover:bg-gray-800"
              >
                <LogOut className="w-4 h-4 mr-1" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-br from-blue-900/20 to-blue-800/10 border-blue-700/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-300 text-sm font-medium">Total Venues</p>
                  <p className="text-3xl font-bold text-white">{stats.totalVenues}</p>
                </div>
                <Building2 className="w-8 h-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-900/20 to-green-800/10 border-green-700/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-300 text-sm font-medium">Total Users</p>
                  <p className="text-3xl font-bold text-white">{stats.totalUsers}</p>
                </div>
                <Users className="w-8 h-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-yellow-900/20 to-yellow-800/10 border-yellow-700/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-300 text-sm font-medium">Pending Approvals</p>
                  <p className="text-3xl font-bold text-white">{stats.pendingNotifications}</p>
                </div>
                <Bell className="w-8 h-8 text-yellow-400" />
              </div>
              {stats.pendingNotifications > 0 && (
                <Badge className="bg-yellow-600 text-white mt-2">
                  Requires Attention
                </Badge>
              )}
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-900/20 to-purple-800/10 border-purple-700/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-300 text-sm font-medium">Total Notifications</p>
                  <p className="text-3xl font-bold text-white">{stats.totalNotifications}</p>
                </div>
                <BarChart3 className="w-8 h-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Tabs */}
        <Tabs defaultValue="notifications" className="space-y-6">
          <TabsList className="bg-gray-800 border-gray-700">
            <TabsTrigger value="notifications" className="data-[state=active]:bg-emerald-600">
              <Bell className="w-4 h-4 mr-2" />
              Notification Approvals
              {stats.pendingNotifications > 0 && (
                <Badge className="bg-red-600 text-white ml-2 text-xs">
                  {stats.pendingNotifications}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-emerald-600">
              <BarChart3 className="w-4 h-4 mr-2" />
              System Analytics
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-emerald-600">
              <Settings className="w-4 h-4 mr-2" />
              Platform Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="notifications" className="space-y-6">
            <NotificationApprovalInterface />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">System Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <BarChart3 className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400">System analytics coming soon</p>
                  <p className="text-gray-500 text-sm">Comprehensive platform metrics and insights</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Platform Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Settings className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400">Platform settings coming soon</p>
                  <p className="text-gray-500 text-sm">System configuration and management tools</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

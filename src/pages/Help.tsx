
import React, { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { HelpCircle, MessageSquare, Mail, Phone, ArrowLeft, MapPin, BookOpen, CreditCard, Calendar, Users, Search, FileText, Headphones, Clock } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';

const Help: React.FC = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-navy-dark text-white">
      <Header />

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <Link
            to="/"
            className="inline-flex items-center text-emerald-400 hover:text-emerald-300 transition-colors mb-8"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>

          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4 flex items-center justify-center gap-3">
              <HelpCircle className="w-10 h-10 text-emerald-400" />
              Help Center
            </h1>
            <p className="text-gray-300 text-lg max-w-3xl mx-auto">
              Welcome to Grid२Play Help Center. Find answers to common questions, get support for bookings,
              payments, and account issues. Our team is here to help you make the most of your sports experience.
            </p>
            <div className="mt-4 text-sm text-gray-400">
              <p><strong>Business:</strong> DROP SHOTS SPORTS ACADEMY</p>
              <p><strong>Support Hours:</strong> Monday - Sunday, 9:00 AM - 9:00 PM IST</p>
            </div>
          </div>

          {/* Quick Navigation */}
          <div className="bg-navy-light rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Search className="w-5 h-5 mr-2 text-emerald-400" />
              Quick Help Topics
            </h3>
            <div className="grid md:grid-cols-2 gap-2 text-sm">
              <a href="#booking-help" className="text-emerald-400 hover:text-emerald-300">• Booking & Reservations</a>
              <a href="#payment-help" className="text-emerald-400 hover:text-emerald-300">• Payments & Refunds</a>
              <a href="#account-help" className="text-emerald-400 hover:text-emerald-300">• Account Management</a>
              <a href="#venue-help" className="text-emerald-400 hover:text-emerald-300">• Venue Information</a>
              <a href="#technical-help" className="text-emerald-400 hover:text-emerald-300">• Technical Support</a>
              <a href="#contact-support" className="text-emerald-400 hover:text-emerald-300">• Contact Support</a>
            </div>
          </div>
          {/* Help Categories */}
          <div className="space-y-8">
            {/* Section 1: Booking Help */}
            <section id="booking-help" className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Calendar className="w-6 h-6 text-emerald-400 mr-3" />
                Booking & Reservations
              </h2>
              <div className="space-y-4 text-gray-300">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-navy rounded-lg p-4">
                    <h3 className="font-semibold text-white mb-2">How to Book</h3>
                    <ul className="text-sm space-y-1">
                      <li>• Browse venues by location or sport</li>
                      <li>• Select your preferred time slot</li>
                      <li>• Complete payment via Razorpay</li>
                      <li>• Receive confirmation via SMS/WhatsApp</li>
                    </ul>
                  </div>
                  <div className="bg-navy rounded-lg p-4">
                    <h3 className="font-semibold text-white mb-2">Booking Rules</h3>
                    <ul className="text-sm space-y-1">
                      <li>• Book up to 14 days in advance</li>
                      <li>• Minimum 1-hour booking slots</li>
                      <li>• Consecutive slots only</li>
                      <li>• Payment required to confirm</li>
                    </ul>
                  </div>
                </div>
                <div className="bg-emerald-900/20 border border-emerald-600/30 rounded-lg p-4">
                  <h4 className="font-semibold text-emerald-300 mb-2">Need Booking Help?</h4>
                  <p className="text-sm">
                    Having trouble with your booking? Contact our support team at
                    <a href="tel:+************" className="text-emerald-400 underline ml-1">+91 92118 48599</a>
                    for immediate assistance.
                  </p>
                </div>
              </div>
            </section>

            {/* Section 2: Payment Help */}
            <section id="payment-help" className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <CreditCard className="w-6 h-6 text-blue-400 mr-3" />
                Payments & Refunds
              </h2>
              <div className="space-y-4 text-gray-300">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Payment Methods</h3>
                  <p className="mb-2">We accept all major payment methods through Razorpay:</p>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>UPI (Google Pay, PhonePe, Paytm, etc.)</li>
                    <li>Credit & Debit Cards (Visa, Mastercard, RuPay)</li>
                    <li>Net Banking (All major banks)</li>
                    <li>Digital Wallets (Paytm, Amazon Pay, etc.)</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Refund Policy</h3>
                  <div className="bg-navy rounded-lg p-4">
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <h4 className="font-semibold text-white mb-2">Cancellation Timeline</h4>
                        <ul className="space-y-1">
                          <li>• 24+ hours: 100% refund</li>
                          <li>• 12-24 hours: 75% refund</li>
                          <li>• 6-12 hours: 50% refund</li>
                          <li>• Less than 6 hours: No refund</li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold text-white mb-2">Refund Processing</h4>
                        <ul className="space-y-1">
                          <li>• Processed within 5-7 business days</li>
                          <li>• Refunded to original payment method</li>
                          <li>• Weather cancellations: Full refund</li>
                          <li>• Email notification sent</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Section 3: Account Help */}
            <section id="account-help" className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Users className="w-6 h-6 text-purple-400 mr-3" />
                Account Management
              </h2>
              <div className="space-y-4 text-gray-300">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-navy rounded-lg p-4">
                    <h3 className="font-semibold text-white mb-2">Account Setup</h3>
                    <ul className="text-sm space-y-1">
                      <li>• Phone-first registration</li>
                      <li>• SMS OTP verification</li>
                      <li>• Optional email verification</li>
                      <li>• Profile completion</li>
                    </ul>
                  </div>
                  <div className="bg-navy rounded-lg p-4">
                    <h3 className="font-semibold text-white mb-2">Profile Management</h3>
                    <ul className="text-sm space-y-1">
                      <li>• Update personal information</li>
                      <li>• Manage sports preferences</li>
                      <li>• View booking history</li>
                      <li>• Download booking receipts</li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>

            {/* Section 4: Contact Support */}
            <section id="contact-support" className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Headphones className="w-6 h-6 text-orange-400 mr-3" />
                Contact Support
              </h2>
              <div className="space-y-4 text-gray-300">
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="bg-navy rounded-lg p-4 text-center">
                    <Phone className="w-8 h-8 text-emerald-400 mx-auto mb-3" />
                    <h3 className="font-semibold text-white mb-2">Phone Support</h3>
                    <p className="text-sm mb-2">Immediate assistance</p>
                    <a href="tel:+************" className="text-emerald-400 hover:text-emerald-300 font-medium">
                      +91 92118 48599
                    </a>
                  </div>
                  <div className="bg-navy rounded-lg p-4 text-center">
                    <Mail className="w-8 h-8 text-blue-400 mx-auto mb-3" />
                    <h3 className="font-semibold text-white mb-2">Email Support</h3>
                    <p className="text-sm mb-2">Detailed inquiries</p>
                    <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 font-medium">
                      <EMAIL>
                    </a>
                  </div>
                  <div className="bg-navy rounded-lg p-4 text-center">
                    <MessageSquare className="w-8 h-8 text-purple-400 mx-auto mb-3" />
                    <h3 className="font-semibold text-white mb-2">Live Chat</h3>
                    <p className="text-sm mb-2">Real-time support</p>
                    <button className="text-purple-400 hover:text-purple-300 font-medium">
                      Start Chat
                    </button>
                  </div>
                </div>

                <div className="bg-emerald-900/20 border border-emerald-600/30 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 text-emerald-400 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-emerald-300 mb-1">Support Hours</h4>
                      <p className="text-sm">Monday - Sunday: 9:00 AM - 9:00 PM IST</p>
                      <p className="text-xs text-gray-400 mt-1">Average response time: Under 2 hours</p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Office Address */}
            <section className="bg-navy-light rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <MapPin className="w-6 h-6 text-red-400 mr-3" />
                Visit Our Office
              </h2>
              <div className="bg-navy rounded-lg p-4">
                <div className="text-gray-300 leading-relaxed">
                  <p className="font-semibold text-white mb-2">DROP SHOTS SPORTS ACADEMY</p>
                  <p>Shop no 54 WZ-10 First Floor</p>
                  <p>Jwala Heri, Pachim Vihar</p>
                  <p>West Delhi, 110063</p>
                  <p className="mt-2 text-emerald-400 font-medium">India</p>
                  <div className="mt-4 flex flex-col sm:flex-row gap-2">
                    <a href="tel:+************" className="text-emerald-400 hover:text-emerald-300 text-sm">
                      📞 +91 92118 48599
                    </a>
                    <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 text-sm">
                      ✉️ <EMAIL>
                    </a>
                  </div>
                </div>
              </div>
            </section>

            {/* Additional Resources */}
            <section className="bg-emerald-900/10 border border-emerald-600/30 rounded-lg p-6 text-center">
              <h3 className="text-xl font-semibold mb-3 flex items-center justify-center gap-2">
                <BookOpen className="w-5 h-5 text-emerald-400" />
                Additional Resources
              </h3>
              <p className="text-gray-300 mb-4">
                Explore more resources to get the most out of Grid२Play
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link
                  to="/faq"
                  className="inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  View FAQ
                </Link>
                <Link
                  to="/contact"
                  className="inline-flex items-center px-6 py-3 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Contact Form
                </Link>
                <Link
                  to="/privacy"
                  className="inline-flex items-center px-6 py-3 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Privacy Policy
                </Link>
              </div>
            </section>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Help;

import React, { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ShieldCheck, Mail, ArrowLeft, Eye, Lock, Users, Globe, Phone, Calendar, CreditCard, MapPin, AlertTriangle, FileText } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';

const Privacy: React.FC = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-navy-dark text-white">
      <Header />

    <div className="container mx-auto px-4 py-16">
      <div className="max-w-4xl mx-auto">
        {/* Back Button */}
        <Link
          to="/"
          className="inline-flex items-center text-emerald-400 hover:text-emerald-300 transition-colors mb-8"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Home
        </Link>

        {/* <PERSON><PERSON> */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4 flex items-center justify-center gap-3">
            <ShieldCheck className="w-10 h-10 text-emerald-400" />
            Privacy Policy
          </h1>
          <p className="text-gray-300 text-lg max-w-3xl mx-auto">
            At Grid२Play, we are committed to protecting your privacy and ensuring the security of your personal information.
            This comprehensive policy explains how we collect, use, and safeguard your data.
          </p>
          <div className="mt-4 text-sm text-gray-400">
            <p><strong>Effective Date:</strong> {new Date().toLocaleDateString('en-IN')}</p>
            <p><strong>Last Updated:</strong> {new Date().toLocaleDateString('en-IN')}</p>
            <p><strong>Version:</strong> 1.0</p>
          </div>
        </div>

        {/* Quick Navigation */}
        <div className="bg-navy-light rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <FileText className="w-5 h-5 mr-2 text-emerald-400" />
            Quick Navigation
          </h3>
          <div className="grid md:grid-cols-2 gap-2 text-sm">
            <a href="#company-info" className="text-emerald-400 hover:text-emerald-300">1. Company Information</a>
            <a href="#data-collection" className="text-emerald-400 hover:text-emerald-300">2. Data We Collect</a>
            <a href="#data-usage" className="text-emerald-400 hover:text-emerald-300">3. How We Use Data</a>
            <a href="#data-sharing" className="text-emerald-400 hover:text-emerald-300">4. Data Sharing</a>
            <a href="#data-security" className="text-emerald-400 hover:text-emerald-300">5. Data Security</a>
            <a href="#user-rights" className="text-emerald-400 hover:text-emerald-300">6. Your Rights</a>
            <a href="#cookies" className="text-emerald-400 hover:text-emerald-300">7. Cookies & Tracking</a>
            <a href="#third-parties" className="text-emerald-400 hover:text-emerald-300">8. Third-Party Services</a>
            <a href="#data-retention" className="text-emerald-400 hover:text-emerald-300">9. Data Retention</a>
            <a href="#contact" className="text-emerald-400 hover:text-emerald-300">10. Contact Information</a>
          </div>
        </div>
        {/* Privacy Policy Content */}
        <div className="space-y-8">
          {/* Section 1: Company Information */}
          <section id="company-info" className="bg-navy-light rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 flex items-center">
              <Users className="w-6 h-6 text-emerald-400 mr-3" />
              1. Company Information
            </h2>
            <div className="space-y-4 text-gray-300">
              <div className="bg-navy rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-3">About Grid२Play</h3>
                <div className="space-y-2">
                  <p><strong>Business Name:</strong> DROP SHOTS SPORTS ACADEMY</p>
                  <p><strong>Brand Name:</strong> Grid२Play</p>
                  <p><strong>Service:</strong> Sports venue booking and management platform</p>
                  <div className="flex items-center gap-2 mt-3">
                    <MapPin className="w-4 h-4 text-emerald-400" />
                    <span>Shop no 54 WZ-10 First Floor, Jwala Heri, Pachim Vihar, West Delhi, 110063, India</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-emerald-400" />
                    <span>+91 92118 48599</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-emerald-400" />
                    <span><EMAIL></span>
                  </div>
                </div>
              </div>
              <p>
                Grid२Play operates as India's premier sports booking platform, connecting sports enthusiasts with
                quality venues across the country. We are committed to maintaining the highest standards of data
                protection and user privacy.
              </p>
            </div>
          </section>

          {/* Section 2: Data Collection */}
          <section id="data-collection" className="bg-navy-light rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 flex items-center">
              <Eye className="w-6 h-6 text-blue-400 mr-3" />
              2. Information We Collect
            </h2>
            <div className="space-y-4 text-gray-300">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Personal Information</h3>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li><strong>Account Data:</strong> Full name, email address, phone number</li>
                  <li><strong>Profile Information:</strong> Sports preferences, skill level, location</li>
                  <li><strong>Verification Data:</strong> Age verification, identity confirmation</li>
                  <li><strong>Communication:</strong> Support messages, feedback, reviews</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Booking & Transaction Data</h3>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li><strong>Booking Details:</strong> Venue selections, time slots, sports preferences</li>
                  <li><strong>Payment Information:</strong> Transaction IDs, payment methods (via Razorpay)</li>
                  <li><strong>Guest Information:</strong> Names and contact details for group bookings</li>
                  <li><strong>Booking History:</strong> Past and upcoming reservations, cancellations</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Technical & Usage Data</h3>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li><strong>Device Information:</strong> IP address, browser type, operating system</li>
                  <li><strong>Usage Analytics:</strong> Pages visited, features used, session duration</li>
                  <li><strong>Location Data:</strong> GPS coordinates (with permission) for venue recommendations</li>
                  <li><strong>Performance Data:</strong> App crashes, loading times, error logs</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Section 3: Data Usage */}
          <section id="data-usage" className="bg-navy-light rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 flex items-center">
              <Globe className="w-6 h-6 text-green-400 mr-3" />
              3. How We Use Your Information
            </h2>
            <div className="space-y-4 text-gray-300">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Core Services</h3>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Process and manage your sports venue bookings</li>
                  <li>Facilitate secure payments through Razorpay integration</li>
                  <li>Send booking confirmations, reminders, and updates via SMS/WhatsApp</li>
                  <li>Provide customer support and resolve booking issues</li>
                  <li>Maintain your account and profile information</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Service Improvement</h3>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Analyze usage patterns to improve platform performance</li>
                  <li>Personalize venue recommendations based on your preferences</li>
                  <li>Develop new features and enhance user experience</li>
                  <li>Conduct research and analytics for business insights</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Communication & Marketing</h3>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Send promotional offers and venue updates (with consent)</li>
                  <li>Share sports community news and platform announcements</li>
                  <li>Conduct surveys and collect feedback for service improvement</li>
                  <li>Provide technical support and troubleshooting assistance</li>
                </ul>
              </div>
            </div>
          </section>
          {/* Section 4: Data Sharing */}
          <section id="data-sharing" className="bg-navy-light rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 flex items-center">
              <Users className="w-6 h-6 text-purple-400 mr-3" />
              4. Data Sharing & Third Parties
            </h2>
            <div className="space-y-4 text-gray-300">
              <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-yellow-300 mb-1">Important Notice</h4>
                    <p className="text-sm">We never sell your personal information to third parties. Data sharing only occurs for essential service delivery.</p>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Essential Service Partners</h3>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li><strong>Razorpay:</strong> Secure payment processing (transaction data only)</li>
                  <li><strong>MSG91:</strong> SMS/WhatsApp notifications (phone numbers for delivery)</li>
                  <li><strong>Venue Partners:</strong> Booking details for reservation confirmation</li>
                  <li><strong>Supabase:</strong> Secure cloud database hosting (encrypted data)</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Legal Requirements</h3>
                <p>We may disclose information when required by Indian law, court orders, or to protect our legal rights and user safety.</p>
              </div>
            </div>
          </section>

          {/* Section 5: Data Security */}
          <section id="data-security" className="bg-navy-light rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 flex items-center">
              <Lock className="w-6 h-6 text-red-400 mr-3" />
              5. Data Security & Protection
            </h2>
            <div className="space-y-4 text-gray-300">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Security Measures</h3>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li><strong>Encryption:</strong> All data transmitted using SSL/TLS encryption</li>
                  <li><strong>Access Controls:</strong> Role-based access with multi-factor authentication</li>
                  <li><strong>Regular Audits:</strong> Security assessments and vulnerability testing</li>
                  <li><strong>Data Backup:</strong> Secure, encrypted backups with disaster recovery</li>
                  <li><strong>Monitoring:</strong> 24/7 security monitoring and incident response</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Payment Security</h3>
                <p>All payment processing is handled by Razorpay, a PCI DSS compliant payment gateway. Grid२Play never stores your complete payment card details.</p>
              </div>
            </div>
          </section>

          {/* Section 6: User Rights */}
          <section id="user-rights" className="bg-navy-light rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 flex items-center">
              <ShieldCheck className="w-6 h-6 text-emerald-400 mr-3" />
              6. Your Privacy Rights
            </h2>
            <div className="space-y-4 text-gray-300">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-navy rounded-lg p-4">
                  <h4 className="font-semibold text-white mb-2">Access & Portability</h4>
                  <ul className="text-sm space-y-1">
                    <li>• View all your personal data</li>
                    <li>• Download your booking history</li>
                    <li>• Export account information</li>
                  </ul>
                </div>
                <div className="bg-navy rounded-lg p-4">
                  <h4 className="font-semibold text-white mb-2">Control & Correction</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Update profile information</li>
                    <li>• Correct inaccurate data</li>
                    <li>• Manage communication preferences</li>
                  </ul>
                </div>
                <div className="bg-navy rounded-lg p-4">
                  <h4 className="font-semibold text-white mb-2">Deletion & Withdrawal</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Request account deletion</li>
                    <li>• Withdraw consent for marketing</li>
                    <li>• Remove specific data types</li>
                  </ul>
                </div>
                <div className="bg-navy rounded-lg p-4">
                  <h4 className="font-semibold text-white mb-2">Objection & Restriction</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Object to data processing</li>
                    <li>• Restrict certain uses</li>
                    <li>• Opt-out of automated decisions</li>
                  </ul>
                </div>
              </div>
              <div className="bg-emerald-900/20 border border-emerald-600/30 rounded-lg p-4">
                <h4 className="font-semibold text-emerald-300 mb-2">How to Exercise Your Rights</h4>
                <p className="text-sm">Contact us at <a href="mailto:<EMAIL>" className="text-emerald-400 underline"><EMAIL></a> or call <a href="tel:+************" className="text-emerald-400 underline">+91 92118 48599</a>. We'll respond within 30 days.</p>
              </div>
            </div>
          </section>

          {/* Contact Section */}
          <section id="contact" className="bg-emerald-900/10 border border-emerald-600/30 rounded-lg p-6 text-center">
            <h3 className="text-xl font-semibold mb-3 flex items-center justify-center gap-2">
              <Mail className="w-5 h-5 text-emerald-400" />
              Questions About This Privacy Policy?
            </h3>
            <p className="text-gray-300 mb-4">
              Our privacy team is here to help. Contact us for any questions about how we handle your data.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
              >
                <Mail className="w-4 h-4 mr-2" />
                Email Support
              </a>
              <a
                href="tel:+************"
                className="inline-flex items-center px-6 py-3 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors"
              >
                <Phone className="w-4 h-4 mr-2" />
                Call Us
              </a>
              <Link
                to="/terms"
                className="inline-flex items-center px-6 py-3 bg-navy border border-emerald-600/30 text-white rounded-lg hover:bg-navy-light transition-colors"
              >
                <FileText className="w-4 h-4 mr-2" />
                Terms of Service
              </Link>
            </div>
            <div className="mt-6 text-sm text-gray-400">
              <p><strong>DROP SHOTS SPORTS ACADEMY</strong></p>
              <p>Shop no 54 WZ-10 First Floor, Jwala Heri, Pachim Vihar, West Delhi, 110063</p>
              <p>Phone: +91 92118 48599 | Email: <EMAIL></p>
            </div>
          </section>
        </div>
      </div>
    </div>

    <Footer />
  </div>
  );
};

export default Privacy; 

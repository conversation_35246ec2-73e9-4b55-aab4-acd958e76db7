import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';

// Security constants
const MAX_COUPON_ATTEMPTS = 3;
const COUPON_ATTEMPT_WINDOW = 60000; // 1 minute
const MAX_PAYMENT_ATTEMPTS = 1;
const PAYMENT_ATTEMPT_WINDOW = 30000; // 30 seconds
const SLOT_CHECK_DEBOUNCE = 500; // 500ms

// Rate limiting storage
const rateLimitStore = new Map<string, { count: number; lastAttempt: number }>();

// Input sanitization utility
export const sanitizeBookingInput = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/['"]/g, '') // Remove quotes to prevent injection
    .substring(0, 255); // Limit length
};

// Phone number sanitization
export const sanitizePhoneNumber = (phone: string): string => {
  if (!phone) return '';
  
  // Remove all non-digit characters except +
  const cleaned = phone.replace(/[^\d+]/g, '');
  
  // Ensure it starts with + and has valid length
  if (cleaned.startsWith('+') && cleaned.length >= 10 && cleaned.length <= 15) {
    return cleaned;
  }
  
  return '';
};

// Coupon code sanitization
export const sanitizeCouponCode = (code: string): string => {
  if (!code) return '';
  
  return code
    .trim()
    .toUpperCase()
    .replace(/[^A-Z0-9]/g, '') // Only allow alphanumeric
    .substring(0, 20); // Limit length
};

// Rate limiting for coupon attempts
export const checkCouponRateLimit = (userId: string): boolean => {
  const key = `coupon_${userId}`;
  const now = Date.now();
  const record = rateLimitStore.get(key);
  
  if (!record) {
    rateLimitStore.set(key, { count: 1, lastAttempt: now });
    return true;
  }
  
  // Reset if window expired
  if (now - record.lastAttempt > COUPON_ATTEMPT_WINDOW) {
    rateLimitStore.set(key, { count: 1, lastAttempt: now });
    return true;
  }
  
  // Check if limit exceeded
  if (record.count >= MAX_COUPON_ATTEMPTS) {
    toast({
      title: "Rate limit exceeded",
      description: "Too many coupon attempts. Please wait a minute.",
      variant: "destructive",
    });
    return false;
  }
  
  // Increment count
  record.count++;
  record.lastAttempt = now;
  rateLimitStore.set(key, record);
  
  return true;
};

// Rate limiting for payment attempts
export const checkPaymentRateLimit = (userId: string): boolean => {
  const key = `payment_${userId}`;
  const now = Date.now();
  const record = rateLimitStore.get(key);
  
  if (!record) {
    rateLimitStore.set(key, { count: 1, lastAttempt: now });
    return true;
  }
  
  // Reset if window expired
  if (now - record.lastAttempt > PAYMENT_ATTEMPT_WINDOW) {
    rateLimitStore.set(key, { count: 1, lastAttempt: now });
    return true;
  }
  
  // Check if limit exceeded
  if (record.count >= MAX_PAYMENT_ATTEMPTS) {
    toast({
      title: "Payment rate limit",
      description: "Please wait 30 seconds before trying again.",
      variant: "destructive",
    });
    return false;
  }
  
  return false; // Only allow one payment attempt per window
};

// Debounced slot availability check
let slotCheckTimeout: NodeJS.Timeout | null = null;

export const debouncedSlotCheck = (callback: () => void): void => {
  if (slotCheckTimeout) {
    clearTimeout(slotCheckTimeout);
  }
  
  slotCheckTimeout = setTimeout(callback, SLOT_CHECK_DEBOUNCE);
};

// CSRF token generation and validation
export const generateCSRFToken = (): string => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

// Secure booking data validation
export interface SecureBookingData {
  courtId: string;
  date: string;
  startTime: string;
  endTime: string;
  slots: string[];
  couponCode?: string;
  guestName?: string;
  guestPhone?: string;
}

export const validateBookingData = (data: SecureBookingData): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Validate court ID (UUID format)
  if (!data.courtId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(data.courtId)) {
    errors.push('Invalid court ID format');
  }
  
  // Validate date (YYYY-MM-DD format)
  if (!data.date || !/^\d{4}-\d{2}-\d{2}$/.test(data.date)) {
    errors.push('Invalid date format');
  }
  
  // Validate time format (HH:MM:SS)
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;
  if (!data.startTime || !timeRegex.test(data.startTime)) {
    errors.push('Invalid start time format');
  }
  
  if (!data.endTime || !timeRegex.test(data.endTime)) {
    errors.push('Invalid end time format');
  }
  
  // Validate slots array
  if (!Array.isArray(data.slots) || data.slots.length === 0) {
    errors.push('No time slots selected');
  }
  
  // Validate guest name if provided
  if (data.guestName && data.guestName.length > 100) {
    errors.push('Guest name too long');
  }
  
  // Validate guest phone if provided
  if (data.guestPhone && !sanitizePhoneNumber(data.guestPhone)) {
    errors.push('Invalid phone number format');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

// Secure server-side price validation
export const validateBookingPriceSecurity = async (
  courtId: string,
  date: string,
  startTime: string,
  endTime: string,
  clientPrice: number
): Promise<{ valid: boolean; serverPrice?: number; error?: string }> => {
  try {
    const { data, error } = await supabase.rpc('validate_booking_price_security', {
      p_court_id: courtId,
      p_booking_date: date,
      p_start_time: startTime,
      p_end_time: endTime,
      p_client_price: clientPrice
    });
    
    if (error) {
      console.error('Price validation error:', error);
      return { valid: false, error: 'Price validation failed' };
    }
    
    return data;
  } catch (error) {
    console.error('Price validation exception:', error);
    return { valid: false, error: 'Price validation service unavailable' };
  }
};

// Secure payment verification
export const verifyPaymentSecurity = async (
  paymentId: string,
  orderId: string,
  signature: string
): Promise<{ valid: boolean; error?: string }> => {
  try {
    const { data, error } = await supabase.functions.invoke('verify-payment-security', {
      body: {
        paymentId,
        orderId,
        signature
      }
    });
    
    if (error) {
      console.error('Payment verification error:', error);
      return { valid: false, error: 'Payment verification failed' };
    }
    
    return data;
  } catch (error) {
    console.error('Payment verification exception:', error);
    return { valid: false, error: 'Payment verification service unavailable' };
  }
};

// Authentication state verification
export const verifyAuthenticationState = (user: any): boolean => {
  if (!user) {
    toast({
      title: "Authentication required",
      description: "Please log in to continue booking",
      variant: "destructive",
    });
    return false;
  }
  
  if (!user.id) {
    toast({
      title: "Authentication incomplete",
      description: "Please refresh and try again",
      variant: "destructive",
    });
    return false;
  }
  
  return true;
};

// Security event severity mapping
const getSeverityLevel = (eventType: string): string => {
  const severityMap: Record<string, string> = {
    'BOOKING_PAGE_SECURITY_INITIALIZED': 'LOW',
    'COUPON_APPLIED_SUCCESS': 'LOW',
    'COUPON_VALIDATION_ERROR': 'MEDIUM',
    'COUPON_RATE_LIMIT_VIOLATION': 'MEDIUM',
    'PRICE_TAMPERING_ATTEMPT': 'HIGH',
    'PAYMENT_ORDER_CREATED': 'LOW',
    'PAYMENT_VERIFICATION_SUCCESS': 'LOW',
    'PAYMENT_SIGNATURE_VERIFICATION_FAILED': 'CRITICAL',
    'BOOKING_CREATED_SUCCESS': 'LOW',
    'BOOKING_CREATION_FAILED': 'HIGH',
    'PAYMENT_CANCELLED': 'LOW',
    'PAYMENT_INITIATED': 'LOW',
    'PAYMENT_INITIALIZATION_ERROR': 'HIGH',
    'BOOKING_DATA_VALIDATION_FAILED': 'MEDIUM'
  };

  return severityMap[eventType] || 'MEDIUM';
};

// Secure logging utility using database RPC function
export const logSecurityEvent = async (
  eventType: string,
  details: Record<string, any>,
  userId?: string
): Promise<void> => {
  try {
    // Add browser context to details
    const enhancedDetails = {
      ...details,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    const { error } = await supabase.rpc('log_security_event', {
      p_event_type: eventType,
      p_severity: getSeverityLevel(eventType),
      p_details: enhancedDetails,
      p_user_id: userId || null
    });

    if (error) {
      console.error('Security logging failed:', error);
    }
  } catch (error) {
    console.error('Security logging exception:', error);
  }
};

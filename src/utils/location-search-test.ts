// Test utility for validating location search functionality
import { googleMapsService } from './google-maps';
import { geocoding } from './geocoding';

export interface LocationSearchTestResult {
  query: string;
  googleMapsResults: number;
  geocodingResults: number;
  googleMapsSuccess: boolean;
  geocodingSuccess: boolean;
  totalTime: number;
}

export class LocationSearchTester {
  // Test queries for Delhi NCR validation
  private testQueries = [
    'Connaught Place',
    'South Delhi',
    'Noida Sector 18',
    'Gurgaon Cyber City',
    'Karol Bagh',
    'Khan Market',
    'Dwarka',
    'Rohini',
    'Faridabad',
    'Ghaziabad'
  ];

  // Run comprehensive location search tests
  async runTests(): Promise<LocationSearchTestResult[]> {
    console.log('🧪 Starting location search tests...');
    const results: LocationSearchTestResult[] = [];

    for (const query of this.testQueries) {
      const result = await this.testSingleQuery(query);
      results.push(result);
      
      // Add delay between tests to respect rate limits
      await this.delay(500);
    }

    this.logTestSummary(results);
    return results;
  }

  // Test a single query against both services
  private async testSingleQuery(query: string): Promise<LocationSearchTestResult> {
    console.log(`🔍 Testing query: "${query}"`);
    const startTime = Date.now();

    let googleMapsResults = 0;
    let geocodingResults = 0;
    let googleMapsSuccess = false;
    let geocodingSuccess = false;

    // Test Google Maps
    try {
      const gmResults = await googleMapsService.searchAddresses(query);
      googleMapsResults = gmResults.length;
      googleMapsSuccess = true;
      console.log(`  ✅ Google Maps: ${googleMapsResults} results`);
    } catch (error) {
      console.log(`  ❌ Google Maps failed:`, error);
    }

    // Test geocoding fallback
    try {
      const delhiQuery = `${query}, New Delhi, India`;
      const geoResults = await geocoding.searchAddresses(delhiQuery);
      
      // Apply Delhi filtering
      const filteredResults = geoResults.filter(suggestion => {
        const displayName = suggestion.display_name?.toLowerCase() || '';
        const city = suggestion.city?.toLowerCase() || '';
        const area = suggestion.area?.toLowerCase() || '';
        const state = suggestion.state?.toLowerCase() || '';
        
        return (
          city.includes('delhi') || 
          city.includes('new delhi') ||
          area.includes('delhi') ||
          area.includes('new delhi') ||
          displayName.includes('delhi') ||
          displayName.includes('new delhi') ||
          displayName.includes('gurgaon') ||
          displayName.includes('gurugram') ||
          displayName.includes('noida') ||
          displayName.includes('faridabad') ||
          displayName.includes('ghaziabad') ||
          state.includes('delhi')
        ) && suggestion.country?.toLowerCase().includes('india');
      });

      geocodingResults = filteredResults.length;
      geocodingSuccess = true;
      console.log(`  ✅ Geocoding: ${geocodingResults} results`);
    } catch (error) {
      console.log(`  ❌ Geocoding failed:`, error);
    }

    const totalTime = Date.now() - startTime;

    return {
      query,
      googleMapsResults,
      geocodingResults,
      googleMapsSuccess,
      geocodingSuccess,
      totalTime
    };
  }

  // Log test summary
  private logTestSummary(results: LocationSearchTestResult[]): void {
    console.log('\n📊 Test Summary:');
    console.log('================');

    const totalTests = results.length;
    const googleMapsSuccessCount = results.filter(r => r.googleMapsSuccess).length;
    const geocodingSuccessCount = results.filter(r => r.geocodingSuccess).length;
    const avgGoogleMapsResults = results.reduce((sum, r) => sum + r.googleMapsResults, 0) / totalTests;
    const avgGeocodingResults = results.reduce((sum, r) => sum + r.geocodingResults, 0) / totalTests;
    const avgTime = results.reduce((sum, r) => sum + r.totalTime, 0) / totalTests;

    console.log(`Total queries tested: ${totalTests}`);
    console.log(`Google Maps success rate: ${googleMapsSuccessCount}/${totalTests} (${(googleMapsSuccessCount/totalTests*100).toFixed(1)}%)`);
    console.log(`Geocoding success rate: ${geocodingSuccessCount}/${totalTests} (${(geocodingSuccessCount/totalTests*100).toFixed(1)}%)`);
    console.log(`Average Google Maps results: ${avgGoogleMapsResults.toFixed(1)}`);
    console.log(`Average geocoding results: ${avgGeocodingResults.toFixed(1)}`);
    console.log(`Average response time: ${avgTime.toFixed(0)}ms`);

    // Show best and worst performing queries
    const bestGoogleMaps = results.reduce((best, current) => 
      current.googleMapsResults > best.googleMapsResults ? current : best
    );
    const worstGoogleMaps = results.reduce((worst, current) => 
      current.googleMapsResults < worst.googleMapsResults ? current : worst
    );

    console.log(`\nBest Google Maps query: "${bestGoogleMaps.query}" (${bestGoogleMaps.googleMapsResults} results)`);
    console.log(`Worst Google Maps query: "${worstGoogleMaps.query}" (${worstGoogleMaps.googleMapsResults} results)`);
  }

  // Utility delay function
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Test API availability
  async testAPIAvailability(): Promise<{ googleMaps: boolean; geocoding: boolean }> {
    console.log('🔧 Testing API availability...');

    let googleMaps = false;
    let geocoding = false;

    try {
      await googleMapsService.loadGoogleMapsAPI();
      googleMaps = googleMapsService.isAPILoaded();
      console.log(`Google Maps API: ${googleMaps ? '✅ Available' : '❌ Not available'}`);
    } catch (error) {
      console.log('Google Maps API: ❌ Failed to load');
    }

    try {
      const testResult = await geocoding.searchAddresses('test');
      geocoding = true;
      console.log('Geocoding API: ✅ Available');
    } catch (error) {
      console.log('Geocoding API: ❌ Not available');
    }

    return { googleMaps, geocoding };
  }
}

// Export singleton instance
export const locationSearchTester = new LocationSearchTester();

// Console helper for manual testing
if (typeof window !== 'undefined') {
  (window as any).testLocationSearch = locationSearchTester;
}

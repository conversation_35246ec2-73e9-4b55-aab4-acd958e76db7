// Security utilities for Grid2Play booking system

import DOMPurify from 'dompurify';

/**
 * Input sanitization and validation utilities
 */
export class SecurityUtils {
  
  /**
   * Sanitize user input to prevent XSS attacks
   */
  static sanitizeInput(input: string): string {
    if (!input) return '';
    
    // Remove HTML tags and dangerous characters
    const sanitized = DOMPurify.sanitize(input, { 
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: []
    });
    
    // Additional cleaning
    return sanitized
      .replace(/[<>\"'`]/g, '') // Remove remaining dangerous chars
      .trim()
      .substring(0, 255); // Limit length
  }

  /**
   * Validate phone number (Indian format)
   */
  static validatePhoneNumber(phone: string): { valid: boolean; error?: string } {
    if (!phone) return { valid: false, error: 'Phone number is required' };
    
    const cleanPhone = phone.replace(/\D/g, ''); // Remove non-digits
    
    if (cleanPhone.length !== 10) {
      return { valid: false, error: 'Phone number must be 10 digits' };
    }
    
    if (!/^[6-9]/.test(cleanPhone)) {
      return { valid: false, error: 'Phone number must start with 6, 7, 8, or 9' };
    }
    
    return { valid: true };
  }

  /**
   * Validate name input
   */
  static validateName(name: string): { valid: boolean; error?: string } {
    if (!name) return { valid: false, error: 'Name is required' };
    
    const cleanName = name.trim();
    
    if (cleanName.length < 2) {
      return { valid: false, error: 'Name must be at least 2 characters' };
    }
    
    if (cleanName.length > 50) {
      return { valid: false, error: 'Name must be less than 50 characters' };
    }
    
    if (!/^[a-zA-Z\s]+$/.test(cleanName)) {
      return { valid: false, error: 'Name can only contain letters and spaces' };
    }
    
    return { valid: true };
  }

  /**
   * Validate coupon code
   */
  static validateCouponCode(code: string): { valid: boolean; error?: string } {
    if (!code) return { valid: true }; // Optional field
    
    const cleanCode = code.trim().toUpperCase();
    
    if (cleanCode.length < 3 || cleanCode.length > 20) {
      return { valid: false, error: 'Coupon code must be 3-20 characters' };
    }
    
    if (!/^[A-Z0-9]+$/.test(cleanCode)) {
      return { valid: false, error: 'Coupon code can only contain letters and numbers' };
    }
    
    return { valid: true };
  }

  /**
   * Rate limiting utility
   */
  static createRateLimiter(windowMs: number, maxAttempts: number) {
    const attempts = new Map<string, { count: number; resetTime: number }>();
    
    return {
      checkLimit: (identifier: string): { allowed: boolean; resetTime?: number } => {
        const now = Date.now();
        const userAttempts = attempts.get(identifier);
        
        if (!userAttempts || now > userAttempts.resetTime) {
          // Reset or create new entry
          attempts.set(identifier, { count: 1, resetTime: now + windowMs });
          return { allowed: true };
        }
        
        if (userAttempts.count >= maxAttempts) {
          return { allowed: false, resetTime: userAttempts.resetTime };
        }
        
        userAttempts.count++;
        return { allowed: true };
      },
      
      reset: (identifier: string) => {
        attempts.delete(identifier);
      }
    };
  }

  /**
   * Generate secure booking reference
   */
  static generateBookingReference(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `BK${timestamp}${random}`.toUpperCase();
  }

  /**
   * Validate price integrity
   */
  static validatePriceIntegrity(
    submittedPrice: number,
    calculatedPrice: number,
    tolerance: number = 1
  ): { valid: boolean; error?: string } {
    if (typeof submittedPrice !== 'number' || submittedPrice < 0) {
      return { valid: false, error: 'Invalid price format' };
    }
    
    if (typeof calculatedPrice !== 'number' || calculatedPrice < 0) {
      return { valid: false, error: 'Invalid calculated price' };
    }
    
    const difference = Math.abs(submittedPrice - calculatedPrice);
    
    if (difference > tolerance) {
      return { 
        valid: false, 
        error: `Price mismatch: submitted ₹${submittedPrice}, expected ₹${calculatedPrice}` 
      };
    }
    
    return { valid: true };
  }

  /**
   * Validate slot selection integrity
   */
  static validateSlotSelection(slots: string[]): { valid: boolean; error?: string } {
    if (!Array.isArray(slots) || slots.length === 0) {
      return { valid: false, error: 'No slots selected' };
    }
    
    if (slots.length > 10) {
      return { valid: false, error: 'Too many slots selected' };
    }
    
    // Validate slot format
    const slotRegex = /^\d{1,2}:\d{2}\s?(AM|PM)\s?-\s?\d{1,2}:\d{2}\s?(AM|PM)$/i;
    
    for (const slot of slots) {
      if (!slotRegex.test(slot)) {
        return { valid: false, error: `Invalid slot format: ${slot}` };
      }
    }
    
    return { valid: true };
  }

  /**
   * Mask sensitive data for logging
   */
  static maskSensitiveData(data: any): any {
    if (typeof data !== 'object' || data === null) return data;
    
    const masked = { ...data };
    
    // Mask phone numbers
    if (masked.phone) {
      masked.phone = masked.phone.replace(/(\d{3})\d{4}(\d{3})/, '$1****$2');
    }
    
    // Mask email
    if (masked.email) {
      const [local, domain] = masked.email.split('@');
      masked.email = `${local.substring(0, 2)}****@${domain}`;
    }
    
    // Mask payment IDs
    if (masked.payment_id || masked.paymentId) {
      const paymentId = masked.payment_id || masked.paymentId;
      masked.payment_id = masked.paymentId = `${paymentId.substring(0, 8)}****`;
    }
    
    return masked;
  }

  /**
   * Generate CSRF token
   */
  static generateCSRFToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Validate date input
   */
  static validateBookingDate(date: string): { valid: boolean; error?: string } {
    if (!date) return { valid: false, error: 'Date is required' };
    
    const bookingDate = new Date(date);
    const today = new Date();
    const maxDate = new Date();
    maxDate.setDate(today.getDate() + 30); // 30 days ahead limit
    
    if (isNaN(bookingDate.getTime())) {
      return { valid: false, error: 'Invalid date format' };
    }
    
    if (bookingDate < today) {
      return { valid: false, error: 'Cannot book for past dates' };
    }
    
    if (bookingDate > maxDate) {
      return { valid: false, error: 'Cannot book more than 30 days ahead' };
    }
    
    return { valid: true };
  }

  /**
   * Validate UUID format
   */
  static validateUUID(uuid: string): { valid: boolean; error?: string } {
    if (!uuid) return { valid: false, error: 'ID is required' };
    
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (!uuidRegex.test(uuid)) {
      return { valid: false, error: 'Invalid ID format' };
    }
    
    return { valid: true };
  }
}

/**
 * Booking-specific security validations
 */
export class BookingSecurityValidator {
  
  /**
   * Validate complete booking request
   */
  static validateBookingRequest(request: {
    venueId: string;
    sportId: string;
    courtId: string;
    date: string;
    slots: string[];
    totalPrice: number;
    guestName?: string;
    guestPhone?: string;
    couponCode?: string;
  }): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Validate IDs
    const venueValidation = SecurityUtils.validateUUID(request.venueId);
    if (!venueValidation.valid) errors.push(`Venue: ${venueValidation.error}`);
    
    const sportValidation = SecurityUtils.validateUUID(request.sportId);
    if (!sportValidation.valid) errors.push(`Sport: ${sportValidation.error}`);
    
    const courtValidation = SecurityUtils.validateUUID(request.courtId);
    if (!courtValidation.valid) errors.push(`Court: ${courtValidation.error}`);
    
    // Validate date
    const dateValidation = SecurityUtils.validateBookingDate(request.date);
    if (!dateValidation.valid) errors.push(dateValidation.error!);
    
    // Validate slots
    const slotsValidation = SecurityUtils.validateSlotSelection(request.slots);
    if (!slotsValidation.valid) errors.push(slotsValidation.error!);
    
    // Validate price
    if (typeof request.totalPrice !== 'number' || request.totalPrice <= 0) {
      errors.push('Invalid total price');
    }
    
    // Validate guest info if provided
    if (request.guestName) {
      const nameValidation = SecurityUtils.validateName(request.guestName);
      if (!nameValidation.valid) errors.push(`Guest name: ${nameValidation.error}`);
    }
    
    if (request.guestPhone) {
      const phoneValidation = SecurityUtils.validatePhoneNumber(request.guestPhone);
      if (!phoneValidation.valid) errors.push(`Guest phone: ${phoneValidation.error}`);
    }
    
    // Validate coupon if provided
    if (request.couponCode) {
      const couponValidation = SecurityUtils.validateCouponCode(request.couponCode);
      if (!couponValidation.valid) errors.push(`Coupon: ${couponValidation.error}`);
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}

export default SecurityUtils;

/**
 * Weekly Cycle Utilities for Grid2Play Admin Dashboard
 * Handles day calculations for Monday-Sunday weekly cycles in IST timezone
 */

import { format, startOfWeek, differenceInDays } from 'date-fns';

/**
 * Get current date and time in IST timezone
 * IST is UTC+5:30 (330 minutes ahead of UTC)
 */
function getCurrentIST(): Date {
  const now = new Date();
  const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
  const istOffset = 5.5 * 60 * 60 * 1000; // IST is UTC+5:30
  return new Date(utc + istOffset);
}



/**
 * Get the current day number in the weekly cycle (Monday = 1, Sunday = 7)
 * Uses simple day-of-week calculation
 *
 * @param targetDate - Optional date to check (defaults to current date)
 * @param weekStartDate - The Monday that starts the week being viewed
 * @returns Day number (1-7) or 0 if targetDate is not in the current week
 */
export function getCurrentDayInWeekCycle(
  targetDate?: Date,
  weekStartDate?: Date
): number {
  // Use current IST date if no target date provided
  const workingDate = targetDate || getCurrentIST();

  // Get the Monday of the current week in IST
  const currentWeekStart = weekStartDate || startOfWeek(workingDate, { weekStartsOn: 1 });

  // If we're checking against a specific week, make sure the target date is in that week
  if (weekStartDate) {
    const daysDiff = differenceInDays(workingDate, weekStartDate);
    if (daysDiff < 0 || daysDiff > 6) {
      return 0; // Date is not in the specified week
    }
  }

  // Calculate day number (Monday = 1, Tuesday = 2, ..., Sunday = 7)
  const daysDifference = differenceInDays(workingDate, currentWeekStart);

  // Debug logging
  console.log('🔧 Day Calculation Debug:', {
    workingDate: workingDate.toISOString(),
    workingDateLocal: workingDate.toString(),
    currentWeekStart: format(currentWeekStart, 'yyyy-MM-dd'),
    daysDifference,
    calculatedDay: daysDifference + 1,
    dayOfWeek: format(workingDate, 'EEEE'),
    jsDay: workingDate.getDay(), // 0=Sunday, 1=Monday, etc.
  });

  // Ensure we're within the week (0-6 days from Monday)
  if (daysDifference >= 0 && daysDifference <= 6) {
    return daysDifference + 1; // Convert 0-6 to 1-7
  }

  return 0; // Date is not in the current week
}

/**
 * Get the active days display string for the dashboard
 * Shows current day number out of 7 for current week, or days with data for historical weeks
 * 
 * @param weekStartDate - The Monday that starts the week being viewed
 * @param daysWithData - Number of days that have booking data
 * @returns String in format "X/7" where X is either current day or days with data
 */
export function getActiveDaysDisplay(
  weekStartDate: Date,
  daysWithData: number
): string {
  // Check if we're viewing the current week using IST
  const now = getCurrentIST();
  const currentWeekStart = startOfWeek(now, { weekStartsOn: 1 });

  const isCurrentWeek = format(weekStartDate, 'yyyy-MM-dd') === format(currentWeekStart, 'yyyy-MM-dd');

  if (isCurrentWeek) {
    // For current week, show current day number
    const currentDay = getCurrentDayInWeekCycle(now, weekStartDate);
    console.log('🔧 Active Days Debug:', {
      now: now.toISOString(),
      nowLocal: now.toString(),
      weekStartDate: format(weekStartDate, 'yyyy-MM-dd'),
      currentWeekStart: format(currentWeekStart, 'yyyy-MM-dd'),
      currentDay,
      isCurrentWeek,
      dayName: format(now, 'EEEE'),
      todayIs: `Today is ${format(now, 'EEEE')}, should be day ${currentDay}/7`
    });
    return `${currentDay}/7`;
  } else {
    // For historical weeks, show days with data
    return `${daysWithData}/7`;
  }
}

/**
 * Get the active days label for the dashboard
 * Returns appropriate label based on whether we're viewing current or historical week
 * 
 * @param weekStartDate - The Monday that starts the week being viewed
 * @returns Label string for the active days counter
 */
export function getActiveDaysLabel(weekStartDate: Date): string {
  const now = getCurrentIST();
  const currentWeekStart = startOfWeek(now, { weekStartsOn: 1 });

  const isCurrentWeek = format(weekStartDate, 'yyyy-MM-dd') === format(currentWeekStart, 'yyyy-MM-dd');

  return isCurrentWeek ? 'Active Days' : 'Days with Data';
}

/**
 * Get date string in YYYY-MM-DD format
 * Useful for date comparisons and API calls
 *
 * @param date - Optional date (defaults to current date)
 * @returns Date string
 */
export function getISTDateString(date?: Date): string {
  const istDate = date ? new Date(date.getTime() + (5.5 * 60 * 60 * 1000)) : getCurrentIST();
  return format(istDate, 'yyyy-MM-dd');
}

/**
 * Check if a given date is today in IST timezone
 * 
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns True if the date is today in IST
 */
export function isToday(dateString: string): boolean {
  const todayIST = getISTDateString();
  const isMatch = dateString === todayIST;

  // Debug logging for timezone verification
  if (import.meta.env.DEV) {
    console.log('🕐 IST Today Check (Fixed):', {
      dateString,
      todayIST,
      isMatch,
      currentISTTime: getCurrentIST().toISOString(),
      note: 'Using proper IST timezone calculation'
    });
  }

  return isMatch;
}

/**
 * Get day name for a given date in IST timezone
 * 
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Day name (e.g., "Monday", "Tuesday")
 */
export function getDayName(dateString: string): string {
  const date = new Date(dateString + 'T00:00:00');
  return format(date, 'EEEE');
}

/**
 * Debug function to log current time and day calculations
 * Useful for troubleshooting day calculation issues
 */
export function debugWeeklyUtils(): void {
  const now = getCurrentIST();
  const utcNow = new Date();
  const currentWeekStart = startOfWeek(now, { weekStartsOn: 1 });
  const currentDay = getCurrentDayInWeekCycle();

  console.log('🕐 Weekly Utils Debug Info (IST Fixed):');
  console.log('UTC Time:', utcNow.toISOString());
  console.log('IST Time:', now.toISOString());
  console.log('IST Date String:', getISTDateString());
  console.log('Current Week Start (Monday):', format(currentWeekStart, 'yyyy-MM-dd'));
  console.log('Current Day in Cycle:', currentDay);
  console.log('Day Name:', getDayName(getISTDateString()));
  console.log('IST Day (0=Sun, 1=Mon):', now.getDay());
  console.log('UTC vs IST Difference:', (now.getTime() - utcNow.getTime()) / (1000 * 60 * 60), 'hours');
}

// Export for use in browser console
// Debug functions available in development only
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  (window as unknown as Record<string, unknown>).debugWeeklyUtils = debugWeeklyUtils;
  (window as unknown as Record<string, unknown>).getCurrentDayInWeekCycle = getCurrentDayInWeekCycle;
  (window as unknown as Record<string, unknown>).getActiveDaysDisplay = getActiveDaysDisplay;
}

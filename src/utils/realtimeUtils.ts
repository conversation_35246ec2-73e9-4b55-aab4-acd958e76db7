import { supabase } from '@/integrations/supabase/client';

// WebSocket connection state management
export interface RealtimeConnectionState {
  isConnected: boolean;
  lastConnected: Date | null;
  retryCount: number;
  maxRetries: number;
}

// Create a connection state manager
class RealtimeConnectionManager {
  private state: RealtimeConnectionState = {
    isConnected: false,
    lastConnected: null,
    retryCount: 0,
    maxRetries: 5
  };

  private listeners: Array<(state: RealtimeConnectionState) => void> = [];

  getState(): RealtimeConnectionState {
    return { ...this.state };
  }

  updateState(updates: Partial<RealtimeConnectionState>) {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }

  addListener(listener: (state: RealtimeConnectionState) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.getState()));
  }

  reset() {
    this.state = {
      isConnected: false,
      lastConnected: null,
      retryCount: 0,
      maxRetries: 5
    };
    this.notifyListeners();
  }
}

export const connectionManager = new RealtimeConnectionManager();

// Enhanced subscription creation with proper error handling
export interface SubscriptionConfig {
  channelName: string;
  table: string;
  filter: string;
  onUpdate: (payload: any) => void;
  onError?: (error: any) => void;
}

export function createSecureSubscription(config: SubscriptionConfig) {
  const { channelName, table, filter, onUpdate, onError } = config;
  
  return supabase
    .channel(channelName)
    .on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: table,
      filter: filter
    }, (payload) => {
      try {
        // Secure logging - no sensitive data exposed
        if (payload.eventType) {
          onUpdate(payload);
        }
      } catch (error) {
        console.warn('Error processing real-time update:', error);
        onError?.(error);
      }
    })
    .subscribe((status) => {
      switch (status) {
        case 'SUBSCRIBED':
          connectionManager.updateState({
            isConnected: true,
            lastConnected: new Date(),
            retryCount: 0
          });
          break;
        case 'CHANNEL_ERROR':
          connectionManager.updateState({
            isConnected: false,
            retryCount: connectionManager.getState().retryCount + 1
          });
          console.warn(`Channel subscription error for ${channelName}`);
          onError?.(new Error(`Channel subscription error for ${channelName}`));
          break;
        case 'TIMED_OUT':
          connectionManager.updateState({
            isConnected: false,
            retryCount: connectionManager.getState().retryCount + 1
          });
          console.warn(`Channel subscription timed out for ${channelName}`);
          onError?.(new Error(`Channel subscription timed out for ${channelName}`));
          break;
        case 'CLOSED':
          connectionManager.updateState({
            isConnected: false
          });
          break;
      }
    });
}

// Utility to safely remove channels
export function safeRemoveChannel(channel: ReturnType<typeof supabase.channel> | null) {
  if (channel) {
    try {
      supabase.removeChannel(channel);
    } catch (error) {
      console.warn('Error removing channel:', error);
    }
  }
}

// Check if user session is valid for real-time subscriptions
export async function validateSessionForRealtime(): Promise<boolean> {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error || !session) {
      console.warn('No valid session for real-time subscriptions');
      return false;
    }
    return true;
  } catch (error) {
    console.warn('Error validating session for real-time:', error);
    return false;
  }
}

// Debounced function creator for real-time updates
export function createDebouncedUpdate(fn: () => void, delay: number = 500) {
  let timeoutId: NodeJS.Timeout;
  
  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(fn, delay);
  };
}

// Connection health monitor
export function monitorRealtimeHealth() {
  const checkInterval = 30000; // 30 seconds
  
  const healthCheck = setInterval(() => {
    const state = connectionManager.getState();
    
    if (!state.isConnected && state.retryCount >= state.maxRetries) {
      console.warn('Real-time connection health check failed - max retries exceeded');
      connectionManager.reset();
    }
    
    if (state.lastConnected && Date.now() - state.lastConnected.getTime() > 60000) {
      console.warn('Real-time connection appears stale');
    }
  }, checkInterval);
  
  return () => clearInterval(healthCheck);
}

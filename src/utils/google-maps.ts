import { Address } from '@/types/location';
import { 
  GoogleMapsConfig, 
  PlacesPrediction, 
  PlaceDetailsResult, 
  AutocompleteRequest,
  PlaceDetailsRequest,
  DELHI_NCR_BOUNDS,
  GoogleMapsStatus,
  AddressComponentTypes
} from '@/types/google-maps';

class GoogleMapsService {
  private isLoaded = false;
  private isLoading = false;
  private autocompleteService: any = null;
  private placesService: any = null;
  private searchCache = new Map<string, Address[]>();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes

  private config: GoogleMapsConfig = {
    apiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '',
    libraries: ['places'],
    region: 'IN',
    language: 'en'
  };

  // Load Google Maps API dynamically
  async loadGoogleMapsAPI(): Promise<boolean> {
    if (this.isLoaded) return true;
    if (this.isLoading) {
      // Wait for current loading to complete
      return new Promise((resolve) => {
        const checkLoaded = () => {
          if (this.isLoaded) resolve(true);
          else if (!this.isLoading) resolve(false);
          else setTimeout(checkLoaded, 100);
        };
        checkLoaded();
      });
    }

    if (!this.config.apiKey) {
      console.error('Google Maps API key not found in environment variables');
      return false;
    }

    this.isLoading = true;

    try {
      // Create script element
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${this.config.apiKey}&libraries=${this.config.libraries.join(',')}&region=${this.config.region}&language=${this.config.language}&callback=initGoogleMaps`;
      script.async = true;
      script.defer = true;

      // Set up callback
      return new Promise((resolve, reject) => {
        window.initGoogleMaps = () => {
          try {
            this.initializeServices();
            this.isLoaded = true;
            this.isLoading = false;
            resolve(true);
          } catch (error) {
            console.error('Failed to initialize Google Maps services:', error);
            this.isLoading = false;
            reject(false);
          }
        };

        script.onerror = () => {
          console.error('Failed to load Google Maps API');
          this.isLoading = false;
          reject(false);
        };

        document.head.appendChild(script);

        // Timeout after 10 seconds
        setTimeout(() => {
          if (!this.isLoaded) {
            console.error('Google Maps API loading timeout');
            this.isLoading = false;
            reject(false);
          }
        }, 10000);
      });
    } catch (error) {
      console.error('Error loading Google Maps API:', error);
      this.isLoading = false;
      return false;
    }
  }

  // Initialize Google Maps services
  private initializeServices(): void {
    if (!window.google?.maps?.places) {
      throw new Error('Google Maps Places API not available');
    }

    this.autocompleteService = new window.google.maps.places.AutocompleteService();
    
    // Create a dummy div for PlacesService (required by API)
    const dummyDiv = document.createElement('div');
    this.placesService = new window.google.maps.places.PlacesService(dummyDiv);
  }

  // Search for address suggestions using Places Autocomplete
  async searchAddresses(query: string): Promise<Address[]> {
    if (query.length < 2) return [];

    // Check cache first
    const cacheKey = query.toLowerCase();
    const cached = this.searchCache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // Ensure API is loaded
    const isLoaded = await this.loadGoogleMapsAPI();
    if (!isLoaded || !this.autocompleteService) {
      console.warn('Google Maps API not available, falling back to basic geocoding');
      return [];
    }

    return new Promise((resolve) => {
      const request: AutocompleteRequest = {
        input: query,
        componentRestrictions: { country: 'IN' },
        bounds: DELHI_NCR_BOUNDS,
        types: ['geocode'],
        strictBounds: false // Allow some flexibility for NCR areas
      };

      this.autocompleteService.getPlacePredictions(
        request,
        async (predictions: PlacesPrediction[] | null, status: string) => {
          if (status !== GoogleMapsStatus.OK || !predictions) {
            console.warn('Google Places Autocomplete failed:', status);
            resolve([]);
            return;
          }

          // Filter predictions for Delhi NCR area
          const delhiPredictions = predictions.filter(prediction => 
            this.isDelhiNCRLocation(prediction.description)
          );

          // Convert predictions to Address objects
          const addresses = await Promise.all(
            delhiPredictions.slice(0, 5).map(prediction => 
              this.convertPredictionToAddress(prediction)
            )
          );

          // Filter out null results
          const validAddresses = addresses.filter(addr => addr !== null) as Address[];

          // Cache results
          this.searchCache.set(cacheKey, validAddresses);
          setTimeout(() => this.searchCache.delete(cacheKey), this.cacheExpiry);

          resolve(validAddresses);
        }
      );
    });
  }

  // Check if location is in Delhi NCR area
  private isDelhiNCRLocation(description: string): boolean {
    const lowerDesc = description.toLowerCase();
    return (
      lowerDesc.includes('delhi') ||
      lowerDesc.includes('new delhi') ||
      lowerDesc.includes('gurgaon') ||
      lowerDesc.includes('gurugram') ||
      lowerDesc.includes('noida') ||
      lowerDesc.includes('faridabad') ||
      lowerDesc.includes('ghaziabad') ||
      lowerDesc.includes('ncr')
    );
  }

  // Convert Google Places prediction to Address interface
  private async convertPredictionToAddress(prediction: PlacesPrediction): Promise<Address | null> {
    if (!this.placesService) return null;

    return new Promise((resolve) => {
      const request: PlaceDetailsRequest = {
        placeId: prediction.place_id,
        fields: ['address_components', 'formatted_address', 'geometry', 'name']
      };

      this.placesService.getDetails(request, (result: PlaceDetailsResult | null, status: string) => {
        if (status !== GoogleMapsStatus.OK || !result) {
          console.warn('Place details request failed:', status);
          // Fallback to basic parsing from prediction
          resolve(this.parseBasicAddress(prediction));
          return;
        }

        resolve(this.parseDetailedAddress(result));
      });
    });
  }

  // Parse address from place details with coordinates
  private parseDetailedAddress(placeDetails: PlaceDetailsResult): Address {
    const components = placeDetails.address_components;
    const address: Address = {
      display_name: placeDetails.formatted_address
    };

    // Extract coordinates from geometry
    if (placeDetails.geometry?.location) {
      (address as any).coordinates = {
        latitude: placeDetails.geometry.location.lat(),
        longitude: placeDetails.geometry.location.lng()
      };
    }

    components.forEach(component => {
      const types = component.types;

      if (types.includes(AddressComponentTypes.ROUTE)) {
        address.street = component.long_name;
      } else if (types.includes(AddressComponentTypes.SUBLOCALITY_LEVEL_1)) {
        address.area = component.long_name;
      } else if (types.includes(AddressComponentTypes.LOCALITY)) {
        address.city = component.long_name;
      } else if (types.includes(AddressComponentTypes.ADMINISTRATIVE_AREA_LEVEL_1)) {
        address.state = component.long_name;
      } else if (types.includes(AddressComponentTypes.COUNTRY)) {
        address.country = component.long_name;
      } else if (types.includes(AddressComponentTypes.POSTAL_CODE)) {
        address.postal_code = component.long_name;
      }
    });

    // Ensure we have city/area information
    if (!address.city && !address.area) {
      address.area = placeDetails.name;
    }

    return address;
  }

  // Fallback parsing from prediction only
  private parseBasicAddress(prediction: PlacesPrediction): Address {
    const terms = prediction.terms;
    const address: Address = {
      display_name: prediction.description
    };

    if (terms.length > 0) {
      address.area = terms[0].value;
    }
    if (terms.length > 1) {
      address.city = terms[1].value;
    }
    if (terms.length > 2) {
      address.state = terms[2].value;
    }

    return address;
  }

  // Clear cache
  clearCache(): void {
    this.searchCache.clear();
  }

  // Check if API is loaded
  isAPILoaded(): boolean {
    return this.isLoaded;
  }
}

// Export singleton instance
export const googleMapsService = new GoogleMapsService();

/**
 * Extension-safe navigation utilities for Grid2Play
 * Handles browser extension conflicts that interfere with React Router and authentication
 */

import { NavigateFunction } from 'react-router-dom';

interface ExtensionError extends Error {
  message: string;
  stack?: string;
}

/**
 * Detects if an error is caused by browser extension interference
 */
export function isExtensionError(error: any): boolean {
  if (!error) return false;
  
  const errorMessage = error.message || error.toString();
  const errorStack = error.stack || '';
  
  // Common extension error patterns
  const extensionPatterns = [
    'Extension context invalidated',
    'extend-native-history-api',
    'processor.js',
    'chrome-extension://',
    'moz-extension://',
    'safari-extension://',
    'Extension context was invalidated',
    'Cannot access a chrome:// URL',
    'Script error',
    'Non-Error promise rejection captured'
  ];
  
  return extensionPatterns.some(pattern => 
    errorMessage.includes(pattern) || errorStack.includes(pattern)
  );
}

/**
 * Safe navigation wrapper that handles extension interference
 */
export function safeNavigate(navigate: NavigateFunction, path: string, options?: { replace?: boolean }) {
  try {
    // First attempt: Use React Router navigation
    navigate(path, options);
  } catch (error) {
    if (isExtensionError(error)) {
      console.warn('Extension interference detected during navigation, using fallback method');
      // Fallback: Use native browser navigation
      try {
        if (options?.replace) {
          window.location.replace(path);
        } else {
          window.location.href = path;
        }
      } catch (fallbackError) {
        console.error('Both navigation methods failed:', { originalError: error, fallbackError });
        // Last resort: Force page reload to target path
        window.location.href = path;
      }
    } else {
      // Re-throw non-extension errors
      throw error;
    }
  }
}

/**
 * Safe history manipulation that avoids extension conflicts
 */
export function safeHistoryOperation(operation: () => void, fallback?: () => void) {
  try {
    operation();
  } catch (error) {
    if (isExtensionError(error)) {
      console.warn('Extension interference detected during history operation');
      if (fallback) {
        try {
          fallback();
        } catch (fallbackError) {
          console.error('History operation fallback failed:', fallbackError);
        }
      }
    } else {
      throw error;
    }
  }
}

/**
 * Wrapper for async operations that might be affected by extension interference
 */
export async function extensionSafeAsync<T>(
  operation: () => Promise<T>,
  fallback?: () => Promise<T> | T,
  errorContext?: string
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    if (isExtensionError(error)) {
      console.warn(`Extension interference detected${errorContext ? ` in ${errorContext}` : ''}, attempting fallback`);
      
      if (fallback) {
        try {
          return await fallback();
        } catch (fallbackError) {
          console.error(`Fallback failed for ${errorContext || 'operation'}:`, fallbackError);
          throw fallbackError;
        }
      } else {
        console.warn(`No fallback available for ${errorContext || 'operation'}`);
        throw error;
      }
    } else {
      // Re-throw non-extension errors
      throw error;
    }
  }
}

/**
 * Detects problematic browser extensions
 */
export function detectProblematicExtensions(): string[] {
  const problematicExtensions: string[] = [];
  
  try {
    // Check for common extension indicators in the global scope
    const globalChecks = [
      'chrome.extension',
      'browser.extension',
      'safari.extension'
    ];
    
    globalChecks.forEach(check => {
      const parts = check.split('.');
      let obj: any = window;
      for (const part of parts) {
        if (obj && typeof obj === 'object' && part in obj) {
          obj = obj[part];
        } else {
          obj = null;
          break;
        }
      }
      if (obj) {
        problematicExtensions.push(check);
      }
    });
    
    // Check for modified history API
    if (window.history && typeof window.history.pushState === 'function') {
      const historyString = window.history.pushState.toString();
      if (historyString.includes('extend-native-history-api') || 
          historyString.includes('processor.js') ||
          historyString.length > 1000) { // Unusually long function suggests modification
        problematicExtensions.push('History API modification detected');
      }
    }
    
  } catch (error) {
    console.warn('Error detecting extensions:', error);
  }
  
  return problematicExtensions;
}

/**
 * Initialize extension conflict detection and warnings
 */
export function initializeExtensionSafety() {
  // Only run in development or when explicitly enabled
  if (import.meta.env.DEV || import.meta.env.VITE_EXTENSION_DETECTION === 'true') {
    const problematicExtensions = detectProblematicExtensions();
    
    if (problematicExtensions.length > 0) {
      console.warn('⚠️ Potentially problematic browser extensions detected:', problematicExtensions);
      console.warn('💡 If you experience navigation or authentication issues, try disabling browser extensions');
    }
  }
  
  // Set up global error handler for extension errors
  const originalErrorHandler = window.onerror;
  window.onerror = (message, source, lineno, colno, error) => {
    if (isExtensionError(error || { message: message?.toString() || '' })) {
      console.warn('Extension error caught and handled:', { message, source });
      return true; // Prevent default error handling
    }
    
    // Call original handler for non-extension errors
    if (originalErrorHandler) {
      return originalErrorHandler(message, source, lineno, colno, error);
    }
    
    return false;
  };
  
  // Set up unhandled promise rejection handler
  const originalRejectionHandler = window.onunhandledrejection;
  window.onunhandledrejection = (event) => {
    if (isExtensionError(event.reason)) {
      console.warn('Extension promise rejection caught and handled:', event.reason);
      event.preventDefault();
      return;
    }
    
    // Call original handler for non-extension rejections
    if (originalRejectionHandler) {
      originalRejectionHandler(event);
    }
  };
}

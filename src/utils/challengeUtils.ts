// Challenge system utility functions
// Moved from ProfileCard.tsx to fix Fast Refresh compatibility

export const getLevelTitle = (level: number): string => {
  const titles = [
    { threshold: 5, title: 'Novice Warrior' },
    { threshold: 10, title: 'Battle-Tested' },
    { threshold: 15, title: 'Elite Champion' },
    { threshold: 20, title: 'Legendary Gladiator' },
    { threshold: Infinity, title: 'Mythic Overlord' }
  ];
  return titles.find(t => level < t.threshold)?.title || 'Mythic Overlord';
};

export const getXpForNextLevel = (level: number): number => {
  return Math.pow(level, 2) * 100;
};

export const getRankColor = (level: number) => {
  const ranks = [
    { threshold: 5, bg: 'bg-gray-600/20', text: 'text-gray-300', border: 'border-gray-500', glow: 'shadow-gray-500/20' },
    { threshold: 10, bg: 'bg-blue-600/20', text: 'text-blue-400', border: 'border-blue-500', glow: 'shadow-blue-500/20' },
    { threshold: 15, bg: 'bg-emerald-600/20', text: 'text-emerald-400', border: 'border-emerald-500', glow: 'shadow-emerald-500/20' },
    { threshold: 20, bg: 'bg-purple-600/20', text: 'text-purple-400', border: 'border-purple-500', glow: 'shadow-purple-500/20' },
    { threshold: Infinity, bg: 'bg-amber-600/20', text: 'text-amber-400', border: 'border-amber-500', glow: 'shadow-amber-500/20' }
  ];
  return ranks.find(r => level < r.threshold) || ranks[ranks.length - 1];
};

export const calculateWinRate = (wins: number, losses: number): number => {
  const totalGames = wins + losses;
  return totalGames > 0 ? Math.round((wins / totalGames) * 100) : 0;
};

export const getProgressPercentage = (currentXp: number, level: number): number => {
  const nextLevelXp = getXpForNextLevel(level);
  return (currentXp / nextLevelXp) * 100;
};

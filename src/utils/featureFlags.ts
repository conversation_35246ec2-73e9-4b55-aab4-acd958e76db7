/**
 * Feature flags for Grid२Play
 * Used to control the visibility of features during development
 */

export const FEATURE_FLAGS = {
  // Tournament system feature flag
  TOURNAMENTS: import.meta.env.VITE_ENABLE_TOURNAMENTS === 'true',
  
  // Add more feature flags as needed
  // CHALLENGES: import.meta.env.VITE_ENABLE_CHALLENGES === 'true',
  // TEAMS: import.meta.env.VITE_ENABLE_TEAMS === 'true',
} as const;

/**
 * Check if a feature is enabled
 * @param feature - The feature to check
 * @returns boolean indicating if the feature is enabled
 */
export const isFeatureEnabled = (feature: keyof typeof FEATURE_FLAGS): boolean => {
  return FEATURE_FLAGS[feature];
};

/**
 * Hook for conditional rendering based on feature flags
 * @param feature - The feature to check
 * @returns boolean indicating if the feature should be rendered
 */
export const useFeatureFlag = (feature: keyof typeof FEATURE_FLAGS): boolean => {
  return isFeatureEnabled(feature);
};

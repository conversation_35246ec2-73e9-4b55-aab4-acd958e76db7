import { supabase } from '@/integrations/supabase/client';

export interface VenueDiscount {
  id: string;
  discount_type: 'percentage' | 'flat';
  discount_value: number;
  code: string;
  is_public: boolean;
  valid_until: string | null;
}

/**
 * Get the best available discount for a venue
 * Returns the highest value discount that is public and active
 */
export const getVenueDiscount = async (venueId: string): Promise<VenueDiscount | null> => {
  try {
    const { data: coupons, error } = await supabase
      .from('coupons')
      .select('id, discount_type, discount_value, code, is_public, valid_until')
      .eq('venue_id', venueId)
      .eq('is_active', true)
      .eq('is_public', true)
      .or('valid_until.is.null,valid_until.gt.now()')
      .order('discount_value', { ascending: false });

    if (error) {
      console.error('Error fetching venue discount:', error);
      return null;
    }

    if (!coupons || coupons.length === 0) {
      return null;
    }

    // Return the highest value discount
    return coupons[0] as VenueDiscount;
  } catch (error) {
    console.error('Error in getVenueDiscount:', error);
    return null;
  }
};

/**
 * Get discounts for multiple venues at once
 * Returns a map of venue ID to discount
 */
export const getVenueDiscounts = async (venueIds: string[]): Promise<Record<string, VenueDiscount>> => {
  try {
    const { data: coupons, error } = await supabase
      .from('coupons')
      .select('id, discount_type, discount_value, code, is_public, valid_until, venue_id')
      .in('venue_id', venueIds)
      .eq('is_active', true)
      .eq('is_public', true)
      .or('valid_until.is.null,valid_until.gt.now()')
      .order('discount_value', { ascending: false });

    if (error) {
      console.error('Error fetching venue discounts:', error);
      return {};
    }

    if (!coupons || coupons.length === 0) {
      return {};
    }

    // Group by venue_id and take the highest discount for each venue
    const discountMap: Record<string, VenueDiscount> = {};
    
    coupons.forEach((coupon) => {
      if (coupon.venue_id && !discountMap[coupon.venue_id]) {
        discountMap[coupon.venue_id] = {
          id: coupon.id,
          discount_type: coupon.discount_type as 'percentage' | 'flat',
          discount_value: coupon.discount_value,
          code: coupon.code,
          is_public: coupon.is_public,
          valid_until: coupon.valid_until
        };
      }
    });

    return discountMap;
  } catch (error) {
    console.error('Error in getVenueDiscounts:', error);
    return {};
  }
};

/**
 * Format discount for display
 */
export const formatDiscountBadge = (discount: VenueDiscount): string => {
  if (discount.discount_type === 'percentage') {
    return `${discount.discount_value}% OFF`;
  } else {
    return `₹${discount.discount_value} OFF`;
  }
};

/**
 * Get discount badge color classes
 */
export const getDiscountBadgeClasses = (discount: VenueDiscount): string => {
  const baseClasses = "absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-bold shadow-lg backdrop-blur-sm z-10";
  
  if (discount.discount_type === 'percentage') {
    if (discount.discount_value >= 50) {
      return `${baseClasses} bg-red-600/90 text-white border border-red-500/50`;
    } else if (discount.discount_value >= 20) {
      return `${baseClasses} bg-orange-600/90 text-white border border-orange-500/50`;
    } else {
      return `${baseClasses} bg-emerald-600/90 text-white border border-emerald-500/50`;
    }
  } else {
    // Flat discount
    if (discount.discount_value >= 100) {
      return `${baseClasses} bg-red-600/90 text-white border border-red-500/50`;
    } else if (discount.discount_value >= 50) {
      return `${baseClasses} bg-orange-600/90 text-white border border-orange-500/50`;
    } else {
      return `${baseClasses} bg-emerald-600/90 text-white border border-emerald-500/50`;
    }
  }
};

import { useEffect, useCallback, useRef, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';
import { toast } from 'react-hot-toast';

export interface EarningsUpdateEvent {
  type: 'INSERT' | 'UPDATE' | 'DELETE';
  table: 'daily_earnings' | 'bookings' | 'settlements';
  record: any;
  old_record?: any;
  venue_id?: string;
}

export interface UseRealTimeEarningsOptions {
  onEarningsUpdate?: (event: EarningsUpdateEvent) => void;
  onBookingUpdate?: (event: EarningsUpdateEvent) => void;
  onSettlementUpdate?: (event: EarningsUpdateEvent) => void;
  venueIds?: string[];
  showNotifications?: boolean;
  autoRefresh?: boolean;
}

export const useRealTimeEarnings = (options: UseRealTimeEarningsOptions = {}) => {
  const {
    onEarningsUpdate,
    onBookingUpdate,
    onSettlementUpdate,
    venueIds,
    showNotifications = false, // Default to false for earnings to avoid spam
    autoRefresh = true
  } = options;

  const channelRef = useRef<RealtimeChannel | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle daily_earnings table changes
  const handleEarningsChange = useCallback((payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    // Filter by venue if specified
    if (venueIds && venueIds.length > 0 && newRecord?.venue_id) {
      if (!venueIds.includes(newRecord.venue_id)) {
        return; // Skip if not in our venue list
      }
    }

    const event: EarningsUpdateEvent = {
      type: eventType,
      table: 'daily_earnings',
      record: newRecord,
      old_record: oldRecord,
      venue_id: newRecord?.venue_id
    };

    setLastUpdate(new Date());

    if (onEarningsUpdate) {
      onEarningsUpdate(event);
    }

    if (showNotifications) {
      switch (eventType) {
        case 'INSERT':
          toast.success('📊 New earnings data available');
          break;
        case 'UPDATE':
          toast.success('📈 Earnings data updated');
          break;
      }
    }

    if (import.meta.env.DEV) {
      console.log('🔄 Earnings Update:', event);
    }
  }, [onEarningsUpdate, venueIds, showNotifications]);

  // Handle bookings table changes (affects earnings)
  const handleBookingChange = useCallback((payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    // Only process confirmed/cancelled bookings that affect earnings
    const affectsEarnings = (record: any) => {
      return record && ['confirmed', 'completed', 'cancelled'].includes(record.status);
    };

    if (!affectsEarnings(newRecord) && !affectsEarnings(oldRecord)) {
      return;
    }

    const event: EarningsUpdateEvent = {
      type: eventType,
      table: 'bookings',
      record: newRecord,
      old_record: oldRecord,
      venue_id: newRecord?.court?.venue_id || oldRecord?.court?.venue_id
    };

    setLastUpdate(new Date());

    if (onBookingUpdate) {
      onBookingUpdate(event);
    }

    if (showNotifications && eventType === 'INSERT' && newRecord?.status === 'confirmed') {
      toast.success('💰 New booking confirmed - earnings updated');
    }

    if (import.meta.env.DEV) {
      console.log('🔄 Booking Update (affects earnings):', event);
    }
  }, [onBookingUpdate, showNotifications]);

  // Handle settlements table changes
  const handleSettlementChange = useCallback((payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    // Filter by venue if specified
    if (venueIds && venueIds.length > 0 && newRecord?.venue_id) {
      if (!venueIds.includes(newRecord.venue_id)) {
        return;
      }
    }

    const event: EarningsUpdateEvent = {
      type: eventType,
      table: 'settlements',
      record: newRecord,
      old_record: oldRecord,
      venue_id: newRecord?.venue_id
    };

    setLastUpdate(new Date());

    if (onSettlementUpdate) {
      onSettlementUpdate(event);
    }

    if (showNotifications) {
      switch (eventType) {
        case 'INSERT':
          toast.success('💳 New settlement created');
          break;
        case 'UPDATE':
          if (newRecord?.status !== oldRecord?.status) {
            toast.success(`💳 Settlement ${newRecord?.status}`);
          }
          break;
      }
    }

    if (import.meta.env.DEV) {
      console.log('🔄 Settlement Update:', event);
    }
  }, [onSettlementUpdate, venueIds, showNotifications]);

  // Subscribe to real-time updates
  const subscribe = useCallback(() => {
    if (channelRef.current) {
      return; // Already subscribed
    }

    try {
      const channelName = `earnings-updates-${Date.now()}`;
      const channel = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'daily_earnings'
          },
          handleEarningsChange
        )
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'bookings'
          },
          handleBookingChange
        )
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'settlements'
          },
          handleSettlementChange
        );

      channel.subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('✅ Real-time earnings updates subscribed');
          setIsConnected(true);
          
          // Clear any pending reconnect
          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
            reconnectTimeoutRef.current = null;
          }
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Real-time earnings subscription error');
          setIsConnected(false);
          scheduleReconnect();
        } else if (status === 'TIMED_OUT') {
          console.warn('⏰ Real-time earnings subscription timed out');
          setIsConnected(false);
          scheduleReconnect();
        } else if (status === 'CLOSED') {
          console.log('🔌 Real-time earnings subscription closed');
          setIsConnected(false);
        }
      });

      channelRef.current = channel;
    } catch (error) {
      console.error('Error setting up real-time earnings updates:', error);
      setIsConnected(false);
      scheduleReconnect();
    }
  }, [handleEarningsChange, handleBookingChange, handleSettlementChange]);

  // Schedule reconnection with exponential backoff
  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      return; // Already scheduled
    }

    const delay = 3000; // 3 seconds
    reconnectTimeoutRef.current = setTimeout(() => {
      console.log('🔄 Attempting to reconnect earnings updates...');
      unsubscribe();
      subscribe();
    }, delay);
  }, [subscribe]);

  // Unsubscribe from real-time updates
  const unsubscribe = useCallback(() => {
    if (channelRef.current) {
      supabase.removeChannel(channelRef.current);
      channelRef.current = null;
      setIsConnected(false);
      console.log('🔌 Real-time earnings updates unsubscribed');
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  // Manual reconnect function
  const reconnect = useCallback(() => {
    unsubscribe();
    setTimeout(() => {
      subscribe();
    }, 1000);
  }, [unsubscribe, subscribe]);

  // Set up subscription on mount
  useEffect(() => {
    if (autoRefresh) {
      subscribe();
    }

    // Cleanup on unmount
    return () => {
      unsubscribe();
    };
  }, [subscribe, unsubscribe, autoRefresh]);

  return {
    isConnected,
    lastUpdate,
    subscribe,
    unsubscribe,
    reconnect
  };
};

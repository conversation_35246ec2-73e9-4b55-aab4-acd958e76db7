import { useState, useEffect } from 'react';

/**
 * Hook to detect if the current device is mobile
 * Returns true for screens smaller than 768px (md breakpoint)
 */
export function useIsMobile(): boolean {
  // Initialize with correct value to prevent race conditions
  const [isMobile, setIsMobile] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth < 768;
    }
    return false;
  });

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Add event listener for window resize
    window.addEventListener('resize', checkIsMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return isMobile;
}

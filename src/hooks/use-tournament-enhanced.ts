import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { 
  TournamentEnhanced, 
  TournamentParticipant, 
  TournamentOrganizer,
  TournamentCategory,
  PlayerTournamentStats,
  TournamentMatchEnhanced,
  TournamentFilters,
  CreateTournamentForm,
  TournamentRegistrationForm
} from '@/types/tournament-enhanced';

// Fetch enhanced tournaments with advanced filtering
export function useEnhancedTournaments(filters?: TournamentFilters) {
  return useQuery<TournamentEnhanced[], Error>({
    queryKey: ['tournaments-enhanced', filters],
    queryFn: async () => {
      let query = supabase
        .from('tournaments_enhanced')
        .select(`
          *,
          sports(name),
          venues(name, location),
          tournament_organizers(organization_name),
          tournament_categories(name)
        `);

      // Apply filters
      if (filters?.status?.length) {
        query = query.in('status', filters.status);
      }
      
      if (filters?.sport_id) {
        query = query.eq('sport_id', filters.sport_id);
      }
      
      if (filters?.venue_id) {
        query = query.eq('venue_id', filters.venue_id);
      }
      
      if (filters?.tournament_type?.length) {
        query = query.in('tournament_type', filters.tournament_type);
      }
      
      if (filters?.format_type?.length) {
        query = query.in('format_type', filters.format_type);
      }
      
      if (filters?.featured_only) {
        query = query.eq('featured', true);
      }
      
      if (filters?.search_query) {
        query = query.or(`name.ilike.%${filters.search_query}%,description.ilike.%${filters.search_query}%`);
      }
      
      // Default ordering
      query = query.order('featured', { ascending: false })
                  .order('tournament_start_date', { ascending: true });

      const { data, error } = await query;
      
      if (error) throw error;
      
      return (data || []).map((t: any) => ({
        ...t,
        sport_name: t.sports?.name,
        venue_name: t.venues?.name,
        venue_location: t.venues?.location,
        organizer_name: t.tournament_organizers?.organization_name,
        category_name: t.tournament_categories?.name,
      }));
    }
  });
}

// Fetch tournament details by slug
export function useEnhancedTournamentDetails(slug?: string) {
  return useQuery<TournamentEnhanced | null, Error>({
    queryKey: ['tournament-enhanced', slug],
    queryFn: async () => {
      if (!slug) return null;
      
      const { data, error } = await supabase
        .from('tournaments_enhanced')
        .select(`
          *,
          sports(name),
          venues(name, location, contact_number, amenities),
          tournament_organizers(organization_name, contact_person_name, contact_email),
          tournament_categories(name, description)
        `)
        .eq('slug', slug)
        .maybeSingle();

      if (error) throw error;
      if (!data) return null;

      return {
        ...data,
        sport_name: data.sports?.name,
        venue_name: data.venues?.name,
        venue_location: data.venues?.location,
        organizer_name: data.tournament_organizers?.organization_name,
        category_name: data.tournament_categories?.name,
      } as TournamentEnhanced;
    },
    enabled: !!slug,
  });
}

// Fetch tournament participants
export function useTournamentParticipants(tournamentId?: string) {
  return useQuery<TournamentParticipant[], Error>({
    queryKey: ['tournament-participants', tournamentId],
    queryFn: async () => {
      if (!tournamentId) return [];
      
      const { data, error } = await supabase
        .from('tournament_participants')
        .select(`
          *,
          profiles(full_name, email)
        `)
        .eq('tournament_id', tournamentId)
        .order('seed_number', { ascending: true, nullsLast: true })
        .order('registration_date', { ascending: true });

      if (error) throw error;
      
      return (data || []).map((p: any) => ({
        ...p,
        user_name: p.profiles?.full_name,
        user_email: p.profiles?.email,
      }));
    },
    enabled: !!tournamentId,
  });
}

// Fetch tournament matches
export function useTournamentMatches(tournamentId?: string) {
  return useQuery<TournamentMatchEnhanced[], Error>({
    queryKey: ['tournament-matches', tournamentId],
    queryFn: async () => {
      if (!tournamentId) return [];
      
      const { data, error } = await supabase
        .from('tournament_matches_enhanced')
        .select(`
          *,
          participant_a:participant_a_id(team_name),
          participant_b:participant_b_id(team_name),
          venues(name)
        `)
        .eq('tournament_id', tournamentId)
        .order('round_number', { ascending: true })
        .order('match_number', { ascending: true });

      if (error) throw error;
      
      return (data || []).map((m: any) => ({
        ...m,
        participant_a_name: m.participant_a?.team_name,
        participant_b_name: m.participant_b?.team_name,
        venue_name: m.venues?.name,
      }));
    },
    enabled: !!tournamentId,
  });
}

// Fetch player tournament statistics
export function usePlayerTournamentStats(userId?: string, sportId?: string) {
  return useQuery<PlayerTournamentStats | null, Error>({
    queryKey: ['player-tournament-stats', userId, sportId],
    queryFn: async () => {
      if (!userId || !sportId) return null;
      
      const { data, error } = await supabase
        .from('player_tournament_stats')
        .select(`
          *,
          sports(name)
        `)
        .eq('user_id', userId)
        .eq('sport_id', sportId)
        .maybeSingle();

      if (error) throw error;
      if (!data) return null;

      return {
        ...data,
        sport_name: data.sports?.name,
      } as PlayerTournamentStats;
    },
    enabled: !!userId && !!sportId,
  });
}

// Fetch tournament organizers
export function useTournamentOrganizers() {
  return useQuery<TournamentOrganizer[], Error>({
    queryKey: ['tournament-organizers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournament_organizers')
        .select('*')
        .eq('verification_status', 'verified')
        .eq('is_active', true)
        .order('organization_name', { ascending: true });

      if (error) throw error;
      return data || [];
    }
  });
}

// Fetch tournament categories
export function useTournamentCategories() {
  return useQuery<TournamentCategory[], Error>({
    queryKey: ['tournament-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournament_categories')
        .select('*')
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) throw error;
      return data || [];
    }
  });
}

// Create tournament mutation
export function useCreateTournament() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (tournamentData: CreateTournamentForm) => {
      // First, create or get organizer
      let organizerId = null;
      
      // For now, we'll create a basic organizer entry
      // In production, this would be handled through a proper organizer registration flow
      const { data: organizer, error: organizerError } = await supabase
        .from('tournament_organizers')
        .insert({
          organization_name: tournamentData.organizer_name || 'Individual Organizer',
          organization_type: 'club',
          contact_person_name: tournamentData.organizer_name || 'Organizer',
          contact_email: tournamentData.contact_info || '<EMAIL>',
          contact_phone: '1234567890',
          verification_status: 'pending',
          partnership_tier: 'basic',
          user_id: (await supabase.auth.getUser()).data.user?.id,
        })
        .select()
        .single();
      
      if (organizerError) throw organizerError;
      organizerId = organizer.id;
      
      // Create the tournament
      const { data, error } = await supabase
        .from('tournaments_enhanced')
        .insert({
          name: tournamentData.tournament_name,
          description: tournamentData.description,
          sport_id: tournamentData.sport_id,
          venue_id: tournamentData.venue_id,
          organizer_id: organizerId,
          category_id: tournamentData.category_id,
          tournament_type: tournamentData.tournament_type,
          format_type: tournamentData.format_type,
          max_participants: tournamentData.max_participants,
          min_participants: tournamentData.min_participants,
          team_size: tournamentData.team_size,
          registration_start_date: tournamentData.registration_start_date,
          registration_end_date: tournamentData.registration_end_date,
          tournament_start_date: tournamentData.tournament_start_date,
          tournament_end_date: tournamentData.tournament_end_date,
          entry_fee: tournamentData.entry_fee,
          prize_pool: tournamentData.prize_pool,
          rules: tournamentData.rules,
          age_restrictions: tournamentData.age_restrictions,
          skill_requirements: tournamentData.skill_requirements,
          equipment_provided: tournamentData.equipment_provided,
          live_streaming: tournamentData.live_streaming,
          public_viewing: tournamentData.public_viewing,
          visibility: tournamentData.visibility,
          status: 'published', // Auto-publish for now
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournaments-enhanced'] });
    },
  });
}

// Register for tournament mutation
export function useRegisterForTournament() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      tournamentId, 
      registrationData 
    }: { 
      tournamentId: string; 
      registrationData: TournamentRegistrationForm 
    }) => {
      const user = (await supabase.auth.getUser()).data.user;
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('tournament_participants')
        .insert({
          tournament_id: tournamentId,
          user_id: user.id,
          team_name: registrationData.team_name,
          player_count: registrationData.player_count,
          player_skill_level: registrationData.player_skill_level,
          notes: registrationData.notes,
          team_members: registrationData.team_members || [],
          status: 'registered',
          payment_status: 'pending',
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: ['tournament-participants', variables.tournamentId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['tournaments-enhanced'] 
      });
    },
  });
}

// Check if user is registered for tournament
export function useIsUserRegistered(tournamentId?: string, userId?: string) {
  return useQuery<boolean, Error>({
    queryKey: ['user-tournament-registration', tournamentId, userId],
    queryFn: async () => {
      if (!tournamentId || !userId) return false;
      
      const { data, error } = await supabase
        .from('tournament_participants')
        .select('id')
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .maybeSingle();

      if (error) throw error;
      return !!data;
    },
    enabled: !!tournamentId && !!userId,
  });
}

import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface AccountActivationStatus {
  isActivated: boolean;
  hasCheckedActivation: boolean;
  isLoading: boolean;
}

// Custom event for account activation updates
const ACCOUNT_ACTIVATION_EVENT = 'accountActivationUpdated';

export const useAccountActivation = () => {
  const { user } = useAuth();
  const [activationStatus, setActivationStatus] = useState<AccountActivationStatus>({
    isActivated: false,
    hasCheckedActivation: false,
    isLoading: false
  });

  const checkAccountActivation = useCallback(async () => {
    if (!user) return;

    setActivationStatus(prev => ({ ...prev, isLoading: true }));

    try {
      // Check account activation status using the new table
      const { data: activationData, error } = await (supabase as any)
        .from('account_activation')
        .select('is_activated, legal_agreements_accepted')
        .eq('user_id', user.id)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      const isActivated = (activationData as any)?.is_activated || false;

      console.log('useAccountActivation: Checked activation status:', { 
        userId: user.id, 
        isActivated, 
        activationData 
      });

      setActivationStatus({
        isActivated,
        hasCheckedActivation: true,
        isLoading: false
      });

      return isActivated;
    } catch (error) {
      console.error('Error checking account activation status:', error);
      // Set as checked even if there's an error to prevent infinite loops
      setActivationStatus({
        isActivated: false,
        hasCheckedActivation: true,
        isLoading: false
      });
      return false;
    }
  }, [user]);

  // Listen for account activation events
  useEffect(() => {
    const handleActivationUpdate = () => {
      console.log('useAccountActivation: Received activation update event');
      checkAccountActivation();
    };

    window.addEventListener(ACCOUNT_ACTIVATION_EVENT, handleActivationUpdate);
    return () => {
      window.removeEventListener(ACCOUNT_ACTIVATION_EVENT, handleActivationUpdate);
    };
  }, [checkAccountActivation]);

  // Function to trigger activation update event
  const triggerActivationUpdate = useCallback(() => {
    console.log('useAccountActivation: Triggering activation update event');
    window.dispatchEvent(new CustomEvent(ACCOUNT_ACTIVATION_EVENT));
  }, []);

  return {
    activationStatus,
    checkAccountActivation,
    triggerActivationUpdate
  };
};

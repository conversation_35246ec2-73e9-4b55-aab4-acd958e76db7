
import { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Session, User } from '@supabase/supabase-js';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/components/ui/use-toast';
import { logAuth } from '@/utils/logger';
import { safeNavigate, extensionSafeAsync, isExtensionError } from '@/utils/extensionSafeNavigation';

interface AuthContextProps {
  user: User | null;
  session: Session | null;
  loading: boolean;
  userRole: 'user' | 'admin' | 'super_admin' | null;
  signUp: (email: string, password: string, userData: any) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  isSessionExpired: boolean;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState<'user' | 'admin' | 'super_admin' | null>(null);
  const [isSessionExpired, setIsSessionExpired] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, currentSession) => {
        logAuth.stateChange(event, !!currentSession);
        
        setSession(currentSession);
        setUser(currentSession?.user ?? null);
        
        if (event === 'SIGNED_IN') {
          setIsSessionExpired(false);
          // Fetch user role after sign in
          if (currentSession?.user) {
            fetchUserRole(currentSession.user.id);
          }
        } else if (event === 'SIGNED_OUT') {
          logAuth.signOut();
          setUserRole(null);
          setUser(null);
          setSession(null);
          setIsSessionExpired(false);
          // Only navigate to login if we're not already on a public page
          const currentPath = window.location.pathname;
          if (!['/login', '/register', '/reset-password', '/verify-email', '/', '/venues', '/sports', '/faq', '/help', '/contact', '/privacy', '/terms', '/cancellation-refunds'].includes(currentPath)) {
            safeNavigate(navigate, '/login');
          }
        } else if (event === 'TOKEN_REFRESHED') {
          setIsSessionExpired(false);
        } else if (event === 'USER_UPDATED') {
          setUser(currentSession?.user ?? null);
        }
      }
    );

    // THEN check for existing session
    supabase.auth.getSession().then(({ data: { session: currentSession } }) => {
      logAuth.sessionCheck(!!currentSession);
      setSession(currentSession);
      setUser(currentSession?.user ?? null);
      
      // Fetch user role for existing session
      if (currentSession?.user) {
        fetchUserRole(currentSession.user.id);
      }
      
      setLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [navigate]); // Remove session dependency to prevent cycling

  // Separate useEffect for session expiration check
  useEffect(() => {
    if (!session?.expires_at) return;

    const checkSessionExpiration = () => {
      const expiresAt = session.expires_at;
      if (expiresAt) {
        const now = Math.floor(Date.now() / 1000);
        if (expiresAt < now) {
          setIsSessionExpired(true);
        }
      }
    };

    const interval = setInterval(checkSessionExpiration, 60000); // Check every minute

    return () => {
      clearInterval(interval);
    };
  }, [session?.expires_at]);

  const fetchUserRole = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', userId);
        
      if (error) {
        console.error('Error fetching user role:', error);
        return;
      }
      
      // Find the highest role (super_admin > admin > user)
      if (data && data.length > 0) {
        if (data.some(r => r.role === 'super_admin')) {
          setUserRole('super_admin');
        } else if (data.some(r => r.role === 'admin')) {
          setUserRole('admin');
        } else {
          setUserRole('user');
        }
      } else {
        setUserRole('user'); // Default role
      }
    } catch (error) {
      console.error('Error in fetchUserRole:', error);
    }
  };

  const signUp = async (email: string, password: string, userData: any) => {
    try {
      const { error } = await supabase.auth.signUp({ 
        email, 
        password,
        options: {
          data: {
            name: userData.name,
            phone: userData.phone
          }
        }
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({ email, password });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  const signOut = async () => {
    await extensionSafeAsync(
      async () => {
        logAuth.signOut();
        const { error } = await supabase.auth.signOut();
        if (error) {
          console.error('Sign out error:', error);
          throw error;
        }

        // Clear local state immediately
        setUser(null);
        setSession(null);
        setUserRole(null);
        setIsSessionExpired(false);

        toast({
          title: "Signed out successfully",
          description: "You have been logged out.",
        });
      },
      async () => {
        // Fallback: Force clear local state and redirect
        console.warn('Using fallback sign-out method due to extension interference');

        // Clear local state
        setUser(null);
        setSession(null);
        setUserRole(null);
        setIsSessionExpired(false);

        // Clear local storage
        try {
          localStorage.removeItem('supabase.auth.token');
          sessionStorage.clear();
        } catch (storageError) {
          console.warn('Could not clear storage:', storageError);
        }

        toast({
          title: "Signed out successfully",
          description: "You have been logged out (fallback method used).",
        });

        // Force navigation to home page
        window.location.href = '/';
      },
      'sign-out operation'
    ).catch((error: any) => {
      // Final error handling
      if (isExtensionError(error)) {
        console.warn('Extension interference prevented sign-out, forcing page reload');
        // Clear what we can and reload
        try {
          setUser(null);
          setSession(null);
          setUserRole(null);
          setIsSessionExpired(false);
        } catch (stateError) {
          console.warn('Could not clear state:', stateError);
        }

        // Force reload to clear everything
        window.location.href = '/';
      } else {
        console.error('Sign out failed:', error);
        toast({
          title: "Sign out failed",
          description: "There was an error signing you out. Please try again.",
          variant: "destructive",
        });
      }
    });
  };
  
  const logout = async () => {
    await signOut();
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      session, 
      loading, 
      userRole, 
      signUp, 
      signIn, 
      signOut,
      isSessionExpired,
      logout
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

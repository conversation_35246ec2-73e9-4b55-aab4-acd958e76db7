import { supabase } from '@/integrations/supabase/client';
import { Address } from '@/types/location';

export interface UserLocationPreference {
  name: string;
  latitude: number;
  longitude: number;
  setAt: string;
  detectionEnabled: boolean;
}

export class LocationPreferenceService {
  /**
   * Save user's location preference to their profile
   */
  async saveLocationPreference(
    userId: string, 
    location: Address
  ): Promise<UserLocationPreference | null> {
    if (!location.coordinates) {
      throw new Error('Location coordinates are required');
    }

    const preference: UserLocationPreference = {
      name: location.area || location.city || location.display_name,
      latitude: location.coordinates.latitude,
      longitude: location.coordinates.longitude,
      setAt: new Date().toISOString(),
      detectionEnabled: true
    };

    const { error } = await supabase
      .from('profiles')
      .update({
        preferred_location_name: preference.name,
        preferred_location_latitude: preference.latitude,
        preferred_location_longitude: preference.longitude,
        location_preference_set_at: preference.setAt
      })
      .eq('id', userId);

    if (error) {
      console.error('Error saving location preference:', error);
      throw error;
    }

    if (import.meta.env.DEV) {
      console.log('Location preference saved:', preference);
    }

    return preference;
  }

  /**
   * Get user's saved location preference
   */
  async getLocationPreference(userId: string): Promise<UserLocationPreference | null> {
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        preferred_location_name,
        preferred_location_latitude,
        preferred_location_longitude,
        location_preference_set_at,
        location_detection_enabled
      `)
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching location preference:', error);
      return null;
    }

    if (!data?.preferred_location_name) {
      return null;
    }

    return {
      name: data.preferred_location_name,
      latitude: data.preferred_location_latitude,
      longitude: data.preferred_location_longitude,
      setAt: data.location_preference_set_at,
      detectionEnabled: data.location_detection_enabled ?? true
    };
  }

  /**
   * Clear user's location preference
   */
  async clearLocationPreference(userId: string): Promise<void> {
    const { error } = await supabase
      .from('profiles')
      .update({
        preferred_location_name: null,
        preferred_location_latitude: null,
        preferred_location_longitude: null,
        location_preference_set_at: null
      })
      .eq('id', userId);

    if (error) {
      console.error('Error clearing location preference:', error);
      throw error;
    }

    if (import.meta.env.DEV) {
      console.log('Location preference cleared for user:', userId);
    }
  }

  /**
   * Update location detection settings
   */
  async updateDetectionSettings(userId: string, enabled: boolean): Promise<void> {
    const { error } = await supabase
      .from('profiles')
      .update({ location_detection_enabled: enabled })
      .eq('id', userId);

    if (error) {
      console.error('Error updating detection settings:', error);
      throw error;
    }

    if (import.meta.env.DEV) {
      console.log('Location detection settings updated:', { userId, enabled });
    }
  }

  /**
   * Save location preference from Google Maps Places result
   */
  async saveLocationFromGoogleMaps(
    userId: string,
    locationName: string,
    coordinates: { latitude: number; longitude: number }
  ): Promise<UserLocationPreference> {
    const preference: UserLocationPreference = {
      name: locationName,
      latitude: coordinates.latitude,
      longitude: coordinates.longitude,
      setAt: new Date().toISOString(),
      detectionEnabled: true
    };

    const { error } = await supabase
      .from('profiles')
      .update({
        preferred_location_name: preference.name,
        preferred_location_latitude: preference.latitude,
        preferred_location_longitude: preference.longitude,
        location_preference_set_at: preference.setAt
      })
      .eq('id', userId);

    if (error) {
      console.error('Error saving Google Maps location preference:', error);
      throw error;
    }

    if (import.meta.env.DEV) {
      console.log('Google Maps location preference saved:', preference);
    }

    return preference;
  }
}

export const locationPreferenceService = new LocationPreferenceService();

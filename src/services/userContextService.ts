import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';

export interface UserProfile {
  id: string;
  full_name?: string;
  phone?: string;
  email?: string;
  role?: string;
  created_at?: string;
  last_login?: string;
}

export interface RecentBooking {
  id: string;
  venue_name: string;
  court_name: string;
  sport_name: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  status: string;
  total_amount: number;
  created_at: string;
}

export interface HelpRequestSummary {
  id: string;
  ticket_number: string;
  subject: string;
  status: string;
  category: string;
  created_at: string;
  last_message_at: string;
}

export interface PageContext {
  path: string;
  page_type: 'home' | 'venues' | 'venue_details' | 'booking' | 'profile' | 'bookings' | 'other';
  venue_id?: string;
  venue_name?: string;
  sport_filter?: string;
  location_filter?: string;
}

export interface SessionContext {
  session_id: string;
  user_agent: string;
  device_type: 'mobile' | 'tablet' | 'desktop';
  browser: string;
  location?: {
    city?: string;
    state?: string;
    country?: string;
  };
  referrer?: string;
  session_duration: number;
}

export interface ComprehensiveUserContext {
  user: UserProfile;
  recentBookings: RecentBooking[];
  helpRequestHistory: HelpRequestSummary[];
  pageContext: PageContext;
  sessionContext: SessionContext;
  preferences: {
    preferred_sports?: string[];
    preferred_locations?: string[];
    notification_preferences?: any;
  };
  supportMetadata: {
    total_bookings: number;
    total_help_requests: number;
    last_support_interaction?: string;
    common_issues?: string[];
    satisfaction_rating?: number;
  };
}

class UserContextService {
  /**
   * Get comprehensive user context for support
   */
  async getUserContext(user: User): Promise<ComprehensiveUserContext> {
    try {
      const [
        userProfile,
        recentBookings,
        helpRequestHistory,
        supportMetadata
      ] = await Promise.allSettled([
        this.getUserProfile(user),
        this.getRecentBookings(user.id),
        this.getHelpRequestHistory(user.id),
        this.getSupportMetadata(user.id)
      ]);

      return {
        user: userProfile.status === 'fulfilled' ? userProfile.value : this.getFallbackUserProfile(user),
        recentBookings: recentBookings.status === 'fulfilled' ? recentBookings.value : [],
        helpRequestHistory: helpRequestHistory.status === 'fulfilled' ? helpRequestHistory.value : [],
        pageContext: this.getPageContext(),
        sessionContext: this.getSessionContext(),
        preferences: await this.getUserPreferences(user.id).catch(() => ({})),
        supportMetadata: supportMetadata.status === 'fulfilled' ? supportMetadata.value : {
          total_bookings: 0,
          total_help_requests: 0
        }
      };
    } catch (error) {
      console.error('Error getting user context:', error);
      // Return minimal context to prevent widget from breaking
      return {
        user: this.getFallbackUserProfile(user),
        recentBookings: [],
        helpRequestHistory: [],
        pageContext: this.getPageContext(),
        sessionContext: this.getSessionContext(),
        preferences: {},
        supportMetadata: {
          total_bookings: 0,
          total_help_requests: 0
        }
      };
    }
  }

  /**
   * Get fallback user profile when database fetch fails
   */
  private getFallbackUserProfile(user: User): UserProfile {
    return {
      id: user.id,
      full_name: user.user_metadata?.full_name,
      phone: user.phone,
      email: user.email,
      role: user.user_metadata?.role || 'user',
      created_at: user.created_at,
      last_login: user.last_sign_in_at
    };
  }

  /**
   * Get user profile information
   */
  private async getUserProfile(user: User): Promise<UserProfile> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, phone, created_at')
        .eq('id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        console.error('Error fetching user profile:', error);
      }

      return {
        id: user.id,
        full_name: data?.full_name || user.user_metadata?.full_name,
        phone: data?.phone || user.phone,
        email: user.email,
        role: user.user_metadata?.role || 'user',
        created_at: data?.created_at || user.created_at,
        last_login: user.last_sign_in_at
      };
    } catch (error) {
      console.error('Error in getUserProfile:', error);
      return {
        id: user.id,
        email: user.email,
        role: 'user'
      };
    }
  }

  /**
   * Get recent bookings (last 10)
   */
  private async getRecentBookings(userId: string): Promise<RecentBooking[]> {
    try {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          start_time,
          end_time,
          status,
          total_price,
          created_at,
          court:courts(
            name,
            venue:venues(name),
            sport:sports(name)
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Error fetching recent bookings:', error);
        return [];
      }

      return (data || []).map(booking => ({
        id: booking.id,
        venue_name: booking.court?.venue?.name || 'Unknown Venue',
        court_name: booking.court?.name || 'Unknown Court',
        sport_name: booking.court?.sport?.name || 'Unknown Sport',
        booking_date: booking.booking_date,
        start_time: booking.start_time,
        end_time: booking.end_time,
        status: booking.status,
        total_amount: Number(booking.total_price) || 0, // Convert total_price to total_amount
        created_at: booking.created_at
      }));
    } catch (error) {
      console.error('Error fetching recent bookings:', error);
      // Return empty array on error to prevent widget from breaking
      return [];
    }
  }

  /**
   * Get help request history (last 5)
   */
  private async getHelpRequestHistory(userId: string): Promise<HelpRequestSummary[]> {
    try {
      const { data, error } = await supabase
        .from('help_requests')
        .select('id, ticket_number, subject, status, category, created_at, last_message_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error fetching help request history:', error);
      return [];
    }
  }

  /**
   * Get current page context
   */
  private getPageContext(): PageContext {
    const path = window.location.pathname;
    const searchParams = new URLSearchParams(window.location.search);
    
    let pageType: PageContext['page_type'] = 'other';
    let venueId: string | undefined;
    let venueName: string | undefined;

    // Determine page type and extract relevant data
    if (path === '/') {
      pageType = 'home';
    } else if (path === '/venues') {
      pageType = 'venues';
    } else if (path.startsWith('/venues/')) {
      pageType = 'venue_details';
      venueId = path.split('/')[2];
      // Try to get venue name from page title or DOM
      const titleElement = document.querySelector('h1');
      venueName = titleElement?.textContent || undefined;
    } else if (path === '/bookings') {
      pageType = 'bookings';
    } else if (path === '/profile') {
      pageType = 'profile';
    } else if (path.includes('/booking')) {
      pageType = 'booking';
    }

    return {
      path,
      page_type: pageType,
      venue_id: venueId,
      venue_name: venueName,
      sport_filter: searchParams.get('sport') || undefined,
      location_filter: searchParams.get('location') || undefined
    };
  }

  /**
   * Get session context
   */
  private getSessionContext(): SessionContext {
    const userAgent = navigator.userAgent;
    const sessionStart = sessionStorage.getItem('session_start');
    const sessionId = sessionStorage.getItem('session_id') || `session_${Date.now()}`;
    
    // Store session ID if not exists
    if (!sessionStorage.getItem('session_id')) {
      sessionStorage.setItem('session_id', sessionId);
    }
    
    // Store session start time if not exists
    if (!sessionStart) {
      sessionStorage.setItem('session_start', Date.now().toString());
    }

    const sessionDuration = sessionStart 
      ? Date.now() - parseInt(sessionStart)
      : 0;

    return {
      session_id: sessionId,
      user_agent: userAgent,
      device_type: this.getDeviceType(),
      browser: this.getBrowser(),
      referrer: document.referrer || undefined,
      session_duration: sessionDuration
    };
  }

  /**
   * Get user preferences
   */
  private async getUserPreferences(userId: string): Promise<any> {
    try {
      // This could be extended to fetch from a user_preferences table
      const preferences = localStorage.getItem(`user_preferences_${userId}`);
      return preferences ? JSON.parse(preferences) : {};
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return {};
    }
  }

  /**
   * Get support metadata
   */
  private async getSupportMetadata(userId: string): Promise<any> {
    try {
      const [bookingCount, helpRequestCount] = await Promise.all([
        this.getTotalBookings(userId),
        this.getTotalHelpRequests(userId)
      ]);

      return {
        total_bookings: bookingCount,
        total_help_requests: helpRequestCount,
        last_support_interaction: await this.getLastSupportInteraction(userId)
      };
    } catch (error) {
      console.error('Error getting support metadata:', error);
      return {
        total_bookings: 0,
        total_help_requests: 0
      };
    }
  }

  /**
   * Helper methods
   */
  private async getTotalBookings(userId: string): Promise<number> {
    const { count, error } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);
    
    return error ? 0 : (count || 0);
  }

  private async getTotalHelpRequests(userId: string): Promise<number> {
    const { count, error } = await supabase
      .from('help_requests')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);
    
    return error ? 0 : (count || 0);
  }

  private async getLastSupportInteraction(userId: string): Promise<string | undefined> {
    const { data, error } = await supabase
      .from('help_requests')
      .select('last_message_at')
      .eq('user_id', userId)
      .order('last_message_at', { ascending: false })
      .limit(1)
      .single();

    return error ? undefined : data?.last_message_at;
  }

  private getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
    const width = window.innerWidth;
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  }

  private getBrowser(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  /**
   * Format context for MSG91 integration
   * Enhanced with security and privacy considerations
   */
  formatForMSG91(context: ComprehensiveUserContext, userConsent: boolean = true): any {
    try {
      // If no consent, provide minimal data only
      if (!userConsent) {
        return {
          unique_id: this.hashUserId(context.user.id || `guest_${Date.now()}`),
          name: 'Grid२Play User',
          country: 'India'
        };
      }

      // With consent, still minimize data exposure
      const contextSummary = this.createMinimalContextSummary(context);

      return {
        // Hashed user ID for privacy
        unique_id: this.hashUserId(context.user.id || `guest_${Date.now()}`),
        // First name only for personalization
        name: this.sanitizeName(context.user.full_name) || 'Grid२Play User',
        // Phone only if explicitly provided
        number: context.user.phone || '',
        // Email domain masked for privacy
        mail: this.maskEmail(context.user.email) || '',
        country: 'India',

        // Minimal context data
        user_type: context.supportMetadata.total_bookings > 0 ? 'returning' : 'new',
        support_level: this.getSupportLevel(context.supportMetadata.total_help_requests),
        current_section: this.sanitizePageType(context.pageContext.page_type),
        device_type: context.sessionContext.device_type || 'unknown',
        context_summary: contextSummary
      };
    } catch (error) {
      console.error('Error formatting MSG91 context:', error);
      // Return minimal safe configuration
      return {
        unique_id: this.hashUserId(context.user.id || `guest_${Date.now()}`),
        name: 'Grid२Play User',
        country: 'India'
      };
    }
  }

  /**
   * Hash user ID for privacy protection
   */
  private hashUserId(userId: string): string {
    try {
      // Simple hash for privacy (not cryptographically secure, but privacy-preserving)
      return btoa(userId).substring(0, 16);
    } catch {
      return `user_${Date.now().toString(36)}`;
    }
  }

  /**
   * Sanitize user name to first name only
   */
  private sanitizeName(fullName?: string): string {
    if (!fullName) return '';
    const firstName = fullName.split(' ')[0];
    // Remove any potentially dangerous characters
    return firstName.replace(/[<>\"'&]/g, '').trim().substring(0, 20);
  }

  /**
   * Mask email for privacy
   */
  private maskEmail(email?: string): string {
    if (!email) return '';
    const [username, domain] = email.split('@');
    if (!domain) return '';

    const maskedUsername = username.length > 2
      ? username.substring(0, 2) + '*'.repeat(username.length - 2)
      : username;

    return `${maskedUsername}@${domain}`;
  }

  /**
   * Get support level without exposing exact numbers
   */
  private getSupportLevel(helpRequests: number): string {
    if (helpRequests === 0) return 'new';
    if (helpRequests <= 2) return 'occasional';
    if (helpRequests <= 5) return 'regular';
    return 'frequent';
  }

  /**
   * Sanitize page type for security
   */
  private sanitizePageType(pageType: string): string {
    const allowedTypes = ['home', 'venues', 'venue_details', 'booking', 'profile', 'bookings'];
    return allowedTypes.includes(pageType) ? pageType : 'other';
  }

  /**
   * Create a minimal, privacy-safe context summary for support agents
   */
  private createMinimalContextSummary(context: ComprehensiveUserContext): string {
    const parts: string[] = [];

    // User type (no specific role exposure)
    if (context.user.role && context.user.role !== 'user') {
      parts.push('Premium user');
    }

    // Activity level (no exact numbers)
    if (context.supportMetadata.total_bookings > 10) {
      parts.push('Active user');
    } else if (context.supportMetadata.total_bookings > 0) {
      parts.push('Returning user');
    } else {
      parts.push('New user');
    }

    // Recent activity (sport only, no venue names)
    if (context.recentBookings.length > 0) {
      const recent = context.recentBookings[0];
      const sportName = recent.sport_name?.replace(/[<>\"'&]/g, '') || 'Unknown';
      parts.push(`Plays: ${sportName}`);
    }

    // Current context (page type only)
    if (context.pageContext.page_type !== 'home') {
      parts.push(`Section: ${this.sanitizePageType(context.pageContext.page_type)}`);
    }

    // Device info
    parts.push(`Device: ${context.sessionContext.device_type}`);

    return parts.join(' | ') || 'New user';
  }
}

// Export singleton instance
export const userContextService = new UserContextService();
export default userContextService;

import { supabase } from '@/integrations/supabase/client';

export interface FAQ {
  id: number;
  question: string;
  answer: string;
  tags: string[];
  is_active: boolean;
  created_at?: string;
}

export interface VenueFAQ {
  id: string;
  venue_id: string;
  question: string;
  answer: string;
  category?: string;
  is_active: boolean;
  order_index?: number;
  created_at: string;
  updated_at: string;
}

export interface FAQSearchResult {
  faq: FAQ | VenueFAQ;
  relevanceScore: number;
  matchedTerms: string[];
  type: 'general' | 'venue';
}

class FAQService {
  private generalFAQs: FAQ[] = [];
  private venueFAQs: VenueFAQ[] = [];
  private lastFetchTime = 0;
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  /**
   * Load all FAQs from database with caching
   */
  async loadFAQs(forceRefresh = false): Promise<void> {
    const now = Date.now();
    if (!forceRefresh && now - this.lastFetchTime < this.cacheTimeout) {
      return; // Use cached data
    }

    try {
      // Load general FAQs
      const { data: generalData, error: generalError } = await supabase
        .from('faqs')
        .select('*')
        .eq('is_active', true)
        .order('id');

      if (generalError) throw generalError;
      this.generalFAQs = generalData || [];

      // Load venue FAQs
      const { data: venueData, error: venueError } = await supabase
        .from('venue_faqs')
        .select('*')
        .eq('is_active', true)
        .order('order_index')
        .order('created_at');

      if (venueError) throw venueError;
      this.venueFAQs = venueData || [];

      this.lastFetchTime = now;
    } catch (error) {
      console.error('Error loading FAQs:', error);
      throw error;
    }
  }

  /**
   * Get all general FAQs
   */
  async getGeneralFAQs(): Promise<FAQ[]> {
    await this.loadFAQs();
    return this.generalFAQs;
  }

  /**
   * Get venue-specific FAQs
   */
  async getVenueFAQs(venueId?: string): Promise<VenueFAQ[]> {
    await this.loadFAQs();
    
    if (venueId) {
      return this.venueFAQs.filter(faq => faq.venue_id === venueId);
    }
    
    return this.venueFAQs;
  }

  /**
   * Search FAQs with relevance scoring
   */
  async searchFAQs(query: string, venueId?: string): Promise<FAQSearchResult[]> {
    if (!query.trim()) return [];

    await this.loadFAQs();
    
    const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 2);
    const results: FAQSearchResult[] = [];

    // Search general FAQs
    for (const faq of this.generalFAQs) {
      const score = this.calculateRelevanceScore(faq, searchTerms);
      if (score > 0) {
        results.push({
          faq,
          relevanceScore: score,
          matchedTerms: this.getMatchedTerms(faq, searchTerms),
          type: 'general'
        });
      }
    }

    // Search venue FAQs (prioritize venue-specific if venueId provided)
    const venueFAQsToSearch = venueId 
      ? this.venueFAQs.filter(faq => faq.venue_id === venueId)
      : this.venueFAQs;

    for (const faq of venueFAQsToSearch) {
      const score = this.calculateVenueRelevanceScore(faq, searchTerms);
      if (score > 0) {
        results.push({
          faq,
          relevanceScore: venueId ? score * 1.5 : score, // Boost venue-specific results
          matchedTerms: this.getVenueMatchedTerms(faq, searchTerms),
          type: 'venue'
        });
      }
    }

    // Sort by relevance score (highest first)
    return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * Get FAQ suggestions based on common categories
   */
  async getFAQSuggestions(category?: string): Promise<FAQ[]> {
    await this.loadFAQs();
    
    if (!category) {
      // Return top FAQs from each category
      return this.generalFAQs.slice(0, 6);
    }

    // Filter by category using tags
    return this.generalFAQs.filter(faq => 
      faq.tags.some(tag => tag.toLowerCase().includes(category.toLowerCase()))
    );
  }

  /**
   * Calculate relevance score for general FAQs
   */
  private calculateRelevanceScore(faq: FAQ, searchTerms: string[]): number {
    let score = 0;
    const question = faq.question.toLowerCase();
    const answer = faq.answer.toLowerCase();
    const tags = faq.tags.map(tag => tag.toLowerCase());

    for (const term of searchTerms) {
      // Exact matches in question (highest weight)
      if (question.includes(term)) {
        score += 10;
      }
      
      // Exact matches in tags (high weight)
      if (tags.some(tag => tag.includes(term))) {
        score += 8;
      }
      
      // Exact matches in answer (medium weight)
      if (answer.includes(term)) {
        score += 5;
      }
      
      // Partial matches (lower weight)
      if (question.includes(term.substring(0, Math.max(3, term.length - 1)))) {
        score += 2;
      }
    }

    return score;
  }

  /**
   * Calculate relevance score for venue FAQs
   */
  private calculateVenueRelevanceScore(faq: VenueFAQ, searchTerms: string[]): number {
    let score = 0;
    const question = faq.question.toLowerCase();
    const answer = faq.answer.toLowerCase();
    const category = faq.category?.toLowerCase() || '';

    for (const term of searchTerms) {
      if (question.includes(term)) score += 10;
      if (category.includes(term)) score += 8;
      if (answer.includes(term)) score += 5;
    }

    return score;
  }

  /**
   * Get matched terms for general FAQs
   */
  private getMatchedTerms(faq: FAQ, searchTerms: string[]): string[] {
    const matched: string[] = [];
    const question = faq.question.toLowerCase();
    const answer = faq.answer.toLowerCase();
    const tags = faq.tags.map(tag => tag.toLowerCase());

    for (const term of searchTerms) {
      if (question.includes(term) || answer.includes(term) || 
          tags.some(tag => tag.includes(term))) {
        matched.push(term);
      }
    }

    return matched;
  }

  /**
   * Get matched terms for venue FAQs
   */
  private getVenueMatchedTerms(faq: VenueFAQ, searchTerms: string[]): string[] {
    const matched: string[] = [];
    const question = faq.question.toLowerCase();
    const answer = faq.answer.toLowerCase();
    const category = faq.category?.toLowerCase() || '';

    for (const term of searchTerms) {
      if (question.includes(term) || answer.includes(term) || category.includes(term)) {
        matched.push(term);
      }
    }

    return matched;
  }

  /**
   * Get FAQ categories for filtering
   */
  async getFAQCategories(): Promise<string[]> {
    await this.loadFAQs();
    
    const categories = new Set<string>();
    
    // Extract categories from general FAQ tags
    this.generalFAQs.forEach(faq => {
      faq.tags.forEach(tag => categories.add(tag));
    });
    
    // Extract categories from venue FAQs
    this.venueFAQs.forEach(faq => {
      if (faq.category) categories.add(faq.category);
    });
    
    return Array.from(categories).sort();
  }

  /**
   * Clear cache to force refresh
   */
  clearCache(): void {
    this.lastFetchTime = 0;
    this.generalFAQs = [];
    this.venueFAQs = [];
  }
}

// Export singleton instance
export const faqService = new FAQService();
export default faqService;

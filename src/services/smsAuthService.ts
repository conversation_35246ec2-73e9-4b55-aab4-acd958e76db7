import { supabase } from '@/integrations/supabase/client';

export interface SMSSignUpData {
  phone: string;
  password: string;
  full_name: string;
}

export interface SMSOTPVerificationData {
  phone: string;
  otp: string;
}

export interface SMSAuthResult {
  success: boolean;
  error?: string;
  user?: any;
  phone?: string;
  expires_in?: number;
  sessionUrl?: string;
}

class SMSAuthService {
  /**
   * Format phone number to international format
   */
  private formatPhoneNumber(phone: string, countryCode: string = '+91'): string {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');
    
    // If phone starts with country code digits, don't add again
    if (cleanPhone.startsWith('91') && countryCode === '+91') {
      return '+' + cleanPhone;
    }
    
    // Add country code if not present
    if (!cleanPhone.startsWith(countryCode.replace('+', ''))) {
      return countryCode + cleanPhone;
    }
    
    return '+' + cleanPhone;
  }

  /**
   * Validate phone number format
   */
  private validatePhoneNumber(phone: string): boolean {
    // Basic international phone number validation
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    const cleanPhone = phone.replace(/[\s()-]/g, '');
    return phoneRegex.test(cleanPhone);
  }

  /**
   * Send SMS OTP for registration
   */
  async sendSMSOTP(userData: SMSSignUpData): Promise<SMSAuthResult> {
    try {
      // Format and validate phone number
      const formattedPhone = this.formatPhoneNumber(userData.phone);

      if (!this.validatePhoneNumber(formattedPhone)) {
        return {
          success: false,
          error: 'Invalid phone number format'
        };
      }

      // Validate other fields
      if (!userData.full_name || userData.full_name.trim().length < 2) {
        return {
          success: false,
          error: 'Full name must be at least 2 characters long'
        };
      }

      if (!userData.password || userData.password.length < 6) {
        return {
          success: false,
          error: 'Password must be at least 6 characters long'
        };
      }

      console.log('Sending SMS OTP to:', formattedPhone);

      // Call send-sms-otp edge function
      const { data, error } = await supabase.functions.invoke('send-sms-otp', {
        body: {
          phone: formattedPhone,
          fullName: userData.full_name.trim(),
          password: userData.password,
          purpose: 'registration'
        }
      });

      if (error) {
        console.error('SMS OTP send error:', error);
        return {
          success: false,
          error: 'Unable to send verification code. Please check your phone number and try again.'
        };
      }

      if (!data.success) {
        console.error('SMS OTP API error:', data);
        // Check if it's a phone already registered error
        if (data.error && data.error.includes('already registered')) {
          return {
            success: false,
            error: 'This phone number is already registered with Grid२Play. Please sign in instead or use a different phone number.'
          };
        }
        return {
          success: false,
          error: 'Unable to send verification code. Please try again in a few moments.'
        };
      }

      console.log('SMS OTP sent successfully:', data);
      return {
        success: true,
        phone: data.phone,
        expires_in: data.expires_in
      };
    } catch (error) {
      console.error('Error in sendSMSOTP:', error);
      return {
        success: false,
        error: 'Unable to send verification code. Please check your connection and try again.'
      };
    }
  }

  /**
   * Verify SMS OTP and create user account
   */
  async verifySMSOTP(verificationData: SMSOTPVerificationData): Promise<SMSAuthResult> {
    try {
      // Format phone number
      const formattedPhone = this.formatPhoneNumber(verificationData.phone);

      // Validate OTP format
      if (!/^\d{6}$/.test(verificationData.otp)) {
        return {
          success: false,
          error: 'Invalid OTP format. Must be 6 digits.'
        };
      }

      console.log('Verifying SMS OTP for:', formattedPhone);

      // Call verify-sms-otp edge function
      const { data, error } = await supabase.functions.invoke('verify-sms-otp', {
        body: {
          phone: formattedPhone,
          otp: verificationData.otp
        }
      });

      if (error) {
        console.error('SMS OTP verification error:', error);
        return {
          success: false,
          error: 'Unable to verify code. Please check your code and try again.'
        };
      }

      if (!data.success) {
        console.error('SMS OTP verification failed:', data);
        return {
          success: false,
          error: data.error || 'OTP verification failed'
        };
      }

      console.log('SMS OTP verified successfully:', data);
      return {
        success: true,
        user: data.user
      };
    } catch (error) {
      console.error('Error in verifySMSOTP:', error);
      return {
        success: false,
        error: 'Unable to verify code. Please try again.'
      };
    }
  }

  /**
   * Resend SMS OTP
   */
  async resendSMSOTP(phone: string): Promise<SMSAuthResult> {
    try {
      const formattedPhone = this.formatPhoneNumber(phone);

      console.log('Resending SMS OTP to:', formattedPhone);

      // For resend, we need to get the pending user data first
      // This is a simplified approach - in production, you might want a dedicated resend endpoint
      return {
        success: false,
        error: 'Please start the registration process again to resend OTP'
      };
    } catch (error) {
      console.error('Error in resendSMSOTP:', error);
      return {
        success: false,
        error: 'Unable to resend verification code. Please try again in a few moments.'
      };
    }
  }

  /**
   * Login with phone and password (Custom implementation)
   */
  async signInWithPhone(phone: string, password: string): Promise<SMSAuthResult> {
    try {
      const formattedPhone = this.formatPhoneNumber(phone);

      console.log('Signing in with phone:', formattedPhone);

      // Call simple phone login edge function
      const { data, error } = await supabase.functions.invoke('phone-login-simple', {
        body: {
          phone: formattedPhone,
          password: password
        }
      });

      if (error) {
        console.error('Phone sign in error:', error);
        return {
          success: false,
          error: 'Login failed - wrong credentials'
        };
      }

      if (!data.success) {
        console.error('Phone sign in failed:', data);
        return {
          success: false,
          error: 'Login failed - wrong credentials'
        };
      }

      // Set the session in Supabase client if provided
      if (data.session) {
        await supabase.auth.setSession(data.session);
      }

      console.log('Phone sign in successful:', data);
      return {
        success: true,
        user: data.user
      };
    } catch (error) {
      console.error('Error in signInWithPhone:', error);
      return {
        success: false,
        error: 'Login failed - wrong credentials'
      };
    }
  }

  /**
   * Send OTP for passwordless login (uses SMS system)
   */
  async sendLoginOTP(phone: string): Promise<SMSAuthResult> {
    try {
      const formattedPhone = this.formatPhoneNumber(phone);

      console.log('Sending SMS login OTP to:', formattedPhone);

      // Use the Edge Function to check if user exists (bypasses RLS)
      // The Edge Function will handle the profile lookup with service role permissions

      // Use the simplified SMS OTP login system
      const { data, error } = await supabase.functions.invoke('simple-sms-login', {
        body: {
          phone: formattedPhone,
          action: 'send'
        }
      });

      if (error) {
        console.error('Login OTP send error:', error);
        return {
          success: false,
          error: 'Login failed - wrong credentials'
        };
      }

      if (!data.success) {
        return {
          success: false,
          error: 'Login failed - wrong credentials'
        };
      }

      console.log('SMS login OTP sent successfully');
      return {
        success: true,
        phone: formattedPhone
      };
    } catch (error) {
      console.error('Error in sendLoginOTP:', error);
      return {
        success: false,
        error: 'Unable to send login code. Please check your phone number and try again.'
      };
    }
  }

  /**
   * Verify login OTP (uses SMS system)
   */
  async verifyLoginOTP(phone: string, otp: string): Promise<SMSAuthResult> {
    try {
      const formattedPhone = this.formatPhoneNumber(phone);

      console.log('Verifying SMS login OTP for:', formattedPhone);

      // Use the simplified SMS OTP verification system
      const { data, error } = await supabase.functions.invoke('simple-sms-login', {
        body: {
          phone: formattedPhone,
          otp: otp,
          action: 'verify'
        }
      });

      if (error) {
        console.error('Login OTP verification error:', error);
        return {
          success: false,
          error: 'Login failed - wrong credentials'
        };
      }

      if (!data.success) {
        return {
          success: false,
          error: 'Login failed - wrong credentials'
        };
      }

      // Handle session URL if provided
      if (data.session_url) {
        console.log('Login OTP verified, session URL created');
        // For now, we'll redirect the user manually or handle session differently
        // The session URL can be used to authenticate the user
      }

      console.log('SMS login OTP verified successfully:', data.user?.id);
      return {
        success: true,
        user: data.user,
        sessionUrl: data.session_url
      };
    } catch (error) {
      console.error('Error in verifyLoginOTP:', error);
      return {
        success: false,
        error: 'Login failed - wrong credentials'
      };
    }
  }



  /**
   * Get supported country codes
   */
  getSupportedCountryCodes(): Array<{code: string, name: string, flag: string}> {
    return [
      { code: '+91', name: 'India', flag: '🇮🇳' },
      { code: '+1', name: 'United States', flag: '🇺🇸' },
      { code: '+44', name: 'United Kingdom', flag: '🇬🇧' },
      { code: '+971', name: 'UAE', flag: '🇦🇪' },
      { code: '+65', name: 'Singapore', flag: '🇸🇬' }
    ];
  }
}

export const smsAuthService = new SMSAuthService();

// Backward compatibility - export as whatsappAuthService for existing code
export const whatsappAuthService = smsAuthService;
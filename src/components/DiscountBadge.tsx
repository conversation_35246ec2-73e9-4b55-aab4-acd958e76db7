import React from 'react';
import { VenueDiscount, formatDiscountBadge, getDiscountBadgeClasses } from '@/utils/discountUtils';

interface DiscountBadgeProps {
  discount: VenueDiscount;
  className?: string;
}

export const DiscountBadge: React.FC<DiscountBadgeProps> = ({ discount, className = "" }) => {
  const badgeText = formatDiscountBadge(discount);
  const badgeClasses = getDiscountBadgeClasses(discount);

  return (
    <div className={`${badgeClasses} ${className}`}>
      {badgeText}
    </div>
  );
};

export default DiscountBadge;

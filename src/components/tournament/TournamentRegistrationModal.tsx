import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  TrophyIcon,
  UsersIcon,
  CoinsIcon,
  CalendarIcon,
  InfoIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  CreditCardIcon,
} from 'lucide-react';
import { toast } from 'react-hot-toast';

import { TournamentEnhanced, TournamentRegistrationForm } from '@/types/tournament-enhanced';
import { useRegisterForTournament } from '@/hooks/use-tournament-enhanced';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';

const registrationSchema = z.object({
  team_name: z.string().min(2, 'Team name must be at least 2 characters'),
  player_count: z.coerce.number().min(1, 'At least 1 player required'),
  player_skill_level: z.enum(['beginner', 'intermediate', 'advanced', 'professional']).optional(),
  notes: z.string().optional(),
});

type RegistrationFormValues = z.infer<typeof registrationSchema>;

interface TournamentRegistrationModalProps {
  tournament: TournamentEnhanced;
  isOpen: boolean;
  onClose: () => void;
}

export function TournamentRegistrationModal({
  tournament,
  isOpen,
  onClose,
}: TournamentRegistrationModalProps) {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [registrationData, setRegistrationData] = useState<RegistrationFormValues | null>(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  const registerMutation = useRegisterForTournament();

  const form = useForm<RegistrationFormValues>({
    resolver: zodResolver(registrationSchema),
    defaultValues: {
      team_name: `${user?.user_metadata?.full_name || 'Player'} ${tournament.format_type === 'team' ? 'Team' : ''}`,
      player_count: tournament.format_type === 'team' ? tournament.team_size : 1,
      player_skill_level: undefined,
      notes: '',
    },
  });

  // Create Razorpay order for tournament entry fee
  const createTournamentPaymentOrder = async (registrationData: RegistrationFormValues) => {
    try {
      const totalAmountInPaise = Math.round(tournament.entry_fee * 100);
      const receipt = `tournament_${tournament.id}_${Date.now()}`;

      const response = await supabase.functions.invoke('create-razorpay-order', {
        body: {
          amount: totalAmountInPaise,
          receipt: receipt,
          notes: {
            tournament_id: tournament.id,
            tournament_name: tournament.name,
            team_name: registrationData.team_name,
            player_count: registrationData.player_count,
            user_id: user?.id,
            type: 'tournament_registration'
          }
        }
      });

      if (response.error) {
        console.error('Payment order creation failed:', response.error);
        throw response.error;
      }

      return response.data;
    } catch (error) {
      console.error('Error creating payment order:', error);
      throw error;
    }
  };

  // Handle payment success
  const handlePaymentSuccess = async (paymentData: any, registrationData: RegistrationFormValues) => {
    try {
      // Register for tournament with payment confirmation
      await registerMutation.mutateAsync({
        tournamentId: tournament.id,
        registrationData: {
          ...registrationData,
          payment_status: 'paid',
          payment_id: paymentData.razorpay_payment_id,
          order_id: paymentData.razorpay_order_id,
        },
      });

      toast.success('🏆 Registration and payment successful!');
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Registration error after payment:', error);
      toast.error('Payment successful but registration failed. Please contact support.');
    }
  };

  // Handle payment failure
  const handlePaymentFailure = (error: any) => {
    console.error('Payment failed:', error);
    toast.error('Payment failed. Please try again.');
    setIsProcessingPayment(false);
  };

  // Process payment with Razorpay
  const processPayment = async (registrationData: RegistrationFormValues) => {
    if (!user) {
      toast.error('Please log in to continue');
      return;
    }

    setIsProcessingPayment(true);

    try {
      const orderData = await createTournamentPaymentOrder(registrationData);
      if (!orderData) return;

      const { order } = orderData;

      const options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID || 'rzp_test_fallback',
        amount: order.amount,
        currency: order.currency,
        name: "Grid२Play",
        description: `Tournament Registration - ${tournament.name}`,
        order_id: order.id,
        prefill: {
          name: registrationData.team_name,
          email: user.email,
          contact: user.phone || ''
        },
        notes: {
          tournament_id: tournament.id,
          team_name: registrationData.team_name,
          user_id: user.id
        },
        theme: {
          color: "#047857" // Emerald-800
        },
        handler: (response: any) => {
          handlePaymentSuccess(response, registrationData);
        },
        modal: {
          ondismiss: () => {
            setIsProcessingPayment(false);
          }
        }
      };

      const razorpayInstance = new (window as any).Razorpay(options);
      razorpayInstance.on('payment.failed', handlePaymentFailure);
      razorpayInstance.open();

    } catch (error) {
      console.error("Payment initialization error:", error);
      toast.error("Failed to initialize payment");
      setIsProcessingPayment(false);
    }
  };

  const onSubmit = async (data: RegistrationFormValues) => {
    if (currentStep === 1) {
      setRegistrationData(data);
      setCurrentStep(2);
      return;
    }

    // If tournament has entry fee, process payment
    if (tournament.entry_fee > 0) {
      await processPayment(data);
    } else {
      // Free tournament - register directly
      try {
        await registerMutation.mutateAsync({
          tournamentId: tournament.id,
          registrationData: data,
        });

        toast.success('Registration successful!');
        onSuccess();
        onClose();
      } catch (error: any) {
        console.error('Registration error:', error);
        toast.error(error.message || 'Registration failed');
      }
    }
  };

  const handleBack = () => {
    if (currentStep === 2) {
      setCurrentStep(1);
    }
  };

  const isTeamTournament = tournament.format_type === 'team';
  const registrationFull = tournament.registration_count >= tournament.max_participants;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrophyIcon className="h-5 w-5 text-emerald-600" />
            Register for Tournament
          </DialogTitle>
          <DialogDescription>
            Join {tournament.name} and compete with other players
          </DialogDescription>
        </DialogHeader>

        {registrationFull ? (
          <Alert>
            <AlertCircleIcon className="h-4 w-4" />
            <AlertDescription>
              This tournament is full. Registration is no longer available.
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-6">
            {/* Progress Indicator */}
            <div className="flex items-center justify-center space-x-4">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                currentStep >= 1 ? 'bg-emerald-600 border-emerald-600 text-white' : 'border-gray-300'
              }`}>
                1
              </div>
              <div className={`w-16 h-1 ${currentStep >= 2 ? 'bg-emerald-600' : 'bg-gray-300'}`} />
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                currentStep >= 2 ? 'bg-emerald-600 border-emerald-600 text-white' : 'border-gray-300'
              }`}>
                2
              </div>
            </div>

            <div className="text-center text-sm text-gray-600">
              Step {currentStep} of 2: {currentStep === 1 ? 'Registration Details' : 'Payment & Confirmation'}
            </div>

            {/* Step 1: Registration Form */}
            {currentStep === 1 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    {/* Tournament Summary */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Tournament Summary</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Sport:</span>
                            <span className="ml-2 font-medium">{tournament.sport_name}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Format:</span>
                            <span className="ml-2 font-medium capitalize">{tournament.format_type}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Type:</span>
                            <span className="ml-2 font-medium capitalize">{tournament.tournament_type}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Team Size:</span>
                            <span className="ml-2 font-medium">{tournament.team_size} players</span>
                          </div>
                        </div>
                        
                        {tournament.entry_fee > 0 && (
                          <div className="flex items-center justify-between pt-2 border-t">
                            <span className="text-gray-600">Entry Fee:</span>
                            <span className="text-lg font-bold text-emerald-600">₹{tournament.entry_fee}</span>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Registration Form */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Registration Details</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <FormField
                          control={form.control}
                          name="team_name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                {isTeamTournament ? 'Team Name' : 'Player Name'} *
                              </FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder={isTeamTournament ? 'Enter your team name' : 'Enter your name'}
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="player_count"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Number of Players *</FormLabel>
                              <FormControl>
                                <Input 
                                  type="number" 
                                  min="1" 
                                  max={tournament.team_size}
                                  {...field} 
                                />
                              </FormControl>
                              <FormDescription>
                                {isTeamTournament 
                                  ? `Maximum ${tournament.team_size} players per team`
                                  : 'Individual tournament (1 player)'
                                }
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="player_skill_level"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Skill Level</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select your skill level" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="beginner">Beginner</SelectItem>
                                  <SelectItem value="intermediate">Intermediate</SelectItem>
                                  <SelectItem value="advanced">Advanced</SelectItem>
                                  <SelectItem value="professional">Professional</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                This helps organizers create balanced matches
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="notes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Additional Notes</FormLabel>
                              <FormControl>
                                <Textarea 
                                  placeholder="Any additional information or special requirements..."
                                  rows={3}
                                  {...field} 
                                />
                              </FormControl>
                              <FormDescription>
                                Optional: Share any relevant information with the organizers
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>

                    {/* Age Restrictions Warning */}
                    {tournament.age_restrictions && (
                      <Alert>
                        <InfoIcon className="h-4 w-4" />
                        <AlertDescription>
                          <strong>Age Requirements:</strong> This tournament has age restrictions.
                          {tournament.age_restrictions.min_age && ` Minimum age: ${tournament.age_restrictions.min_age}`}
                          {tournament.age_restrictions.max_age && ` Maximum age: ${tournament.age_restrictions.max_age}`}
                        </AlertDescription>
                      </Alert>
                    )}

                    <div className="flex justify-between pt-4">
                      <Button type="button" variant="outline" onClick={onClose}>
                        Cancel
                      </Button>
                      <Button type="submit">
                        {tournament.entry_fee > 0 ? 'Continue to Payment' : 'Complete Registration'}
                      </Button>
                    </div>
                  </form>
                </Form>
              </motion.div>
            )}

            {/* Step 2: Payment & Confirmation */}
            {currentStep === 2 && registrationData && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                {/* Registration Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Registration Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Team/Player:</span>
                        <span className="ml-2 font-medium">{registrationData.team_name}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Players:</span>
                        <span className="ml-2 font-medium">{registrationData.player_count}</span>
                      </div>
                      {registrationData.player_skill_level && (
                        <div>
                          <span className="text-gray-600">Skill Level:</span>
                          <span className="ml-2 font-medium capitalize">{registrationData.player_skill_level}</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Payment Details */}
                {tournament.entry_fee > 0 ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <CreditCardIcon className="h-5 w-5 text-emerald-600" />
                        Payment Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex justify-between items-center text-lg">
                        <span>Entry Fee:</span>
                        <span className="font-bold text-emerald-600">₹{tournament.entry_fee}</span>
                      </div>
                      
                      <Alert>
                        <InfoIcon className="h-4 w-4" />
                        <AlertDescription>
                          Payment will be processed securely through our payment gateway. 
                          You will receive a confirmation email after successful payment.
                        </AlertDescription>
                      </Alert>
                    </CardContent>
                  </Card>
                ) : (
                  <Alert>
                    <CheckCircleIcon className="h-4 w-4" />
                    <AlertDescription>
                      This is a free tournament. No payment is required for registration.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex justify-between pt-4">
                  <Button type="button" variant="outline" onClick={handleBack}>
                    Back
                  </Button>
                  <Button
                    onClick={() => form.handleSubmit(onSubmit)()}
                    disabled={registerMutation.isPending || isProcessingPayment}
                    className="bg-emerald-600 hover:bg-emerald-700"
                  >
                    {isProcessingPayment ? (
                      <div className="flex items-center gap-2">
                        <CreditCardIcon className="h-4 w-4" />
                        Processing Payment...
                      </div>
                    ) : registerMutation.isPending ? 'Processing...' :
                     tournament.entry_fee > 0 ? (
                      <div className="flex items-center gap-2">
                        <CreditCardIcon className="h-4 w-4" />
                        Pay ₹{tournament.entry_fee} & Register
                      </div>
                     ) : 'Complete Registration'}
                  </Button>
                </div>
              </motion.div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

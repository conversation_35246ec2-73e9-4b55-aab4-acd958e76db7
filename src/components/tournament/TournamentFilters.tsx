import React from 'react';
import { motion } from 'framer-motion';
import { X, Calendar, MapPin, Trophy, Users, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { TournamentFilters as TournamentFiltersType } from '@/types/tournament-enhanced';

interface TournamentFiltersProps {
  filters: TournamentFiltersType;
  onFiltersChange: (filters: TournamentFiltersType) => void;
  onClose?: () => void;
}

export function TournamentFilters({ filters, onFiltersChange, onClose }: TournamentFiltersProps) {
  const statusOptions = [
    { value: 'registration_open', label: 'Registration Open', color: 'bg-green-500' },
    { value: 'upcoming', label: 'Upcoming', color: 'bg-blue-500' },
    { value: 'ongoing', label: 'Live', color: 'bg-orange-500' },
    { value: 'completed', label: 'Completed', color: 'bg-gray-500' },
  ];

  const tournamentTypeOptions = [
    { value: 'knockout', label: 'Knockout' },
    { value: 'round_robin', label: 'Round Robin' },
    { value: 'league', label: 'League' },
    { value: 'swiss', label: 'Swiss' },
  ];

  const formatTypeOptions = [
    { value: 'individual', label: 'Individual' },
    { value: 'team', label: 'Team' },
    { value: 'mixed', label: 'Mixed' },
  ];

  const updateFilter = (key: keyof TournamentFiltersType, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const toggleStatusFilter = (status: string) => {
    const currentStatus = filters.status || [];
    const newStatus = currentStatus.includes(status)
      ? currentStatus.filter(s => s !== status)
      : [...currentStatus, status];
    updateFilter('status', newStatus);
  };

  const toggleTournamentType = (type: string) => {
    const currentTypes = filters.tournament_type || [];
    const newTypes = currentTypes.includes(type)
      ? currentTypes.filter(t => t !== type)
      : [...currentTypes, type];
    updateFilter('tournament_type', newTypes);
  };

  const toggleFormatType = (type: string) => {
    const currentFormats = filters.format_type || [];
    const newFormats = currentFormats.includes(type)
      ? currentFormats.filter(f => f !== type)
      : [...currentFormats, type];
    updateFilter('format_type', newFormats);
  };

  const clearAllFilters = () => {
    onFiltersChange({});
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.status?.length) count++;
    if (filters.sport_id) count++;
    if (filters.venue_id) count++;
    if (filters.tournament_type?.length) count++;
    if (filters.format_type?.length) count++;
    if (filters.entry_fee_range) count++;
    if (filters.featured_only) count++;
    return count;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="w-full bg-gray-800 border-gray-700">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold text-white">
              Tournament Filters
              {getActiveFilterCount() > 0 && (
                <Badge variant="secondary" className="ml-2 bg-emerald-600 text-white">
                  {getActiveFilterCount()} active
                </Badge>
              )}
            </CardTitle>
            <div className="flex items-center gap-2">
              {getActiveFilterCount() > 0 && (
                <Button variant="ghost" size="sm" onClick={clearAllFilters} className="text-white hover:text-emerald-400">
                  Clear All
                </Button>
              )}
              {onClose && (
                <Button variant="ghost" size="sm" onClick={onClose} className="text-white hover:text-emerald-400">
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Tournament Status */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-emerald-600" />
              <Label className="text-sm font-medium text-white">Tournament Status</Label>
            </div>
            <div className="flex flex-wrap gap-2">
              {statusOptions.map((option) => (
                <Badge
                  key={option.value}
                  variant={filters.status?.includes(option.value) ? "default" : "outline"}
                  className={`cursor-pointer transition-all ${
                    filters.status?.includes(option.value)
                      ? `${option.color} text-white`
                      : 'hover:bg-gray-100'
                  }`}
                  onClick={() => toggleStatusFilter(option.value)}
                >
                  <div className={`h-2 w-2 rounded-full ${option.color} mr-2`}></div>
                  {option.label}
                </Badge>
              ))}
            </div>
          </div>

          {/* Tournament Type */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Trophy className="h-4 w-4 text-emerald-600" />
              <Label className="text-sm font-medium text-white">Tournament Type</Label>
            </div>
            <div className="flex flex-wrap gap-2">
              {tournamentTypeOptions.map((option) => (
                <Badge
                  key={option.value}
                  variant={filters.tournament_type?.includes(option.value) ? "default" : "outline"}
                  className="cursor-pointer transition-all hover:bg-emerald-50"
                  onClick={() => toggleTournamentType(option.value)}
                >
                  {option.label}
                </Badge>
              ))}
            </div>
          </div>

          {/* Format Type */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-emerald-600" />
              <Label className="text-sm font-medium text-white">Format</Label>
            </div>
            <div className="flex flex-wrap gap-2">
              {formatTypeOptions.map((option) => (
                <Badge
                  key={option.value}
                  variant={filters.format_type?.includes(option.value) ? "default" : "outline"}
                  className="cursor-pointer transition-all hover:bg-emerald-50"
                  onClick={() => toggleFormatType(option.value)}
                >
                  {option.label}
                </Badge>
              ))}
            </div>
          </div>

          {/* Sport Selection */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Trophy className="h-4 w-4 text-emerald-600" />
              <Label className="text-sm font-medium text-white">Sport</Label>
            </div>
            <Select
              value={filters.sport_id || 'all'}
              onValueChange={(value) => updateFilter('sport_id', value === 'all' ? undefined : value)}
            >
              <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                <SelectValue placeholder="Select sport" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                <SelectItem value="all">All Sports</SelectItem>
                <SelectItem value="cricket">Cricket</SelectItem>
                <SelectItem value="football">Football</SelectItem>
                <SelectItem value="badminton">Badminton</SelectItem>
                <SelectItem value="tennis">Tennis</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Venue Selection */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-emerald-600" />
              <Label className="text-sm font-medium text-white">Venue</Label>
            </div>
            <Select
              value={filters.venue_id || 'all'}
              onValueChange={(value) => updateFilter('venue_id', value === 'all' ? undefined : value)}
            >
              <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                <SelectValue placeholder="Select venue" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                <SelectItem value="all">All Venues</SelectItem>
                <SelectItem value="venue1">RPM Box Cricket</SelectItem>
                <SelectItem value="venue2">East Delhi Sports Complex</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Entry Fee Range */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-emerald-600" />
              <Label className="text-sm font-medium text-white">Entry Fee Range</Label>
            </div>
            <div className="space-y-2">
              <Slider
                value={filters.entry_fee_range || [0, 5000]}
                onValueChange={(value) => updateFilter('entry_fee_range', value as [number, number])}
                max={5000}
                step={100}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-gray-300">
                <span>₹{filters.entry_fee_range?.[0] || 0}</span>
                <span>₹{filters.entry_fee_range?.[1] || 5000}</span>
              </div>
            </div>
          </div>

          {/* Featured Only */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="featured"
              checked={filters.featured_only || false}
              onCheckedChange={(checked) => updateFilter('featured_only', checked)}
            />
            <Label htmlFor="featured" className="text-sm font-medium text-white">
              Show only featured tournaments
            </Label>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

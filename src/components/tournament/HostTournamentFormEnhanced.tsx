import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  CalendarIcon, 
  MapPinIcon, 
  TrophyIcon, 
  UsersIcon,
  DollarSignIcon,
  InfoIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  ClockIcon,
  EyeIcon,
  PlayIcon
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'react-hot-toast';

import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { useCreateTournament, useTournamentCategories } from '@/hooks/use-tournament-enhanced';
import { CreateTournamentForm } from '@/types/tournament-enhanced';

const formSchema = z.object({
  tournament_name: z.string().min(3, 'Tournament name must be at least 3 characters'),
  description: z.string().optional(),
  sport_id: z.string().min(1, 'Please select a sport'),
  venue_id: z.string().min(1, 'Please select a venue'),
  category_id: z.string().optional(),
  
  tournament_type: z.enum(['knockout', 'round_robin', 'league', 'swiss']),
  format_type: z.enum(['individual', 'team', 'mixed']),
  max_participants: z.coerce.number().min(2, 'At least 2 participants required').max(1000, 'Maximum 1000 participants'),
  min_participants: z.coerce.number().min(2, 'At least 2 participants required'),
  team_size: z.coerce.number().min(1, 'Team size must be at least 1').max(20, 'Maximum 20 players per team'),
  
  registration_start_date: z.string().min(1, 'Registration start date is required'),
  registration_end_date: z.string().min(1, 'Registration end date is required'),
  tournament_start_date: z.string().min(1, 'Tournament start date is required'),
  tournament_end_date: z.string().min(1, 'Tournament end date is required'),
  
  entry_fee: z.coerce.number().min(0, 'Entry fee cannot be negative').max(50000, 'Maximum entry fee is ₹50,000'),
  prize_pool: z.coerce.number().min(0, 'Prize pool cannot be negative').max(1000000, 'Maximum prize pool is ₹10,00,000'),
  
  rules: z.string().optional(),
  min_age: z.coerce.number().optional(),
  max_age: z.coerce.number().optional(),
  skill_requirements: z.string().optional(),
  equipment_provided: z.boolean().default(false),
  live_streaming: z.boolean().default(false),
  public_viewing: z.boolean().default(true),
  visibility: z.enum(['public', 'private', 'invite_only']).default('public'),
  
  organizer_name: z.string().min(2, 'Organizer name is required'),
  contact_info: z.string().email('Please enter a valid email address'),
});

type FormValues = z.infer<typeof formSchema>;

export function HostTournamentFormEnhanced() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [sports, setSports] = useState<{ id: string; name: string }[]>([]);
  const [venues, setVenues] = useState<{ id: string; name: string; location: string }[]>([]);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  const { data: categories = [] } = useTournamentCategories();
  const createTournamentMutation = useCreateTournament();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      tournament_name: '',
      description: '',
      sport_id: '',
      venue_id: '',
      category_id: '',
      tournament_type: 'knockout',
      format_type: 'team',
      max_participants: 16,
      min_participants: 8,
      team_size: 1,
      registration_start_date: '',
      registration_end_date: '',
      tournament_start_date: '',
      tournament_end_date: '',
      entry_fee: 0,
      prize_pool: 0,
      rules: '',
      min_age: undefined,
      max_age: undefined,
      skill_requirements: '',
      equipment_provided: false,
      live_streaming: false,
      public_viewing: true,
      visibility: 'public',
      organizer_name: user?.user_metadata?.full_name || '',
      contact_info: user?.email || '',
    },
  });

  // Fetch sports and venues (maintaining existing integration)
  useEffect(() => {
    const fetchSportsAndVenues = async () => {
      try {
        const [sportsResponse, venuesResponse] = await Promise.all([
          supabase.from('sports').select('id, name').eq('is_active', true).order('name'),
          supabase.from('venues').select('id, name, location').eq('is_active', true).order('name')
        ]);

        if (sportsResponse.data) setSports(sportsResponse.data);
        if (venuesResponse.data) setVenues(venuesResponse.data);
      } catch (error) {
        console.error('Error fetching sports and venues:', error);
        toast.error('Failed to load sports and venues');
      }
    };

    fetchSportsAndVenues();
  }, []);

  const onSubmit = async (data: FormValues) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading('Creating your tournament...');

      const tournamentData: CreateTournamentForm = {
        tournament_name: data.tournament_name,
        description: data.description,
        sport_id: data.sport_id,
        venue_id: data.venue_id,
        category_id: data.category_id === 'none' ? undefined : data.category_id,
        tournament_type: data.tournament_type,
        format_type: data.format_type,
        max_participants: data.max_participants,
        min_participants: data.min_participants,
        team_size: data.team_size,
        registration_start_date: data.registration_start_date,
        registration_end_date: data.registration_end_date,
        tournament_start_date: data.tournament_start_date,
        tournament_end_date: data.tournament_end_date,
        entry_fee: data.entry_fee,
        prize_pool: data.prize_pool,
        rules: data.rules,
        age_restrictions: data.min_age || data.max_age ? {
          min_age: data.min_age,
          max_age: data.max_age,
        } : undefined,
        skill_requirements: data.skill_requirements,
        equipment_provided: data.equipment_provided,
        live_streaming: data.live_streaming,
        public_viewing: data.public_viewing,
        visibility: data.visibility,
        organizer_name: data.organizer_name,
        contact_info: data.contact_info,
      };

      const result = await createTournamentMutation.mutateAsync(tournamentData);

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success('🏆 Tournament created successfully! Your tournament is now under review and will be published soon.', {
        duration: 5000,
      });

      // Navigate after a short delay to let user see the success message
      setTimeout(() => {
        navigate('/tournaments');
      }, 2000);

    } catch (error: any) {
      console.error('Error creating tournament:', error);

      // Show specific error messages based on error type
      let errorMessage = 'Failed to create tournament. Please try again.';

      if (error.message?.includes('duplicate')) {
        errorMessage = 'A tournament with this name already exists. Please choose a different name.';
      } else if (error.message?.includes('permission')) {
        errorMessage = 'You do not have permission to create tournaments. Please contact support.';
      } else if (error.message?.includes('validation')) {
        errorMessage = 'Please check your form data and try again.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(`❌ ${errorMessage}`, {
        duration: 6000,
      });
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return 'Basic Information';
      case 2: return 'Tournament Configuration';
      case 3: return 'Schedule & Pricing';
      case 4: return 'Rules & Settings';
      default: return '';
    }
  };

  const getStepIcon = (step: number) => {
    switch (step) {
      case 1: return <InfoIcon className="h-5 w-5" />;
      case 2: return <TrophyIcon className="h-5 w-5" />;
      case 3: return <CalendarIcon className="h-5 w-5" />;
      case 4: return <CheckCircleIcon className="h-5 w-5" />;
      default: return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-900 rounded-lg border border-gray-800">
      {/* Header with Branding */}
      <div className="text-center mb-8">
        <div className="text-emerald-400 text-lg font-medium mb-2">Grid२Play</div>
        <h1 className="text-3xl font-bold text-white mb-2">Tournament Creation</h1>
        <p className="text-gray-300">
          Create professional tournaments with comprehensive management tools
        </p>
      </div>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[1, 2, 3, 4].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                step <= currentStep 
                  ? 'bg-emerald-600 border-emerald-600 text-white' 
                  : 'border-gray-300 text-gray-400'
              }`}>
                {step < currentStep ? (
                  <CheckCircleIcon className="h-5 w-5" />
                ) : (
                  getStepIcon(step)
                )}
              </div>
              {step < 4 && (
                <div className={`w-full h-1 mx-4 ${
                  step < currentStep ? 'bg-emerald-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>
        <div className="mt-4 text-center">
          <h2 className="text-2xl font-bold text-white">
            Step {currentStep}: {getStepTitle(currentStep)}
          </h2>
          <p className="text-gray-300 mt-1">
            {currentStep === 1 && 'Tell us about your tournament'}
            {currentStep === 2 && 'Configure tournament format and participants'}
            {currentStep === 3 && 'Set dates, pricing, and prizes'}
            {currentStep === 4 && 'Define rules and additional settings'}
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <InfoIcon className="h-5 w-5 text-emerald-400" />
                    Basic Tournament Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={form.control}
                    name="tournament_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Tournament Name *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., Summer Cricket Championship 2024"
                            className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:border-emerald-500"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe your tournament, its objectives, and what makes it special..."
                            rows={4}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          A compelling description helps attract more participants
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="sport_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Sport *</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select sport" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {sports.map((sport) => (
                                <SelectItem key={sport.id} value={sport.id}>
                                  {sport.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="venue_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Venue *</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select venue" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {venues.map((venue) => (
                                <SelectItem key={venue.id} value={venue.id}>
                                  <div>
                                    <div className="font-medium">{venue.name}</div>
                                    <div className="text-sm text-gray-500">{venue.location}</div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="category_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tournament Category</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select category (optional)" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="none">No specific category</SelectItem>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                <div>
                                  <div className="font-medium">{category.name}</div>
                                  {category.description && (
                                    <div className="text-sm text-gray-500">{category.description}</div>
                                  )}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Categories help players find tournaments suited to their age and skill level
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="organizer_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Organizer Name *</FormLabel>
                          <FormControl>
                            <Input placeholder="Your name or organization" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="contact_info"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact Email *</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Tournament Configuration */}
          {currentStep === 2 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <TrophyIcon className="h-5 w-5 text-emerald-400" />
                    Tournament Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="tournament_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tournament Type *</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select tournament type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="knockout">
                                <div>
                                  <div className="font-medium">Knockout</div>
                                  <div className="text-sm text-gray-500">Single elimination format</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="round_robin">
                                <div>
                                  <div className="font-medium">Round Robin</div>
                                  <div className="text-sm text-gray-500">Everyone plays everyone</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="league">
                                <div>
                                  <div className="font-medium">League</div>
                                  <div className="text-sm text-gray-500">Points-based standings</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="swiss">
                                <div>
                                  <div className="font-medium">Swiss</div>
                                  <div className="text-sm text-gray-500">Pairing-based system</div>
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="format_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Format Type *</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select format" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="individual">Individual</SelectItem>
                              <SelectItem value="team">Team</SelectItem>
                              <SelectItem value="mixed">Mixed</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="max_participants"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Max Participants *</FormLabel>
                          <FormControl>
                            <Input type="number" min="2" max="1000" {...field} />
                          </FormControl>
                          <FormDescription>Maximum number of participants</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="min_participants"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Min Participants *</FormLabel>
                          <FormControl>
                            <Input type="number" min="2" {...field} />
                          </FormControl>
                          <FormDescription>Minimum to start tournament</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="team_size"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Team Size *</FormLabel>
                          <FormControl>
                            <Input type="number" min="1" max="20" {...field} />
                          </FormControl>
                          <FormDescription>Players per team</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Alert>
                    <AlertCircleIcon className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Tournament Type Guide:</strong>
                      <ul className="mt-2 space-y-1 text-sm">
                        <li><strong>Knockout:</strong> Fast elimination format, ideal for 8-64 participants</li>
                        <li><strong>Round Robin:</strong> Fair format where everyone plays, best for 4-12 participants</li>
                        <li><strong>League:</strong> Season-style with points, good for ongoing competitions</li>
                        <li><strong>Swiss:</strong> Balanced pairing system, great for chess-style tournaments</li>
                      </ul>
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Step 3: Schedule & Pricing */}
          {currentStep === 3 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <CalendarIcon className="h-5 w-5 text-emerald-400" />
                    Schedule & Pricing
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="registration_start_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Registration Start Date *</FormLabel>
                          <FormControl>
                            <Input type="datetime-local" {...field} />
                          </FormControl>
                          <FormDescription>When registration opens</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="registration_end_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Registration End Date *</FormLabel>
                          <FormControl>
                            <Input type="datetime-local" {...field} />
                          </FormControl>
                          <FormDescription>Registration deadline</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="tournament_start_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tournament Start Date *</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormDescription>First match date</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="tournament_end_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tournament End Date *</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormDescription>Final match date</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="entry_fee"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Entry Fee (₹)</FormLabel>
                          <FormControl>
                            <Input type="number" min="0" max="50000" placeholder="0" {...field} />
                          </FormControl>
                          <FormDescription>
                            Set to 0 for free tournaments
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="prize_pool"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Prize Pool (₹)</FormLabel>
                          <FormControl>
                            <Input type="number" min="0" max="1000000" placeholder="0" {...field} />
                          </FormControl>
                          <FormDescription>
                            Total prize money for winners
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {form.watch('entry_fee') > 0 && (
                    <Alert>
                      <DollarSignIcon className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Revenue Sharing:</strong> Grid२Play charges a 10% platform fee on entry fees.
                        The remaining 90% goes to the organizer and venue as per the partnership agreement.
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Step 4: Rules & Settings */}
          {currentStep === 4 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <CheckCircleIcon className="h-5 w-5 text-emerald-400" />
                    Rules & Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={form.control}
                    name="rules"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tournament Rules</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Define tournament rules, regulations, and guidelines..."
                            rows={6}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Clear rules help ensure fair play and smooth tournament execution
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="min_age"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum Age</FormLabel>
                          <FormControl>
                            <Input type="number" min="5" max="100" placeholder="No limit" {...field} />
                          </FormControl>
                          <FormDescription>Minimum age requirement</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="max_age"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Maximum Age</FormLabel>
                          <FormControl>
                            <Input type="number" min="5" max="100" placeholder="No limit" {...field} />
                          </FormControl>
                          <FormDescription>Maximum age requirement</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="skill_requirements"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Skill Requirements</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Beginner level, 2+ years experience" {...field} />
                        </FormControl>
                        <FormDescription>
                          Specify any skill level requirements or prerequisites
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="equipment_provided"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Equipment Provided</FormLabel>
                            <FormDescription>
                              Will you provide equipment for participants?
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="live_streaming"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Live Streaming</FormLabel>
                            <FormDescription>
                              Enable live streaming for matches
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="public_viewing"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Public Viewing</FormLabel>
                            <FormDescription>
                              Allow public to view tournament details and results
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="visibility"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tournament Visibility</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select visibility" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="public">
                              <div>
                                <div className="font-medium">Public</div>
                                <div className="text-sm text-gray-500">Anyone can find and join</div>
                              </div>
                            </SelectItem>
                            <SelectItem value="private">
                              <div>
                                <div className="font-medium">Private</div>
                                <div className="text-sm text-gray-500">Only visible to invited participants</div>
                              </div>
                            </SelectItem>
                            <SelectItem value="invite_only">
                              <div>
                                <div className="font-medium">Invite Only</div>
                                <div className="text-sm text-gray-500">Registration by invitation only</div>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 1}
            >
              Previous
            </Button>
            
            {currentStep < totalSteps ? (
              <Button type="button" onClick={nextStep}>
                Next Step
              </Button>
            ) : (
              <Button 
                type="submit" 
                disabled={createTournamentMutation.isPending}
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                {createTournamentMutation.isPending ? 'Creating...' : 'Create Tournament'}
              </Button>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
}

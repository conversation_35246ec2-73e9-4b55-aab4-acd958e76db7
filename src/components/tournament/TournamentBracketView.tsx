import React from 'react';
import { motion } from 'framer-motion';
import { 
  TrophyIcon, 
  PlayIcon, 
  CheckCircleIcon, 
  ClockIcon,
  UsersIcon,
  CalendarIcon,
  MapPinIcon
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TournamentEnhanced, TournamentMatchEnhanced } from '@/types/tournament-enhanced';

interface TournamentBracketViewProps {
  tournament: TournamentEnhanced;
  matches: TournamentMatchEnhanced[];
}

export function TournamentBracketView({ tournament, matches }: TournamentBracketViewProps) {
  const getMatchStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-orange-100 text-orange-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'postponed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getMatchStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled': return <ClockIcon className="h-3 w-3" />;
      case 'in_progress': return <PlayIcon className="h-3 w-3" />;
      case 'completed': return <CheckCircleIcon className="h-3 w-3" />;
      default: return <ClockIcon className="h-3 w-3" />;
    }
  };

  // Group matches by round
  const matchesByRound = matches.reduce((acc, match) => {
    const round = match.round_number;
    if (!acc[round]) {
      acc[round] = [];
    }
    acc[round].push(match);
    return acc;
  }, {} as Record<number, TournamentMatchEnhanced[]>);

  const rounds = Object.keys(matchesByRound).map(Number).sort((a, b) => a - b);

  // Check if bracket is generated
  const isBracketGenerated = matches.length > 0;
  const canGenerateBracket = tournament.status === 'registration_closed' || tournament.status === 'upcoming';

  if (!isBracketGenerated) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-12 text-center">
            <TrophyIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Tournament Bracket Not Generated Yet
            </h3>
            <p className="text-gray-600 mb-6">
              {tournament.status === 'registration_open' 
                ? 'The tournament bracket will be generated after registration closes.'
                : 'The bracket will be available soon.'}
            </p>
            
            {canGenerateBracket && (
              <Button className="bg-emerald-600 hover:bg-emerald-700">
                Generate Bracket
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Tournament Format Info */}
        <Card>
          <CardHeader>
            <CardTitle>Tournament Format</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-600 capitalize">
                  {tournament.tournament_type}
                </div>
                <div className="text-sm text-gray-600">Format</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {tournament.registration_count}
                </div>
                <div className="text-sm text-gray-600">Participants</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 capitalize">
                  {tournament.format_type}
                </div>
                <div className="text-sm text-gray-600">Type</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Format Explanation */}
        <Alert>
          <TrophyIcon className="h-4 w-4" />
          <AlertDescription>
            <strong>{tournament.tournament_type.charAt(0).toUpperCase() + tournament.tournament_type.slice(1)} Tournament:</strong>
            {tournament.tournament_type === 'knockout' && ' Single elimination format where losing teams are eliminated immediately.'}
            {tournament.tournament_type === 'round_robin' && ' Every participant plays against every other participant.'}
            {tournament.tournament_type === 'league' && ' Points-based system with standings and rankings.'}
            {tournament.tournament_type === 'swiss' && ' Pairing-based system where participants with similar scores play each other.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Tournament Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrophyIcon className="h-5 w-5 text-emerald-600" />
            Tournament Bracket
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-emerald-600">{rounds.length}</div>
              <div className="text-sm text-gray-600">Total Rounds</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{matches.length}</div>
              <div className="text-sm text-gray-600">Total Matches</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {matches.filter(m => m.status === 'completed').length}
              </div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {matches.filter(m => m.status === 'in_progress').length}
              </div>
              <div className="text-sm text-gray-600">Live</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bracket Rounds */}
      <div className="space-y-8">
        {rounds.map((roundNumber) => (
          <motion.div
            key={roundNumber}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: roundNumber * 0.1 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>
                    Round {roundNumber}
                    {roundNumber === rounds.length && tournament.tournament_type === 'knockout' && ' (Final)'}
                    {roundNumber === rounds.length - 1 && tournament.tournament_type === 'knockout' && rounds.length > 1 && ' (Semi-Final)'}
                  </span>
                  <Badge variant="outline">
                    {matchesByRound[roundNumber].length} matches
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {matchesByRound[roundNumber].map((match, index) => (
                    <motion.div
                      key={match.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      {/* Match Header */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">Match {match.match_number}</span>
                          <Badge 
                            variant="outline" 
                            className={`${getMatchStatusColor(match.status)} border-0 text-xs`}
                          >
                            {getMatchStatusIcon(match.status)}
                            <span className="ml-1 capitalize">{match.status.replace('_', ' ')}</span>
                          </Badge>
                        </div>
                        {match.is_featured && (
                          <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                            Featured
                          </Badge>
                        )}
                      </div>

                      {/* Participants */}
                      <div className="space-y-2 mb-3">
                        <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <span className="font-medium">
                            {match.participant_a_name || 'TBD'}
                          </span>
                          {match.status === 'completed' && match.winner_id === match.participant_a_id && (
                            <TrophyIcon className="h-4 w-4 text-yellow-500" />
                          )}
                        </div>
                        <div className="text-center text-xs text-gray-500">vs</div>
                        <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <span className="font-medium">
                            {match.participant_b_name || 'TBD'}
                          </span>
                          {match.status === 'completed' && match.winner_id === match.participant_b_id && (
                            <TrophyIcon className="h-4 w-4 text-yellow-500" />
                          )}
                        </div>
                      </div>

                      {/* Match Details */}
                      <div className="space-y-1 text-xs text-gray-600">
                        {match.match_date && (
                          <div className="flex items-center gap-1">
                            <CalendarIcon className="h-3 w-3" />
                            <span>{match.match_date}</span>
                            {match.start_time && <span>at {match.start_time}</span>}
                          </div>
                        )}
                        {match.court_name && (
                          <div className="flex items-center gap-1">
                            <MapPinIcon className="h-3 w-3" />
                            <span>{match.court_name}</span>
                          </div>
                        )}
                        {match.spectator_count > 0 && (
                          <div className="flex items-center gap-1">
                            <UsersIcon className="h-3 w-3" />
                            <span>{match.spectator_count} watching</span>
                          </div>
                        )}
                      </div>

                      {/* Live Score */}
                      {match.status === 'in_progress' && match.live_score && (
                        <div className="mt-3 p-2 bg-orange-50 rounded border border-orange-200">
                          <div className="text-xs font-medium text-orange-800">Live Score</div>
                          <div className="text-sm font-bold text-orange-900">
                            {JSON.stringify(match.live_score)}
                          </div>
                        </div>
                      )}

                      {/* Match Actions */}
                      {match.status === 'in_progress' && (
                        <div className="mt-3">
                          <Button size="sm" variant="outline" className="w-full">
                            <PlayIcon className="h-3 w-3 mr-1" />
                            Watch Live
                          </Button>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Tournament Winner */}
      {tournament.status === 'completed' && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-6 text-center">
            <TrophyIcon className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-yellow-800 mb-2">Tournament Champion</h3>
            <p className="text-yellow-700">
              Congratulations to the tournament winner!
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

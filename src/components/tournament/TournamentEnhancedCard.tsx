import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import {
  CalendarIcon,
  MapPinIcon,
  UsersIcon,
  TrophyIcon,
  CoinsIcon,
  StarIcon,
  ClockIcon,
  EyeIcon,
  PlayIcon,
  CheckCircleIcon,
  XCircleIcon,
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { TournamentEnhanced } from '@/types/tournament-enhanced';

interface TournamentEnhancedCardProps {
  tournament: TournamentEnhanced;
  featured?: boolean;
  compact?: boolean;
}

export function TournamentEnhancedCard({ 
  tournament, 
  featured = false, 
  compact = false 
}: TournamentEnhancedCardProps) {
  const registrationProgress = (tournament.registration_count / tournament.max_participants) * 100;
  const isRegistrationOpen = tournament.status === 'registration_open';
  const isUpcoming = tournament.status === 'upcoming';
  const isOngoing = tournament.status === 'ongoing';
  const isCompleted = tournament.status === 'completed';
  
  const getStatusColor = () => {
    switch (tournament.status) {
      case 'registration_open':
        return 'bg-green-500';
      case 'upcoming':
        return 'bg-blue-500';
      case 'ongoing':
        return 'bg-orange-500';
      case 'completed':
        return 'bg-gray-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = () => {
    switch (tournament.status) {
      case 'registration_open':
        return 'Registration Open';
      case 'upcoming':
        return 'Upcoming';
      case 'ongoing':
        return 'Live';
      case 'completed':
        return 'Completed';
      default:
        return tournament.status;
    }
  };

  const getStatusIcon = () => {
    switch (tournament.status) {
      case 'registration_open':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'upcoming':
        return <ClockIcon className="h-4 w-4" />;
      case 'ongoing':
        return <PlayIcon className="h-4 w-4" />;
      case 'completed':
        return <TrophyIcon className="h-4 w-4" />;
      default:
        return <XCircleIcon className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      className={`h-full ${featured ? 'transform scale-105' : ''}`}
    >
      <Card className={`h-full overflow-hidden group hover:shadow-xl hover:shadow-emerald-500/20 transition-all duration-300 bg-gray-800 border-gray-700 ${
        featured ? 'ring-2 ring-emerald-500 shadow-lg shadow-emerald-500/20' : ''
      }`}>
        {/* Card Header with Status and Featured Badge */}
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <div className={`h-2 w-2 rounded-full ${getStatusColor()}`}></div>
              <Badge 
                variant="secondary" 
                className={`text-xs ${getStatusColor().replace('bg-', 'bg-opacity-10 text-')}`}
              >
                {getStatusIcon()}
                <span className="ml-1">{getStatusText()}</span>
              </Badge>
            </div>
            
            <div className="flex items-center gap-1">
              {featured && (
                <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
                  <StarIcon className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              )}
              {tournament.live_streaming && (
                <Badge variant="outline" className="text-red-500 border-red-500">
                  <PlayIcon className="h-3 w-3 mr-1" />
                  Live
                </Badge>
              )}
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-bold text-white group-hover:text-emerald-400 transition-colors line-clamp-2">
              {tournament.name}
            </h3>
            {tournament.organizer_name && (
              <p className="text-sm text-gray-300 mt-1">
                by {tournament.organizer_name}
              </p>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Tournament Details */}
          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-300">
              <TrophyIcon className="h-4 w-4 mr-2 text-emerald-400" />
              <span className="font-medium">{tournament.sport_name}</span>
              <span className="mx-2">•</span>
              <span className="capitalize">{tournament.tournament_type}</span>
            </div>

            <div className="flex items-center text-sm text-gray-300">
              <MapPinIcon className="h-4 w-4 mr-2 text-emerald-400" />
              <span className="truncate">{tournament.venue_name}</span>
            </div>

            <div className="flex items-center text-sm text-gray-300">
              <CalendarIcon className="h-4 w-4 mr-2 text-emerald-400" />
              <span>
                {format(new Date(tournament.tournament_start_date), 'MMM d')} - {' '}
                {format(new Date(tournament.tournament_end_date), 'MMM d, yyyy')}
              </span>
            </div>
          </div>

          {/* Registration Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center text-gray-300">
                <UsersIcon className="h-4 w-4 mr-2 text-emerald-400" />
                <span>Participants</span>
              </div>
              <span className="font-medium text-white">
                {tournament.registration_count} / {tournament.max_participants}
              </span>
            </div>
            <Progress value={registrationProgress} className="h-2" />
          </div>

          {/* Entry Fee and Prize Pool */}
          {(tournament.entry_fee > 0 || tournament.prize_pool > 0) && (
            <div className="flex items-center justify-between text-sm">
              {tournament.entry_fee > 0 && (
                <div className="flex items-center text-gray-300">
                  <CoinsIcon className="h-4 w-4 mr-1 text-emerald-400" />
                  <span>Entry: ₹{tournament.entry_fee}</span>
                </div>
              )}
              {tournament.prize_pool > 0 && (
                <div className="flex items-center text-emerald-400 font-medium">
                  <TrophyIcon className="h-4 w-4 mr-1" />
                  <span>Prize: ₹{tournament.prize_pool}</span>
                </div>
              )}
            </div>
          )}

          {/* Tournament Format and Team Size */}
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span className="capitalize">{tournament.format_type}</span>
            {tournament.format_type === 'team' && (
              <span>{tournament.team_size} players/team</span>
            )}
            {tournament.view_count > 0 && (
              <div className="flex items-center">
                <EyeIcon className="h-3 w-3 mr-1" />
                <span>{tournament.view_count} views</span>
              </div>
            )}
          </div>

          {/* Registration Deadline (if registration is open) */}
          {isRegistrationOpen && (
            <div className="bg-emerald-900/30 border border-emerald-600/30 rounded-lg p-3">
              <div className="flex items-center text-sm text-emerald-300">
                <ClockIcon className="h-4 w-4 mr-2" />
                <span className="font-medium">
                  Registration closes: {format(new Date(tournament.registration_end_date), 'MMM d, yyyy')}
                </span>
              </div>
            </div>
          )}

          {/* Action Button */}
          <div className="pt-2">
            <Button 
              asChild 
              className={`w-full ${
                isRegistrationOpen 
                  ? 'bg-emerald-600 hover:bg-emerald-700' 
                  : 'bg-gray-600 hover:bg-gray-700'
              }`}
            >
              <Link to={`/tournaments/${tournament.slug}`}>
                {isRegistrationOpen && 'Register Now'}
                {isUpcoming && 'View Details'}
                {isOngoing && 'Watch Live'}
                {isCompleted && 'View Results'}
                {!isRegistrationOpen && !isUpcoming && !isOngoing && !isCompleted && 'View Details'}
              </Link>
            </Button>
          </div>

          {/* Additional Info for Featured Tournaments */}
          {featured && !compact && tournament.description && (
            <div className="pt-2 border-t border-gray-700">
              <p className="text-sm text-gray-300 line-clamp-2">
                {tournament.description}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}

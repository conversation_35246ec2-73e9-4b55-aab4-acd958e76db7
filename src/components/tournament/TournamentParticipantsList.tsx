import React from 'react';
import { motion } from 'framer-motion';
import { 
  UsersIcon, 
  TrophyIcon, 
  StarIcon, 
  CrownIcon,
  UserIcon,
  ShieldIcon
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { TournamentParticipant } from '@/types/tournament-enhanced';

interface TournamentParticipantsListProps {
  participants: TournamentParticipant[];
}

export function TournamentParticipantsList({ participants }: TournamentParticipantsListProps) {
  const getSkillLevelColor = (skillLevel?: string) => {
    switch (skillLevel) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-blue-100 text-blue-800';
      case 'advanced': return 'bg-purple-100 text-purple-800';
      case 'professional': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSkillLevelIcon = (skillLevel?: string) => {
    switch (skillLevel) {
      case 'beginner': return <UserIcon className="h-3 w-3" />;
      case 'intermediate': return <StarIcon className="h-3 w-3" />;
      case 'advanced': return <TrophyIcon className="h-3 w-3" />;
      case 'professional': return <CrownIcon className="h-3 w-3" />;
      default: return <ShieldIcon className="h-3 w-3" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'registered': return 'bg-blue-100 text-blue-800';
      case 'withdrawn': return 'bg-red-100 text-red-800';
      case 'disqualified': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (participants.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <UsersIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Participants Yet</h3>
          <p className="text-gray-600">Be the first to register for this tournament!</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-emerald-600">{participants.length}</div>
            <div className="text-sm text-gray-600">Total Participants</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {participants.filter(p => p.status === 'confirmed').length}
            </div>
            <div className="text-sm text-gray-600">Confirmed</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {participants.filter(p => p.payment_status === 'paid').length}
            </div>
            <div className="text-sm text-gray-600">Paid</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {participants.reduce((sum, p) => sum + p.player_count, 0)}
            </div>
            <div className="text-sm text-gray-600">Total Players</div>
          </CardContent>
        </Card>
      </div>

      {/* Participants List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UsersIcon className="h-5 w-5 text-emerald-600" />
            Registered Participants
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {participants.map((participant, index) => (
              <motion.div
                key={participant.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-4">
                  {/* Seed Number */}
                  {participant.seed_number && (
                    <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-emerald-600">
                        #{participant.seed_number}
                      </span>
                    </div>
                  )}
                  
                  {/* Avatar */}
                  <Avatar>
                    <AvatarImage src="" />
                    <AvatarFallback>
                      {participant.team_name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  
                  {/* Participant Info */}
                  <div>
                    <h3 className="font-medium text-gray-900">{participant.team_name}</h3>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span>{participant.player_count} player{participant.player_count > 1 ? 's' : ''}</span>
                      {participant.user_name && (
                        <>
                          <span>•</span>
                          <span>{participant.user_name}</span>
                        </>
                      )}
                    </div>
                    {participant.notes && (
                      <p className="text-xs text-gray-500 mt-1 max-w-md truncate">
                        {participant.notes}
                      </p>
                    )}
                  </div>
                </div>
                
                {/* Status and Badges */}
                <div className="flex items-center space-x-2">
                  {/* Skill Level */}
                  {participant.player_skill_level && (
                    <Badge 
                      variant="outline" 
                      className={`${getSkillLevelColor(participant.player_skill_level)} border-0`}
                    >
                      {getSkillLevelIcon(participant.player_skill_level)}
                      <span className="ml-1 capitalize">{participant.player_skill_level}</span>
                    </Badge>
                  )}
                  
                  {/* Registration Status */}
                  <Badge 
                    variant="outline" 
                    className={`${getStatusColor(participant.status)} border-0`}
                  >
                    {participant.status}
                  </Badge>
                  
                  {/* Payment Status */}
                  {participant.payment_status === 'paid' && (
                    <Badge className="bg-green-100 text-green-800 border-0">
                      Paid
                    </Badge>
                  )}
                  
                  {participant.payment_status === 'pending' && (
                    <Badge className="bg-orange-100 text-orange-800 border-0">
                      Payment Pending
                    </Badge>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Skill Level Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Skill Level Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['beginner', 'intermediate', 'advanced', 'professional'].map((level) => {
              const count = participants.filter(p => p.player_skill_level === level).length;
              const percentage = participants.length > 0 ? (count / participants.length) * 100 : 0;
              
              return (
                <div key={level} className="text-center">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2 ${getSkillLevelColor(level)}`}>
                    {getSkillLevelIcon(level)}
                  </div>
                  <div className="text-lg font-bold text-gray-900">{count}</div>
                  <div className="text-sm text-gray-600 capitalize">{level}</div>
                  <div className="text-xs text-gray-500">{percentage.toFixed(0)}%</div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

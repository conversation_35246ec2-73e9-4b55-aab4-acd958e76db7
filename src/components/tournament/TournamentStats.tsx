import React from 'react';
import { motion } from 'framer-motion';
import { 
  TrophyIcon, 
  UsersIcon, 
  CalendarIcon, 
  MapPinIcon,
  TrendingUpIcon,
  AwardIcon,
  DollarSignIcon,
  PlayIcon
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface StatCardProps {
  icon: React.ReactNode;
  title: string;
  value: string;
  subtitle?: string;
  trend?: {
    value: string;
    isPositive: boolean;
  };
  delay?: number;
}

function StatCard({ icon, title, value, subtitle, trend, delay = 0 }: StatCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay }}
    >
      <Card className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-white/20 rounded-lg">
              {icon}
            </div>
            {trend && (
              <div className={`flex items-center text-sm ${
                trend.isPositive ? 'text-green-300' : 'text-red-300'
              }`}>
                <TrendingUpIcon className={`h-4 w-4 mr-1 ${
                  !trend.isPositive ? 'rotate-180' : ''
                }`} />
                {trend.value}
              </div>
            )}
          </div>
          
          <div>
            <h3 className="text-2xl font-bold text-white mb-1">{value}</h3>
            <p className="text-emerald-100 text-sm font-medium">{title}</p>
            {subtitle && (
              <p className="text-emerald-200 text-xs mt-1">{subtitle}</p>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function TournamentStats() {
  const stats = [
    {
      icon: <TrophyIcon className="h-6 w-6 text-white" />,
      title: "Active Tournaments",
      value: "524",
      subtitle: "Across all sports",
      trend: { value: "+12%", isPositive: true },
    },
    {
      icon: <UsersIcon className="h-6 w-6 text-white" />,
      title: "Registered Players",
      value: "12,847",
      subtitle: "This month",
      trend: { value: "+24%", isPositive: true },
    },
    {
      icon: <MapPinIcon className="h-6 w-6 text-white" />,
      title: "Partner Venues",
      value: "156",
      subtitle: "Across India",
      trend: { value: "+8%", isPositive: true },
    },
    {
      icon: <DollarSignIcon className="h-6 w-6 text-white" />,
      title: "Prize Money",
      value: "₹8.2L",
      subtitle: "Distributed this month",
      trend: { value: "+31%", isPositive: true },
    },
    {
      icon: <PlayIcon className="h-6 w-6 text-white" />,
      title: "Live Matches",
      value: "47",
      subtitle: "Happening now",
    },
    {
      icon: <AwardIcon className="h-6 w-6 text-white" />,
      title: "Champions",
      value: "2,341",
      subtitle: "Tournament winners",
      trend: { value: "+18%", isPositive: true },
    },
    {
      icon: <CalendarIcon className="h-6 w-6 text-white" />,
      title: "Upcoming Events",
      value: "89",
      subtitle: "Next 30 days",
    },
    {
      icon: <TrendingUpIcon className="h-6 w-6 text-white" />,
      title: "Growth Rate",
      value: "156%",
      subtitle: "Year over year",
      trend: { value: "+43%", isPositive: true },
    },
  ];

  return (
    <div className="space-y-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="text-center"
      >
        <h2 className="text-4xl font-bold text-white mb-4">
          Tournament Platform Statistics
        </h2>
        <p className="text-emerald-100 text-lg max-w-2xl mx-auto">
          Join the fastest-growing sports tournament platform in India. 
          See how our community is making sports more accessible and competitive.
        </p>
      </motion.div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard
            key={stat.title}
            icon={stat.icon}
            title={stat.title}
            value={stat.value}
            subtitle={stat.subtitle}
            trend={stat.trend}
            delay={index * 0.1}
          />
        ))}
      </div>

      {/* Additional Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12"
      >
        <Card className="bg-white/10 backdrop-blur-sm border-white/20">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">95%</div>
            <div className="text-emerald-100 text-sm">Tournament Completion Rate</div>
            <div className="text-emerald-200 text-xs mt-1">Industry leading</div>
          </CardContent>
        </Card>

        <Card className="bg-white/10 backdrop-blur-sm border-white/20">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">4.8★</div>
            <div className="text-emerald-100 text-sm">Average Tournament Rating</div>
            <div className="text-emerald-200 text-xs mt-1">From 15K+ reviews</div>
          </CardContent>
        </Card>

        <Card className="bg-white/10 backdrop-blur-sm border-white/20">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">72%</div>
            <div className="text-emerald-100 text-sm">Player Return Rate</div>
            <div className="text-emerald-200 text-xs mt-1">Players join multiple tournaments</div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Real-time Activity Feed */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.6 }}
        className="mt-12"
      >
        <Card className="bg-white/10 backdrop-blur-sm border-white/20">
          <CardContent className="p-6">
            <h3 className="text-xl font-bold text-white mb-4 text-center">
              Live Tournament Activity
            </h3>
            <div className="space-y-3">
              {[
                "🏏 Mumbai Cricket Championship - Final match starting in 10 minutes",
                "⚽ Delhi Football League - Team Dynamos just scored! 2-1",
                "🏸 Bangalore Badminton Open - Registration closing in 2 hours",
                "🎾 Chennai Tennis Masters - Quarterfinals live now",
                "🏏 Pune Cricket Cup - New tournament announced for next month",
              ].map((activity, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                  className="text-emerald-100 text-sm py-2 px-3 bg-white/5 rounded-lg"
                >
                  {activity}
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

import React, { useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAccountActivation } from '@/hooks/useAccountActivation';

const LegalComplianceManager: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { activationStatus, checkAccountActivation } = useAccountActivation();



  // Check account activation status when user changes or when returning to legal agreements page
  useEffect(() => {
    if (user && !activationStatus.isLoading) {
      // Always check when user changes or when on legal agreements page
      if (!activationStatus.hasCheckedActivation || location.pathname === '/legal-agreements') {
        checkAccountActivation();
      }
    }
  }, [user, activationStatus.hasCheckedActivation, activationStatus.isLoading, location.pathname, checkAccountActivation]);

  // Redirect to legal agreements page if user is authenticated and account is not activated
  useEffect(() => {
    if (user && activationStatus.hasCheckedActivation && !activationStatus.isActivated) {
      // Don't redirect if already on the legal agreements page
      if (location.pathname !== '/legal-agreements') {
        console.log('LegalComplianceManager: Redirecting to legal agreements page');
        navigate('/legal-agreements');
      }
    }
  }, [user, activationStatus.hasCheckedActivation, activationStatus.isActivated, location.pathname, navigate]);

  // This component now redirects to the legal agreements page instead of showing a modal
  return null;
};

export default LegalComplianceManager;

import React, { useState } from 'react';
import { X, Mail, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { validateEmail, sanitizeInput, isTempEmail } from '@/utils/security';

interface EmailVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEmailVerified: () => void;
  currentEmail?: string;
  userName?: string;
  userId?: string;
}

const EmailVerificationModal: React.FC<EmailVerificationModalProps> = ({
  isOpen,
  onClose,
  onEmailVerified,
  currentEmail,
  userName,
  userId
}) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [emailError, setEmailError] = useState('');

  // Check if current email is temporary
  const isCurrentEmailTemp = isTempEmail(currentEmail);

  const handleSendVerificationEmail = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    if (email.toLowerCase() === currentEmail?.toLowerCase()) {
      setEmailError('Please enter a different email address');
      return;
    }

    setIsLoading(true);
    setEmailError('');

    try {
      const sanitizedEmail = sanitizeInput(email);

      // Send verification email using the existing WhatsApp email verification system
      const { data, error } = await supabase.functions.invoke('send-whatsapp-email-verification', {
        body: {
          email: sanitizedEmail,
          userId: userId,
          userName: userName || 'User'
        }
      });

      let result;
      if (error) {
        result = { success: false, error: error.message };
      } else {
        result = data;
      }

      if (result.success) {
        setEmailSent(true);
        toast({
          title: "Verification email sent!",
          description: `We've sent a verification link to ${sanitizedEmail}`,
        });
      } else {
        setEmailError(result.error || 'Failed to send verification email');
        toast({
          title: "Failed to send email",
          description: result.error || 'Please try again.',
          variant: "destructive",
        });
      }
    } catch (error) {
      setEmailError('An unexpected error occurred');
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setEmail('');
    setEmailSent(false);
    setEmailError('');
    onClose();
  };

  const handleEmailVerified = () => {
    onEmailVerified();
    handleClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-black via-[#1E3B2C] to-black rounded-xl shadow-2xl max-w-md w-full border border-[#2E7D32]/30">
        {/* Header */}
        <div className="p-6 border-b border-[#2E7D32]/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-[#2E7D32]/20 rounded-full flex items-center justify-center">
                <Mail className="h-5 w-5 text-[#2E7D32]" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-white">
                  {isCurrentEmailTemp ? 'Add Email Address' : 'Update Email Address'}
                </h2>
                <p className="text-sm text-gray-400">
                  Verify your email for booking confirmations
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {!emailSent ? (
            <>
              {isCurrentEmailTemp && (
                <div className="mb-4 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="h-4 w-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm text-yellow-200">
                        You're currently using a temporary email address. Add your real email to receive booking confirmations and important updates.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <form onSubmit={handleSendVerificationEmail} className="space-y-4">
                <div>
                  <label htmlFor="email-verification" className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-4 w-4 text-[#2E7D32]" />
                    </div>
                    <input
                      id="email-verification"
                      type="email"
                      value={email}
                      onChange={(e) => {
                        setEmail(e.target.value);
                        setEmailError('');
                      }}
                      className={`pl-10 w-full p-3 border ${emailError ? 'border-red-500' : 'border-[#2E7D32]/30'} bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-[#2E7D32] transition-all text-sm`}
                      placeholder="Enter your email address"
                      required
                      maxLength={254}
                    />
                  </div>
                  {emailError && <p className="text-red-400 text-xs mt-1">{emailError}</p>}
                  <p className="text-[#2E7D32] text-xs mt-1">📧 We'll send a verification link to this email</p>
                </div>

                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full py-3 px-4 bg-gradient-to-r from-[#2E7D32] to-green-600 hover:from-green-600 hover:to-[#2E7D32] text-white rounded-md font-medium transition-all flex justify-center items-center transform hover:scale-[1.02] shadow-lg disabled:opacity-50"
                >
                  {isLoading ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending...
                    </span>
                  ) : (
                    <span className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Send Verification Email
                    </span>
                  )}
                </button>
              </form>
            </>
          ) : (
            <div className="text-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">
                Verification Email Sent!
              </h3>
              <p className="text-gray-300 text-sm mb-4">
                We've sent a verification link to <span className="text-[#2E7D32] font-medium">{email}</span>
              </p>
              
              <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4 mb-6">
                <p className="text-green-300 text-sm">
                  ✅ Check your email and click the verification link to complete the process
                </p>
              </div>

              <button
                onClick={handleClose}
                className="w-full py-3 px-4 bg-gradient-to-r from-[#2E7D32] to-green-600 hover:from-green-600 hover:to-[#2E7D32] text-white rounded-md font-medium transition-all flex justify-center items-center transform hover:scale-[1.02] shadow-lg"
              >
                <span className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Got it!
                </span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationModal;

import React from 'react';
import { motion } from 'framer-motion';
import { Clock, Users, AlertCircle } from 'lucide-react';

interface TimeSlot {
  start_time: string;
  end_time: string;
  price: string;
  is_available: boolean;
  booking_type: 'court_based' | 'capacity_based';
  available_spots?: number;
  total_capacity?: number;
}

interface MobileSlotGridProps {
  slots: TimeSlot[];
  selectedSlots: string[];
  onSlotClick: (slot: TimeSlot, event?: React.MouseEvent) => void;
  formatTime: (time: string) => string;
  getSelectableSlots: (currentSlots: string[], availableSlots: TimeSlot[]) => string[];
  loading?: boolean;
}

const MobileSlotGrid: React.FC<MobileSlotGridProps> = ({
  slots,
  selectedSlots,
  onSlotClick,
  formatTime,
  getSelectableSlots,
  loading = false
}) => {
  
  const selectableSlots = getSelectableSlots(selectedSlots, slots);
  
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <Clock className="text-emerald-400" size={32} />
        </motion.div>
        <p className="mt-4 text-gray-400">Loading available slots...</p>
      </div>
    );
  }

  if (slots.length === 0) {
    return (
      <div className="text-center py-12 bg-gray-800/30 rounded-xl border border-gray-700">
        <AlertCircle className="mx-auto text-gray-400 mb-4" size={48} />
        <h3 className="text-lg font-medium text-gray-300 mb-2">No Slots Available</h3>
        <p className="text-gray-400">No time slots are available for the selected date.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Mobile-optimized grid - single column with larger touch targets */}
      <div className="grid grid-cols-1 gap-3">
        {slots.map((slot, index) => {
          const slotDisplay = `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`;
          const isSelected = selectedSlots.includes(slotDisplay);
          const isSelectable = slot.is_available && (selectedSlots.length === 0 || selectableSlots.includes(slotDisplay));
          const isDisabledByRule = slot.is_available && !isSelected && !isSelectable;
          
          return (
            <motion.button
              key={`${slot.start_time}-${slot.end_time}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              whileHover={{ scale: isSelectable ? 1.02 : 1 }}
              whileTap={{ scale: isSelectable ? 0.98 : 1 }}
              disabled={!isSelectable}
              onClick={(e) => onSlotClick(slot, e)}
              className={`
                relative p-4 rounded-xl border-2 transition-all duration-200 text-left
                min-h-[80px] flex flex-col justify-between
                ${!slot.is_available
                  ? 'bg-red-900/20 border-red-800/50 text-red-300 cursor-not-allowed'
                  : isSelected
                    ? 'bg-emerald-600/20 border-emerald-500 text-emerald-100 shadow-lg shadow-emerald-900/20'
                    : isDisabledByRule
                      ? 'bg-gray-800/30 border-gray-700/50 text-gray-500 cursor-not-allowed opacity-50'
                      : 'bg-gray-800/50 border-gray-600/50 text-gray-200 hover:border-emerald-500/50 hover:bg-gray-700/50 active:bg-gray-600/50'
                }
              `}
            >
              {/* Time and Price Row */}
              <div className="flex justify-between items-start">
                <div>
                  <div className="font-semibold text-lg">
                    {formatTime(slot.start_time)}
                  </div>
                  <div className="text-sm opacity-75">
                    to {formatTime(slot.end_time)}
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="font-bold text-lg">
                    ₹{parseFloat(slot.price).toFixed(0)}
                  </div>
                  {slot.booking_type === 'capacity_based' && (
                    <div className="text-xs opacity-75 flex items-center gap-1">
                      <Users size={12} />
                      {slot.available_spots || 0}/{slot.total_capacity || 0}
                    </div>
                  )}
                </div>
              </div>

              {/* Status Indicators */}
              <div className="flex justify-between items-center mt-2">
                <div className="flex items-center gap-2">
                  {/* Selection indicator */}
                  <div className={`w-3 h-3 rounded-full transition-all ${
                    isSelected 
                      ? 'bg-emerald-400 shadow-lg shadow-emerald-400/50' 
                      : slot.is_available 
                        ? 'bg-gray-600 border border-gray-500' 
                        : 'bg-red-600'
                  }`} />
                  
                  {/* Status text */}
                  <span className="text-xs font-medium">
                    {!slot.is_available 
                      ? 'Booked' 
                      : isSelected 
                        ? 'Selected' 
                        : isDisabledByRule 
                          ? 'Not consecutive'
                          : 'Available'
                    }
                  </span>
                </div>

                {/* Booking type indicator */}
                {slot.booking_type === 'capacity_based' && (
                  <div className="text-xs bg-blue-900/30 text-blue-300 px-2 py-1 rounded-full">
                    Capacity
                  </div>
                )}
              </div>

              {/* Visual indicator for non-consecutive slots */}
              {isDisabledByRule && selectedSlots.length > 0 && (
                <div className="absolute top-2 right-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
                </div>
              )}

              {/* Selection animation overlay */}
              {isSelected && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute inset-0 bg-emerald-500/10 rounded-xl pointer-events-none"
                />
              )}
            </motion.button>
          );
        })}
      </div>

      {/* Mobile-optimized legend */}
      <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700/50">
        <h4 className="text-sm font-medium text-gray-300 mb-3">Legend</h4>
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-emerald-400 rounded-full" />
            <span className="text-gray-300">Selected</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-gray-600 border border-gray-500 rounded-full" />
            <span className="text-gray-300">Available</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-600 rounded-full" />
            <span className="text-gray-300">Booked</span>
          </div>
          {selectedSlots.length > 0 && slots.some(slot => slot.booking_type === 'court_based') && (
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-gray-600 rounded-full opacity-50" />
              <span className="text-gray-300">Non-consecutive</span>
            </div>
          )}
        </div>
      </div>

      {/* Consecutive slot selection tip for mobile */}
      {slots.some(slot => slot.booking_type === 'court_based') && (
        <div className="bg-blue-900/20 rounded-xl p-4 border border-blue-800/30">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-bold">i</span>
            </div>
            <div>
              <h4 className="text-sm font-medium text-blue-300 mb-1">
                Consecutive Slots Only
              </h4>
              <p className="text-xs text-gray-400 mb-2">
                You can select multiple time slots, but they must be consecutive (no gaps).
              </p>
              <p className="text-xs text-blue-300">
                💡 <strong>Tip:</strong> Long press and drag to select multiple slots on mobile
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileSlotGrid;

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  MapPin, 
  Activity, 
  Calendar, 
  User, 
  Phone, 
  Tag,
  ChevronDown,
  Check,
  AlertCircle
} from 'lucide-react';
import { SecurityUtils } from '@/utils/securityUtils';

interface MobileBookingFormProps {
  venues: any[];
  sports: any[];
  courts: any[];
  selectedVenue: string;
  selectedSport: string;
  selectedCourt: string;
  selectedDate: string;
  name: string;
  phone: string;
  couponCode: string;
  onVenueChange: (venueId: string) => void;
  onSportChange: (sportId: string) => void;
  onCourtChange: (courtId: string) => void;
  onDateChange: (date: string) => void;
  onNameChange: (name: string) => void;
  onPhoneChange: (phone: string) => void;
  onCouponChange: (code: string) => void;
  loading?: {
    venues?: boolean;
    sports?: boolean;
    courts?: boolean;
  };
}

const MobileBookingForm: React.FC<MobileBookingFormProps> = ({
  venues,
  sports,
  courts,
  selectedVenue,
  selectedSport,
  selectedCourt,
  selectedDate,
  name,
  phone,
  couponCode,
  onVenueChange,
  onSportChange,
  onCourtChange,
  onDateChange,
  onNameChange,
  onPhoneChange,
  onCouponChange,
  loading = {}
}) => {
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    venue: true,
    sport: false,
    court: false,
    date: false,
    personal: false
  });

  const validateField = (field: string, value: string) => {
    const errors = { ...validationErrors };
    
    switch (field) {
      case 'name':
        const nameValidation = SecurityUtils.validateName(value);
        if (!nameValidation.valid && value) {
          errors.name = nameValidation.error!;
        } else {
          delete errors.name;
        }
        break;
      case 'phone':
        const phoneValidation = SecurityUtils.validatePhoneNumber(value);
        if (!phoneValidation.valid && value) {
          errors.phone = phoneValidation.error!;
        } else {
          delete errors.phone;
        }
        break;
      case 'coupon':
        const couponValidation = SecurityUtils.validateCouponCode(value);
        if (!couponValidation.valid && value) {
          errors.coupon = couponValidation.error!;
        } else {
          delete errors.coupon;
        }
        break;
    }
    
    setValidationErrors(errors);
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const getSectionStatus = (section: string): 'complete' | 'incomplete' | 'disabled' => {
    switch (section) {
      case 'venue':
        return selectedVenue ? 'complete' : 'incomplete';
      case 'sport':
        return selectedSport ? 'complete' : selectedVenue ? 'incomplete' : 'disabled';
      case 'court':
        return selectedCourt ? 'complete' : (selectedVenue && selectedSport) ? 'incomplete' : 'disabled';
      case 'date':
        return selectedDate ? 'complete' : 'incomplete';
      case 'personal':
        return (name && phone) ? 'complete' : 'incomplete';
      default:
        return 'incomplete';
    }
  };

  const FormSection: React.FC<{
    title: string;
    icon: React.ReactNode;
    section: string;
    children: React.ReactNode;
    required?: boolean;
  }> = ({ title, icon, section, children, required = true }) => {
    const status = getSectionStatus(section);
    const isExpanded = expandedSections[section];
    const isDisabled = status === 'disabled';

    return (
      <div className={`border rounded-xl overflow-hidden transition-all ${
        isDisabled 
          ? 'border-gray-700 bg-gray-800/30' 
          : status === 'complete'
            ? 'border-emerald-600/50 bg-emerald-900/10'
            : 'border-gray-600 bg-gray-800/50'
      }`}>
        <button
          onClick={() => !isDisabled && toggleSection(section)}
          disabled={isDisabled}
          className={`w-full p-4 flex items-center justify-between text-left transition-colors ${
            isDisabled 
              ? 'cursor-not-allowed' 
              : 'hover:bg-gray-700/50 active:bg-gray-600/50'
          }`}
        >
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${
              isDisabled 
                ? 'bg-gray-700 text-gray-500'
                : status === 'complete'
                  ? 'bg-emerald-600 text-white'
                  : 'bg-gray-600 text-gray-300'
            }`}>
              {status === 'complete' ? <Check size={16} /> : icon}
            </div>
            <div>
              <h3 className={`font-medium ${
                isDisabled ? 'text-gray-500' : 'text-white'
              }`}>
                {title}
                {required && <span className="text-red-400 ml-1">*</span>}
              </h3>
              <p className={`text-xs ${
                isDisabled 
                  ? 'text-gray-600' 
                  : status === 'complete'
                    ? 'text-emerald-400'
                    : 'text-gray-400'
              }`}>
                {status === 'complete' 
                  ? 'Completed' 
                  : isDisabled 
                    ? 'Complete previous steps first'
                    : 'Tap to configure'
                }
              </p>
            </div>
          </div>
          
          {!isDisabled && (
            <motion.div
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown size={20} className="text-gray-400" />
            </motion.div>
          )}
        </button>

        <motion.div
          initial={false}
          animate={{ height: isExpanded ? 'auto' : 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="overflow-hidden"
        >
          <div className="p-4 pt-0 border-t border-gray-700/50">
            {children}
          </div>
        </motion.div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Venue Selection */}
      <FormSection
        title="Select Venue"
        icon={<MapPin size={16} />}
        section="venue"
      >
        {loading.venues ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-400" />
            <span className="ml-2 text-gray-400">Loading venues...</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-2">
            {venues.map((venue) => (
              <button
                key={venue.id}
                onClick={() => onVenueChange(venue.id)}
                className={`p-3 rounded-lg border text-left transition-all ${
                  selectedVenue === venue.id
                    ? 'border-emerald-500 bg-emerald-900/20 text-emerald-100'
                    : 'border-gray-600 bg-gray-700/50 text-gray-200 hover:border-gray-500'
                }`}
              >
                <div className="font-medium">{venue.name}</div>
                {venue.address && (
                  <div className="text-xs text-gray-400 mt-1">{venue.address}</div>
                )}
              </button>
            ))}
          </div>
        )}
      </FormSection>

      {/* Sport Selection */}
      <FormSection
        title="Select Sport"
        icon={<Activity size={16} />}
        section="sport"
      >
        {loading.sports ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-400" />
            <span className="ml-2 text-gray-400">Loading sports...</span>
          </div>
        ) : sports.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            <AlertCircle className="mx-auto mb-2" size={24} />
            No sports available for this venue
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-2">
            {sports.map((sport) => (
              <button
                key={sport.id}
                onClick={() => onSportChange(sport.id)}
                className={`p-3 rounded-lg border text-center transition-all ${
                  selectedSport === sport.id
                    ? 'border-emerald-500 bg-emerald-900/20 text-emerald-100'
                    : 'border-gray-600 bg-gray-700/50 text-gray-200 hover:border-gray-500'
                }`}
              >
                <div className="font-medium text-sm">{sport.name}</div>
              </button>
            ))}
          </div>
        )}
      </FormSection>

      {/* Court Selection */}
      <FormSection
        title="Select Court"
        icon={<MapPin size={16} />}
        section="court"
      >
        {loading.courts ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-400" />
            <span className="ml-2 text-gray-400">Loading courts...</span>
          </div>
        ) : courts.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            <AlertCircle className="mx-auto mb-2" size={24} />
            No courts available for this sport
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-2">
            {courts.map((court) => (
              <button
                key={court.id}
                onClick={() => onCourtChange(court.id)}
                className={`p-3 rounded-lg border text-left transition-all ${
                  selectedCourt === court.id
                    ? 'border-emerald-500 bg-emerald-900/20 text-emerald-100'
                    : 'border-gray-600 bg-gray-700/50 text-gray-200 hover:border-gray-500'
                }`}
              >
                <div className="font-medium">{court.name}</div>
                {court.description && (
                  <div className="text-xs text-gray-400 mt-1">{court.description}</div>
                )}
              </button>
            ))}
          </div>
        )}
      </FormSection>

      {/* Date Selection */}
      <FormSection
        title="Select Date"
        icon={<Calendar size={16} />}
        section="date"
      >
        <input
          type="date"
          value={selectedDate}
          onChange={(e) => onDateChange(e.target.value)}
          min={new Date().toISOString().split('T')[0]}
          className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-white min-h-[48px] touch-manipulation"
          style={{ fontSize: '16px', WebkitTapHighlightColor: 'transparent' }}
        />
      </FormSection>

      {/* Personal Information */}
      <FormSection
        title="Personal Information"
        icon={<User size={16} />}
        section="personal"
        required={false}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Full Name
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => {
                const sanitized = SecurityUtils.sanitizeInput(e.target.value);
                onNameChange(sanitized);
                validateField('name', sanitized);
              }}
              placeholder="Enter your full name"
              className={`w-full p-3 bg-gray-700 border rounded-lg focus:ring-2 focus:ring-emerald-500 text-white min-h-[48px] touch-manipulation ${
                validationErrors.name ? 'border-red-500' : 'border-gray-600'
              }`}
              style={{ fontSize: '16px', WebkitTapHighlightColor: 'transparent' }}
            />
            {validationErrors.name && (
              <div className="mobile-error-message bg-red-900/20 border border-red-800/50 rounded-lg p-2 mt-2">
                <p className="text-red-300 text-sm flex items-center gap-2">
                  <AlertCircle size={14} className="flex-shrink-0" />
                  <span>{validationErrors.name}</span>
                </p>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              value={phone}
              onChange={(e) => {
                const sanitized = e.target.value.replace(/\D/g, '').substring(0, 10);
                onPhoneChange(sanitized);
                validateField('phone', sanitized);
              }}
              placeholder="10-digit mobile number"
              className={`w-full p-3 bg-gray-700 border rounded-lg focus:ring-2 focus:ring-emerald-500 text-white min-h-[48px] touch-manipulation ${
                validationErrors.phone ? 'border-red-500' : 'border-gray-600'
              }`}
              style={{ fontSize: '16px', WebkitTapHighlightColor: 'transparent' }}
            />
            {validationErrors.phone && (
              <div className="mobile-error-message bg-red-900/20 border border-red-800/50 rounded-lg p-2 mt-2">
                <p className="text-red-300 text-sm flex items-center gap-2">
                  <AlertCircle size={14} className="flex-shrink-0" />
                  <span>{validationErrors.phone}</span>
                </p>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Coupon Code (Optional)
            </label>
            <input
              type="text"
              value={couponCode}
              onChange={(e) => {
                const sanitized = SecurityUtils.sanitizeInput(e.target.value.toUpperCase());
                onCouponChange(sanitized);
                validateField('coupon', sanitized);
              }}
              placeholder="Enter coupon code"
              className={`w-full p-3 bg-gray-700 border rounded-lg focus:ring-2 focus:ring-emerald-500 text-white min-h-[48px] touch-manipulation ${
                validationErrors.coupon ? 'border-red-500' : 'border-gray-600'
              }`}
              style={{ fontSize: '16px', WebkitTapHighlightColor: 'transparent' }}
            />
            {validationErrors.coupon && (
              <div className="mobile-error-message bg-red-900/20 border border-red-800/50 rounded-lg p-2 mt-2">
                <p className="text-red-300 text-sm flex items-center gap-2">
                  <AlertCircle size={14} className="flex-shrink-0" />
                  <span>{validationErrors.coupon}</span>
                </p>
              </div>
            )}
          </div>
        </div>
      </FormSection>
    </div>
  );
};

export default MobileBookingForm;

import { motion } from "framer-motion";
import React, { useState, useEffect, useCallback } from "react";
import { AuroraBackground } from "@/components/ui/aurora-background";
import { ArrowRight, Activity, MapPin, TrendingUp, Calendar, Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Typewriter } from '@/components/Typewriter';

import { useEnhancedLocation } from '@/hooks/use-enhanced-location';
import { geocoding } from '@/utils/geocoding';
import { googleMapsService } from '@/utils/google-maps';
import { Address } from '@/types/location';



// SVG silhouettes for swimming, football, badminton, basketball, tennis, cricket, volleyball, running
const silhouettes = [
  // Swimming
  <svg key="swimming" viewBox="0 0 64 32" className="absolute left-2 top-4 w-28 h-10 sm:w-36 sm:h-16 md:w-48 md:h-24 opacity-40 text-emerald-500 animate-float-slow" fill="none"><path d="M8 28c4 0 8-4 12-4s8 4 12 4 8-4 12-4 8 4 12 4" stroke="currentColor" strokeWidth="3" strokeLinecap="round" fill="none"/><ellipse cx="32" cy="16" rx="6" ry="2" fill="currentColor"/><path d="M28 14c-2-4 4-8 8-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" fill="none"/></svg>,
  // Football (soccer)
  <svg key="football" viewBox="0 0 64 64" className="absolute right-2 top-16 w-20 h-20 sm:w-32 sm:h-32 md:w-40 md:h-40 opacity-35 text-emerald-400 animate-float-medium" fill="none"><circle cx="32" cy="32" r="16" stroke="currentColor" strokeWidth="3" fill="none"/><circle cx="32" cy="32" r="6" fill="currentColor"/><path d="M32 16v12m0 8v12m-12-12h12m8 0h12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/></svg>,
  // Badminton
  <svg key="badminton" viewBox="0 0 64 64" className="absolute left-1/2 bottom-4 w-20 h-20 sm:w-28 sm:h-28 md:w-36 md:h-36 opacity-30 text-emerald-600 animate-float-fast" fill="none"><ellipse cx="48" cy="16" rx="4" ry="8" transform="rotate(30 48 16)" fill="currentColor"/><rect x="14" y="44" width="24" height="4" rx="2" transform="rotate(-30 14 44)" fill="currentColor"/><rect x="36" y="12" width="4" height="24" rx="2" transform="rotate(30 36 12)" fill="currentColor"/></svg>,
  // Basketball
  <svg key="basketball" viewBox="0 0 64 64" className="absolute left-8 bottom-20 w-16 h-16 sm:w-24 sm:h-24 md:w-32 md:h-32 opacity-40 text-emerald-700 animate-float-medium" fill="none"><circle cx="32" cy="32" r="16" stroke="currentColor" strokeWidth="3" fill="none"/><path d="M16 32c8 0 16 8 32 0" stroke="currentColor" strokeWidth="2"/><path d="M32 16c0 8 0 24 0 32" stroke="currentColor" strokeWidth="2"/></svg>,
  // Tennis
  <svg key="tennis" viewBox="0 0 64 64" className="absolute right-16 top-4 w-14 h-14 sm:w-20 sm:h-20 md:w-28 md:h-28 opacity-35 text-emerald-300 animate-float-fast" fill="none"><ellipse cx="48" cy="16" rx="8" ry="4" fill="currentColor"/><rect x="14" y="44" width="24" height="4" rx="2" transform="rotate(-30 14 44)" fill="currentColor"/></svg>,
  // Cricket
  <svg key="cricket" viewBox="0 0 64 64" className="absolute left-2 bottom-2 w-16 h-16 sm:w-24 sm:h-24 md:w-32 md:h-32 opacity-40 text-emerald-400 animate-float-slow" fill="none"><rect x="40" y="10" width="4" height="36" rx="2" transform="rotate(20 40 10)" fill="currentColor"/><circle cx="20" cy="54" r="6" fill="currentColor"/></svg>,
  // Volleyball
  <svg key="volleyball" viewBox="0 0 64 64" className="absolute right-8 bottom-8 w-14 h-14 sm:w-20 sm:h-20 md:w-28 md:h-28 opacity-35 text-emerald-500 animate-float-medium" fill="none"><circle cx="32" cy="32" r="14" stroke="currentColor" strokeWidth="3" fill="none"/><path d="M18 32c8-8 20-8 28 0" stroke="currentColor" strokeWidth="2"/><path d="M32 18c0 8 0 20 0 28" stroke="currentColor" strokeWidth="2"/></svg>,
  // Running
  <svg key="running" viewBox="0 0 64 64" className="absolute right-1/3 top-1/2 w-20 h-10 sm:w-28 sm:h-14 md:w-36 md:h-20 opacity-40 text-emerald-600 animate-float-fast" fill="none"><path d="M20 40c4-8 12-8 16 0" stroke="currentColor" strokeWidth="3" strokeLinecap="round" fill="none"/><circle cx="32" cy="24" r="6" fill="currentColor"/><path d="M32 30v10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/></svg>
];

export function AuroraBackgroundDemo() {
  const navigate = useNavigate();
  const animatedWords = ["Game on!", "Match!", "Court!", "Training!", "Tournament!"];

  // Real-time stats (in a real app, these would come from API)
  const [stats, setStats] = useState({
    venues: 1247,
    bookingsToday: 342,
    sportsAvailable: 15
  });



  // Location search state
  const [locationQuery, setLocationQuery] = useState('');
  const [locationSuggestions, setLocationSuggestions] = useState<Address[]>([]);
  const [isLocationSearching, setIsLocationSearching] = useState(false);
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const [useGoogleMaps, setUseGoogleMaps] = useState(true);



  // Enhanced location search with Google Maps Places Autocomplete and fallback
  const handleLocationSearch = useCallback(async (query: string) => {
    setLocationQuery(query);

    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    if (query.length < 2) {
      setLocationSuggestions([]);
      setShowLocationSuggestions(false);
      return;
    }

    // Debounce search requests
    const timeout = setTimeout(async () => {
      setIsLocationSearching(true);

      try {
        let suggestions: Address[] = [];

        // Try Google Maps first if enabled
        if (useGoogleMaps) {
          try {
            suggestions = await googleMapsService.searchAddresses(query);

            // Log for debugging in development
            if (import.meta.env.DEV) {
              console.log('Google Maps search results:', suggestions.length);
            }
          } catch (error) {
            console.warn('Google Maps search failed, falling back to geocoding:', error);
            setUseGoogleMaps(false); // Disable for this session
          }
        }

        // Fallback to basic geocoding if Google Maps failed or returned no results
        if (suggestions.length === 0) {
          try {
            const delhiQuery = `${query}, New Delhi, India`;
            const geocodingSuggestions = await geocoding.searchAddresses(delhiQuery);

            // Filter results to only include New Delhi and surrounding NCR areas
            suggestions = geocodingSuggestions.filter(suggestion => {
              const displayName = suggestion.display_name?.toLowerCase() || '';
              const city = suggestion.city?.toLowerCase() || '';
              const area = suggestion.area?.toLowerCase() || '';
              const state = suggestion.state?.toLowerCase() || '';

              // Check if location is in Delhi/New Delhi metropolitan area
              const isDelhiArea =
                // Core Delhi areas
                city.includes('delhi') ||
                city.includes('new delhi') ||
                area.includes('delhi') ||
                area.includes('new delhi') ||
                displayName.includes('delhi') ||
                displayName.includes('new delhi') ||
                // NCR (National Capital Region) areas commonly served
                displayName.includes('gurgaon') ||
                displayName.includes('gurugram') ||
                displayName.includes('noida') ||
                displayName.includes('faridabad') ||
                displayName.includes('ghaziabad') ||
                // Delhi state/territory
                state.includes('delhi');

              return isDelhiArea && suggestion.country?.toLowerCase().includes('india');
            });

            if (import.meta.env.DEV) {
              console.log('Fallback geocoding results:', suggestions.length);
            }
          } catch (fallbackError) {
            console.error('Both Google Maps and fallback geocoding failed:', fallbackError);
          }
        }

        setLocationSuggestions(suggestions);
        setShowLocationSuggestions(suggestions.length > 0);
      } catch (error) {
        console.error('Location search failed:', error);
        setLocationSuggestions([]);
        setShowLocationSuggestions(false);
      } finally {
        setIsLocationSearching(false);
      }
    }, 300); // 300ms debounce

    setSearchTimeout(timeout);
  }, [searchTimeout, useGoogleMaps]);

  // Handle location selection with coordinates
  const handleLocationSelect = async (suggestion: Address) => {
    setLocationQuery(suggestion.city || suggestion.area || suggestion.display_name || '');
    setLocationSuggestions([]);
    setShowLocationSuggestions(false);

    // Get coordinates for the selected location
    let coordinates: { lat: number; lng: number } | null = null;

    try {
      // First, try to use coordinates from Google Maps result
      if (suggestion.coordinates) {
        coordinates = {
          lat: suggestion.coordinates.latitude,
          lng: suggestion.coordinates.longitude
        };

        if (import.meta.env.DEV) {
          console.log('Using Google Maps coordinates:', coordinates);
        }
      } else {
        // Fallback to geocoding if no coordinates available
        const geocodedCoords = await geocoding.forwardGeocode(suggestion.display_name);
        if (geocodedCoords) {
          coordinates = {
            lat: geocodedCoords.latitude,
            lng: geocodedCoords.longitude
          };

          if (import.meta.env.DEV) {
            console.log('Using geocoded coordinates:', coordinates);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to get coordinates for selected location:', error);
    }

    // Build navigation URL with location and coordinates
    const locationName = suggestion.area || suggestion.city || suggestion.display_name || '';
    let navigationUrl = `/venues?location=${encodeURIComponent(locationName)}`;

    if (coordinates) {
      navigationUrl += `&lat=${coordinates.lat}&lng=${coordinates.lng}`;
    }

    // Navigate to venues with location filter and coordinates
    navigate(navigationUrl);
  };



  // Initialize component and preload Google Maps
  useEffect(() => {


    // Preload Google Maps API for better performance
    if (useGoogleMaps) {
      googleMapsService.loadGoogleMapsAPI().catch(error => {
        console.warn('Failed to preload Google Maps API:', error);
        setUseGoogleMaps(false);
      });
    }

    // Simulate real-time stats updates
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        bookingsToday: prev.bookingsToday + Math.floor(Math.random() * 3)
      }));
    }, 10000); // Update every 10 seconds

    // Cleanup function
    return () => {
      clearInterval(interval);
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout, useGoogleMaps]);

  // Close location suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.location-search-container')) {
        setShowLocationSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <AuroraBackground className="bg-black dark:bg-black">
      {/* Real-Time Stats Banner */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.8 }}
        className="absolute top-0 left-0 right-0 z-20"
      >
        <div className="bg-emerald-900/20 backdrop-blur-sm border-b border-emerald-500/30">
          <div className="container mx-auto px-4 py-2">
            <div className="flex flex-wrap justify-center items-center gap-3 sm:gap-6 text-emerald-300 text-xs sm:text-sm">
              <div className="flex items-center gap-1.5">
                <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full animate-pulse"></div>
                <TrendingUp className="w-3.5 h-3.5" />
                <span className="font-medium">{stats.venues.toLocaleString()} venues</span>
              </div>
              <div className="flex items-center gap-1.5">
                <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full animate-pulse"></div>
                <Calendar className="w-3.5 h-3.5" />
                <span className="font-medium">{stats.bookingsToday} today</span>
              </div>
              <div className="flex items-center gap-1.5">
                <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full animate-pulse"></div>
                <Activity className="w-3.5 h-3.5" />
                <span className="font-medium">{stats.sportsAvailable}+ sports</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Animated sports silhouettes */}
      <div className="pointer-events-none select-none absolute inset-0 z-0">
        {silhouettes}
      </div>

      {/* Floating Action Indicators */}
      <div className="absolute inset-0 pointer-events-none z-5">
        <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-emerald-400 rounded-full animate-ping opacity-60"></div>
        <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-emerald-500 rounded-full animate-pulse opacity-40"></div>
        <div className="absolute bottom-1/4 left-1/3 w-4 h-4 bg-emerald-300 rounded-full animate-bounce opacity-30"></div>

        {/* Grid pattern overlay */}
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2310b981' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}></div>
      </div>
      <motion.div
        initial={{ opacity: 0.0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{
          delay: 0.3,
          duration: 0.8,
          ease: "easeInOut",
        }}
        className="relative flex flex-col gap-4 sm:gap-6 items-center justify-center px-4 z-10 pt-28 pb-8 sm:pt-40 sm:pb-12"
      >
        {/* Animated headline with blur/fade/emerald highlight */}
        <motion.h1
          initial={{ filter: "blur(8px)", opacity: 0 }}
          animate={{ filter: "blur(0px)", opacity: 1 }}
          transition={{ duration: 1.2, ease: "easeOut" }}
          className="text-3xl sm:text-5xl md:text-7xl font-extrabold text-center text-white drop-shadow-lg relative z-20"
        >
          <span className="text-emerald-300 drop-shadow-lg">
            Book now for your
          </span>
        </motion.h1>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.8, ease: "easeOut" }}
          className="text-3xl sm:text-5xl md:text-7xl font-extrabold text-center text-white w-full flex justify-center relative z-20"
        >
          {/* Emerald blurred glow background */}
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[400px] h-[100px] bg-emerald-700/40 rounded-full blur-2xl z-0 pointer-events-none"></div>
          <span className="inline-block min-w-[15ch] text-center relative z-10 text-emerald-400 drop-shadow-lg">
            <Typewriter
              texts={animatedWords}
              delay={130}
            />
          </span>
        </motion.div>
        {/* Animated subheadline with shimmer/typewriter */}
        <motion.div
          initial={{ opacity: 0, x: -30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.8, duration: 1, ease: "easeOut" }}
          className="font-light text-base sm:text-lg md:text-2xl text-emerald-200 py-2 text-center max-w-2xl animate-shimmer"
        >
          Discover, book, and play at the best venues. <span className="text-emerald-400 font-semibold">Fast. Easy. Anywhere.</span>
        </motion.div>



        {/* Smart Location Search */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0, duration: 0.8 }}
          className="w-full max-w-md mx-auto mb-4 sm:mb-6 relative location-search-container"
        >
          <div className="relative">
            <input
              type="text"
              placeholder="Enter your location in New Delhi..."
              value={locationQuery}
              onChange={(e) => handleLocationSearch(e.target.value)}
              className="w-full px-4 py-3 pl-12 pr-20 bg-black/30 backdrop-blur-sm border border-emerald-600/50 rounded-full text-white placeholder-emerald-300/70 focus:outline-none focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all min-h-[44px]"
            />
            <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-emerald-400" />
            {isLocationSearching ? (
              <Loader2 className="absolute right-12 top-1/2 transform -translate-y-1/2 w-4 h-4 animate-spin text-emerald-400" />
            ) : (
              <button
                onClick={() => locationQuery && handleLocationSearch(locationQuery)}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-emerald-600 hover:bg-emerald-700 px-4 py-1.5 rounded-full text-white text-sm font-medium transition-colors min-h-[36px]"
              >
                Find
              </button>
            )}
          </div>

          {/* Location Suggestions Dropdown */}
          {showLocationSuggestions && locationSuggestions.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="absolute top-full left-0 right-0 mt-2 bg-black/90 backdrop-blur-sm border border-emerald-600/50 rounded-lg shadow-lg z-50 max-h-48 overflow-y-auto"
            >
              {locationSuggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleLocationSelect(suggestion)}
                  className="w-full text-left px-4 py-3 text-white hover:bg-emerald-900/30 transition-colors border-b border-emerald-600/20 last:border-b-0 min-h-[44px] flex items-center"
                >
                  <MapPin className="w-4 h-4 text-emerald-400 mr-3 flex-shrink-0" />
                  <div className="flex-grow min-w-0">
                    <div className="text-sm font-medium truncate">
                      {suggestion.area || suggestion.city || 'Unknown Location'}
                    </div>
                    <div className="text-xs text-emerald-300/70 truncate">
                      {suggestion.city && suggestion.area !== suggestion.city ? `${suggestion.city}, ` : ''}
                      New Delhi, India
                    </div>
                  </div>
                </button>
              ))}
            </motion.div>
          )}
        </motion.div>
        {/* Enhanced CTA Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.8 }}
          className="flex flex-col sm:flex-row gap-2 sm:gap-3 items-center mt-3 sm:mt-4 w-full max-w-sm px-4"
        >
          <motion.button
            whileHover={{
              scale: 1.08,
              boxShadow: "0 0 30px rgba(16, 185, 129, 0.4)",
              backgroundColor: "rgba(16, 185, 129, 0.9)"
            }}
            whileTap={{ scale: 0.97 }}
            className="group bg-gradient-to-r from-emerald-800 to-emerald-700 hover:from-emerald-700 hover:to-emerald-600 text-white rounded-full px-6 py-3 text-base sm:text-lg font-semibold flex items-center justify-center gap-2 shadow-xl border-2 border-emerald-900/60 relative overflow-hidden w-full sm:w-auto min-h-[48px]"
            onClick={() => navigate('/venues')}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
            <span className="relative z-10">Book Now</span>
            <ArrowRight className="w-5 h-5 relative z-10 group-hover:translate-x-1 transition-transform" />
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.05, backgroundColor: "rgba(16, 185, 129, 0.1)" }}
            whileTap={{ scale: 0.95 }}
            className="bg-transparent border-2 border-emerald-700 text-emerald-300 hover:bg-emerald-900/30 rounded-full px-6 py-3 text-base sm:text-lg font-semibold flex items-center justify-center gap-2 shadow-lg backdrop-blur-sm relative w-full sm:w-auto min-h-[48px]"
            onClick={() => navigate("/challenge")}
          >
            <Activity className="w-5 h-5" />
            <span>Challenge Mode</span>
            <span className="text-xs bg-emerald-600 px-1.5 py-0.5 rounded-full ml-1 animate-pulse">BETA</span>
          </motion.button>
        </motion.div>
      </motion.div>


    </AuroraBackground>
  );
}

// Tailwind custom animations (add to tailwind.config.js):
// animation: {
//   'float-slow': 'float 8s ease-in-out infinite',
//   'float-medium': 'float 5s ease-in-out infinite',
//   'float-fast': 'float 3s ease-in-out infinite',
//   'gradient-x': 'gradient-x 8s ease-in-out infinite',
//   'text-blur': 'text-blur 2s ease-in-out infinite',
//   'shimmer': 'shimmer 2s linear infinite',
// },
// keyframes: {
//   float: { '0%, 100%': { transform: 'translateY(0)' }, '50%': { transform: 'translateY(-20px)' } },
//   'gradient-x': { '0%, 100%': { backgroundPosition: '0% 50%' }, '50%': { backgroundPosition: '100% 50%' } },
//   'text-blur': { '0%, 100%': { filter: 'blur(2px)' }, '50%': { filter: 'blur(0px)' } },
//   shimmer: { '100%': { backgroundPosition: '200% 0' } },
// }, 

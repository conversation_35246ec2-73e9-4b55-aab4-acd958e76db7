/**
 * 📊 NOTIFICATION ANALYTICS DASHBOARD
 * 
 * Displays comprehensive notification performance metrics for venue admins:
 * - Read rates and engagement analytics
 * - Delivery tracking and success rates
 * - Historical performance trends
 * - Subscriber insights and growth metrics
 */

import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  MessageCircle, 
  Eye, 
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'react-hot-toast';

interface NotificationAnalytic {
  template_id: string;
  venue_id: string;
  venue_name: string;
  title: string;
  message: string;
  sent_at: string;
  approved: boolean;
  approved_at: string | null;
  total_delivered: number;
  total_read: number;
  read_rate_percentage: number;
  unique_recipients: number;
}

interface SubscriberInsight {
  total_subscribers: number;
  new_subscribers_this_week: number;
  new_subscribers_this_month: number;
  active_subscribers: number;
  avg_engagement_rate: number;
  top_subscriber_locations: string[];
}

interface NotificationAnalyticsProps {
  venueId: string;
  venueName: string;
}

export function NotificationAnalytics({ venueId, venueName }: NotificationAnalyticsProps) {
  const [analytics, setAnalytics] = useState<NotificationAnalytic[]>([]);
  const [insights, setInsights] = useState<SubscriberInsight | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalytics();
    fetchSubscriberInsights();
  }, [venueId]);

  const fetchAnalytics = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_analytics')
        .select('*')
        .eq('venue_id', venueId)
        .order('sent_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      setAnalytics(data || []);
    } catch (err: any) {
      setError(err.message);
      toast.error('Failed to load notification analytics');
    }
  };

  const fetchSubscriberInsights = async () => {
    try {
      const { data, error } = await supabase.rpc('get_venue_subscriber_insights' as any, {
        p_venue_id: venueId
      });

      if (error) throw error;
      if (data && data.length > 0) {
        setInsights(data[0]);
      }
    } catch (err: any) {
      console.error('Error fetching subscriber insights:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map(i => (
            <Card key={i} className="bg-gray-800/50 border-gray-700">
              <CardContent className="p-4">
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                  <div className="h-6 bg-gray-700 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="bg-red-900/20 border-red-800">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-red-400">
            <AlertCircle className="w-5 h-5" />
            <span>Error loading analytics: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalNotifications = analytics.length;
  const avgReadRate = analytics.length > 0 
    ? analytics.reduce((sum, a) => sum + a.read_rate_percentage, 0) / analytics.length 
    : 0;
  const totalDelivered = analytics.reduce((sum, a) => sum + a.total_delivered, 0);
  const totalRead = analytics.reduce((sum, a) => sum + a.total_read, 0);

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-emerald-900/20 to-emerald-800/10 border-emerald-700/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-emerald-300 text-sm font-medium">Total Subscribers</p>
                <p className="text-2xl font-bold text-white">
                  {insights?.total_subscribers || 0}
                </p>
              </div>
              <Users className="w-8 h-8 text-emerald-400" />
            </div>
            {insights && insights.new_subscribers_this_week > 0 && (
              <div className="mt-2 flex items-center gap-1">
                <TrendingUp className="w-3 h-3 text-green-400" />
                <span className="text-xs text-green-400">
                  +{insights.new_subscribers_this_week} this week
                </span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-900/20 to-blue-800/10 border-blue-700/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-300 text-sm font-medium">Notifications Sent</p>
                <p className="text-2xl font-bold text-white">{totalNotifications}</p>
              </div>
              <MessageCircle className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-900/20 to-purple-800/10 border-purple-700/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-300 text-sm font-medium">Avg Read Rate</p>
                <p className="text-2xl font-bold text-white">{avgReadRate.toFixed(1)}%</p>
              </div>
              <Eye className="w-8 h-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-900/20 to-orange-800/10 border-orange-700/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-300 text-sm font-medium">Active Subscribers</p>
                <p className="text-2xl font-bold text-white">
                  {insights?.active_subscribers || 0}
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Notifications Performance */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Recent Notification Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          {analytics.length === 0 ? (
            <div className="text-center py-8">
              <MessageCircle className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400">No notifications sent yet</p>
              <p className="text-gray-500 text-sm">Start engaging with your subscribers!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {analytics.map((notification) => (
                <div
                  key={notification.template_id}
                  className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h4 className="text-white font-medium">{notification.title}</h4>
                      <p className="text-gray-400 text-sm line-clamp-2">
                        {notification.message}
                      </p>
                    </div>
                    <Badge 
                      variant={notification.approved ? "default" : "secondary"}
                      className={notification.approved ? "bg-green-600" : "bg-yellow-600"}
                    >
                      {notification.approved ? "Approved" : "Pending"}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-emerald-400">
                        {notification.total_delivered}
                      </p>
                      <p className="text-xs text-gray-400">Delivered</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-blue-400">
                        {notification.total_read}
                      </p>
                      <p className="text-xs text-gray-400">Read</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-purple-400">
                        {notification.read_rate_percentage.toFixed(1)}%
                      </p>
                      <p className="text-xs text-gray-400">Read Rate</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-400">
                        {format(new Date(notification.sent_at), 'MMM dd, yyyy')}
                      </p>
                      <p className="text-xs text-gray-500">Sent</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

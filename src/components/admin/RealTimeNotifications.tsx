/**
 * 🔄 REAL-TIME NOTIFICATIONS COMPONENT
 * 
 * Displays live notifications for:
 * - New bookings
 * - Booking cancellations
 * - Status changes
 * - Dashboard metric updates
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Bell, CheckCircle, XCircle, Clock, DollarSign, TrendingUp, Trash2, History } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from 'react-hot-toast';
import { format, parseISO } from 'date-fns';
import {
  realTimeManager,
  RealTimeBookingUpdate,
  RealTimeDashboardMetrics
} from '@/utils/realTimeUpdates';

interface RealTimeNotificationsProps {
  userId: string;
  userRole: string;
  adminVenues: Array<{ venue_id: string }>;
  selectedVenueId?: string;
  onMetricsUpdate?: (metrics: RealTimeDashboardMetrics) => void;
}

interface NotificationItem extends RealTimeBookingUpdate {
  isRead: boolean;
  isPersistent?: boolean; // Indicates if this is from persistent storage
  receivedAt: string; // When the notification was first received
}

// 💾 PERSISTENT NOTIFICATION STORAGE
const NOTIFICATION_STORAGE_KEY = 'admin_notification_history';
const MAX_PERSISTENT_NOTIFICATIONS = 5;
const NOTIFICATION_EXPIRY_HOURS = 24; // Clear notifications older than 24 hours

interface StoredNotification {
  notification: NotificationItem;
  storedAt: string;
}

// Utility functions for persistent notification storage
const notificationStorage = {
  save: (notifications: NotificationItem[]): void => {
    try {
      const toStore: StoredNotification[] = notifications
        .slice(0, MAX_PERSISTENT_NOTIFICATIONS)
        .map(notification => ({
          notification,
          storedAt: new Date().toISOString()
        }));

      localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(toStore));
    } catch (error) {
      console.warn('Failed to save notifications to localStorage:', error);
    }
  },

  load: (): NotificationItem[] => {
    try {
      const stored = localStorage.getItem(NOTIFICATION_STORAGE_KEY);
      if (!stored) return [];

      const storedNotifications: StoredNotification[] = JSON.parse(stored);
      const now = new Date();

      // Filter out expired notifications
      const validNotifications = storedNotifications.filter(item => {
        const storedTime = new Date(item.storedAt);
        const hoursDiff = (now.getTime() - storedTime.getTime()) / (1000 * 60 * 60);
        return hoursDiff < NOTIFICATION_EXPIRY_HOURS;
      });

      // Mark as persistent and return
      return validNotifications.map(item => ({
        ...item.notification,
        isPersistent: true
      }));
    } catch (error) {
      console.warn('Failed to load notifications from localStorage:', error);
      localStorage.removeItem(NOTIFICATION_STORAGE_KEY);
      return [];
    }
  },

  clear: (): void => {
    try {
      localStorage.removeItem(NOTIFICATION_STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear notifications from localStorage:', error);
    }
  }
};

const RealTimeNotifications: React.FC<RealTimeNotificationsProps> = ({
  userId,
  userRole,
  adminVenues,
  selectedVenueId = 'all',
  onMetricsUpdate
}) => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [lastMetricsUpdate, setLastMetricsUpdate] = useState<RealTimeDashboardMetrics | null>(null);
  const [showPersistentOnly, setShowPersistentOnly] = useState(false);

  // 💾 Load persistent notifications on component mount
  useEffect(() => {
    const persistentNotifications = notificationStorage.load();
    if (persistentNotifications.length > 0) {
      console.log(`📱 Loaded ${persistentNotifications.length} persistent notifications`);
      setNotifications(persistentNotifications);
      // Count unread persistent notifications
      const unreadPersistent = persistentNotifications.filter(n => !n.isRead).length;
      setUnreadCount(unreadPersistent);
    }
  }, []);

  // 💾 Save notifications to persistent storage whenever they change
  const saveNotificationsToPersistentStorage = useCallback((notificationList: NotificationItem[]) => {
    // Only save the most recent non-persistent notifications (new real-time ones)
    const newNotifications = notificationList.filter(n => !n.isPersistent);
    if (newNotifications.length > 0) {
      notificationStorage.save(newNotifications);
    }
  }, []);

  useEffect(() => {
    let cleanup: (() => void) | undefined;

    const initializeRealTime = async () => {
      try {
        console.log('🔄 Initializing real-time notifications...');
        
        await realTimeManager.initialize(userId, userRole, adminVenues);
        setIsConnected(true);

        // Subscribe to booking updates
        const unsubscribeBookings = realTimeManager.onBookingUpdate((update) => {
          console.log('🔔 New real-time booking update:', update);

          const notification: NotificationItem = {
            ...update,
            isRead: false,
            isPersistent: false,
            receivedAt: new Date().toISOString()
          };

          setNotifications(prev => {
            const newNotifications = [notification, ...prev];
            // Keep last 20 total, but maintain rolling history of 5 most recent for persistence
            const trimmedNotifications = newNotifications.slice(0, 20);

            // Save to persistent storage
            saveNotificationsToPersistentStorage(trimmedNotifications);

            return trimmedNotifications;
          });

          setUnreadCount(prev => prev + 1);

          // Show toast notification
          const message = getNotificationMessage(update);
          const toastIcon = getNotificationIcon(update.type);

          toast.success(message, {
            icon: toastIcon,
            duration: 5000,
            position: 'top-right'
          });
        });

        // Subscribe to dashboard metrics updates
        const unsubscribeMetrics = realTimeManager.onDashboardMetricsUpdate((metrics) => {
          console.log('📊 Dashboard metrics updated:', metrics);
          setLastMetricsUpdate(metrics);
          
          if (onMetricsUpdate) {
            onMetricsUpdate(metrics);
          }
        });

        cleanup = () => {
          unsubscribeBookings();
          unsubscribeMetrics();
          realTimeManager.cleanup();
        };

      } catch (error) {
        console.error('Error initializing real-time notifications:', error);
        setIsConnected(false);
      }
    };

    if (userId && userRole) {
      initializeRealTime();
    }

    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, [userId, userRole, adminVenues, onMetricsUpdate]);

  const getNotificationMessage = (update: RealTimeBookingUpdate): string => {
    switch (update.type) {
      case 'new_booking':
        return `New booking: ${update.customer_name} at ${update.venue_name}`;
      case 'cancellation':
        return `Booking cancelled: ${update.customer_name} at ${update.venue_name}`;
      case 'status_change':
        return `Booking ${update.status}: ${update.customer_name} at ${update.venue_name}`;
      default:
        return 'Booking update received';
    }
  };

  const getNotificationIcon = (type: RealTimeBookingUpdate['type']): string => {
    switch (type) {
      case 'new_booking':
        return '🎾';
      case 'cancellation':
        return '❌';
      case 'status_change':
        return '✅';
      default:
        return '📋';
    }
  };

  const getNotificationColor = (type: RealTimeBookingUpdate['type']): string => {
    switch (type) {
      case 'new_booking':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancellation':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'status_change':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId 
          ? { ...notif, isRead: true }
          : notif
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(notif => ({ ...notif, isRead: true })));
    setUnreadCount(0);
  };

  const clearNotifications = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  // 🗑️ Clear persistent notification history
  const clearPersistentHistory = () => {
    notificationStorage.clear();
    // Remove persistent notifications from current state
    setNotifications(prev => prev.filter(n => !n.isPersistent));
    // Recalculate unread count
    setUnreadCount(prev => {
      const persistentUnread = notifications.filter(n => n.isPersistent && !n.isRead).length;
      return Math.max(0, prev - persistentUnread);
    });
    toast.success('Notification history cleared', {
      icon: '🗑️',
      duration: 3000
    });
  };

  // 📱 Toggle between showing all notifications or persistent only
  const togglePersistentView = () => {
    setShowPersistentOnly(!showPersistentOnly);
  };

  // Filter notifications based on view mode and selected venue
  const displayedNotifications = (showPersistentOnly
    ? notifications.filter(n => n.isPersistent)
    : notifications
  ).filter(notification => {
    // If 'all' venues selected, show all notifications
    if (selectedVenueId === 'all') return true;

    // Filter by selected venue ID
    return notification.venue_id === selectedVenueId;
  });

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm text-gray-600">
            {isConnected ? 'Live Updates Active' : 'Connecting...'}
          </span>
        </div>
        
        {unreadCount > 0 && (
          <Badge variant="destructive" className="animate-pulse">
            {unreadCount} new
          </Badge>
        )}
      </div>

      {/* Last Metrics Update */}
      {lastMetricsUpdate && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-3">
            <div className="flex items-center space-x-2 text-sm">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-green-800">
                Dashboard updated: {lastMetricsUpdate.todayBookings} bookings, 
                ${lastMetricsUpdate.todaysRevenue} revenue
              </span>
              <span className="text-green-600 text-xs">
                {format(parseISO(lastMetricsUpdate.lastUpdated), 'HH:mm:ss')}
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notifications Header & Controls */}
      {notifications.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900">
              Recent Activity ({displayedNotifications.length})
              {showPersistentOnly && (
                <Badge variant="secondary" className="ml-2 text-xs">
                  History Only
                </Badge>
              )}
            </h3>
            <div className="flex items-center space-x-2">
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  className="text-xs"
                >
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Mark all read
                </Button>
              )}
            </div>
          </div>

          {/* Action Controls */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={clearNotifications}
              className="text-xs"
            >
              Clear Current
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={togglePersistentView}
              className={`text-xs ${showPersistentOnly ? 'bg-blue-100 text-blue-800' : ''}`}
            >
              <History className="h-3 w-3 mr-1" />
              {showPersistentOnly ? 'Show All' : 'History Only'}
            </Button>
            {notifications.some(n => n.isPersistent) && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearPersistentHistory}
                className="text-xs text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Clear History
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Notifications List */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {displayedNotifications.map((notification) => (
          <Card
            key={notification.id}
            className={`cursor-pointer transition-all duration-200 ${
              notification.isRead
                ? 'opacity-60 border-gray-200'
                : `border-2 ${getNotificationColor(notification.type)} shadow-sm`
            } ${notification.isPersistent ? 'bg-blue-50/50 border-blue-200' : ''}`}
            onClick={() => markAsRead(notification.id)}
          >
            <CardContent className="p-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">
                      {getNotificationIcon(notification.type)}
                    </span>
                    <span className="text-sm font-medium">
                      {getNotificationMessage(notification)}
                    </span>
                    {!notification.isRead && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    )}
                    {notification.isPersistent && (
                      <Badge variant="secondary" className="text-xs px-1 py-0">
                        <History className="h-2 w-2 mr-1" />
                        Saved
                      </Badge>
                    )}
                  </div>

                  <div className="mt-1 text-xs text-gray-500 space-y-1">
                    <div>
                      Court: {notification.court_name} |
                      Date: {notification.booking_date ? format(parseISO(notification.booking_date), 'MMM dd') : 'N/A'}
                    </div>
                    {notification.total_price && (
                      <div className="flex items-center space-x-1">
                        <DollarSign className="h-3 w-3" />
                        <span>${notification.total_price}</span>
                      </div>
                    )}
                    {notification.isPersistent && (
                      <div className="text-xs text-blue-600">
                        Received: {format(parseISO(notification.receivedAt), 'MMM dd, HH:mm')}
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-xs text-gray-400">
                  {format(parseISO(notification.timestamp), 'HH:mm')}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {displayedNotifications.length === 0 && isConnected && (
        <Card className="border-dashed">
          <CardContent className="p-6 text-center">
            <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">
              {showPersistentOnly
                ? "No saved notification history. Recent updates will be saved automatically."
                : "No recent activity. You'll see live updates here."
              }
            </p>
            {showPersistentOnly && notifications.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={togglePersistentView}
                className="mt-2 text-xs"
              >
                Show all notifications
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Connection Error */}
      {!isConnected && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-3">
            <div className="flex items-center space-x-2 text-sm text-red-800">
              <XCircle className="h-4 w-4" />
              <span>Real-time updates unavailable. Check your connection.</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default RealTimeNotifications;

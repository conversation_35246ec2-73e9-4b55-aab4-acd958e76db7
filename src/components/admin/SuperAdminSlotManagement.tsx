import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { format, addDays, startOfWeek, endOfWeek } from 'date-fns';
import { useSlotManagement } from '@/hooks/useSlotManagement';
import SlotManagementService from '@/services/slotManagementService';
import SlotManagementDashboard from './SlotManagementDashboard';
import SlotManagementNotifications from './SlotManagementNotifications';
import { useRealTimeSlotUpdates } from '@/hooks/useRealTimeSlotUpdates';
import { 
  Calendar as CalendarIcon, 
  Filter, 
  Ban, 
  Unlock, 
  Download,
  Search,
  Building,
  MapPin,
  Clock,
  User,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface Venue {
  id: string;
  name: string;
  location: string;
  courts: Court[];
}

interface Court {
  id: string;
  name: string;
  venue_id: string;
  court_group_id?: string;
}

interface BlockedSlot {
  id: string;
  court_id: string;
  court_name: string;
  venue_id: string;
  venue_name: string;
  date: string;
  start_time: string;
  end_time: string;
  reason: string;
  created_by: string;
  created_by_name: string;
  created_at: string;
}

interface SlotManagementStats {
  total_blocked_slots: number;
  venues_with_blocks: number;
  courts_with_blocks: number;
  date_range: {
    start_date: string;
    end_date: string;
  };
}

const SuperAdminSlotManagement: React.FC = () => {
  const { user } = useAuth();
  const [venues, setVenues] = useState<Venue[]>([]);
  const [componentError, setComponentError] = useState<string | null>(null);

  // Use the slot management hook
  const {
    blockedSlots,
    stats,
    loading,
    error: slotError,
    fetchBlockedSlots,
    fetchFilteredBlockedSlots,
    fetchStats,
    bulkBlockSlots,
    unblockSlots,
    clearError
  } = useSlotManagement();
  const [selectedVenueId, setSelectedVenueId] = useState<string>('');
  const [selectedCourtIds, setSelectedCourtIds] = useState<string[]>([]);
  const [dateMode, setDateMode] = useState<'range' | 'single'>('range');
  const [dateRange, setDateRange] = useState({
    start: new Date(),
    end: addDays(new Date(), 7)
  });
  const [singleDate, setSingleDate] = useState<Date>(new Date());
  const [timeRange, setTimeRange] = useState({
    start: '09:00',
    end: '10:00'
  });
  const [blockReason, setBlockReason] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState({
    venue: 'all_venues',
    court: 'all_courts',
    reason: '',
    createdBy: 'all_admins',
    datePreset: 'week' // week, month, custom
  });

  // Advanced filtering state
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [adminUsers, setAdminUsers] = useState<Array<{id: string, name: string}>>([]);

  // Real-time updates
  useRealTimeSlotUpdates({
    venueIds: (filterBy.venue && filterBy.venue !== 'all_venues') ? [filterBy.venue] : undefined,
    showNotifications: false, // We'll use the dedicated notification component
    onSlotBlocked: () => {
      // Refresh data when slots are blocked
      loadFilteredData();
    },
    onSlotUnblocked: () => {
      // Refresh data when slots are unblocked
      loadFilteredData();
    },
    onSlotUpdated: () => {
      // Refresh data when slots are updated
      loadFilteredData();
    }
  });
  const [selectedSlots, setSelectedSlots] = useState<string[]>([]);
  const [bulkOperationLoading, setBulkOperationLoading] = useState(false);

  useEffect(() => {
    const initializeComponent = async () => {
      try {
        console.log('🚀 Initializing Super Admin Slot Management...');
        console.log('👤 Current user:', user?.id, user?.email);

        await fetchVenuesData();
        await fetchAdminUsers();
        await loadInitialData();

        console.log('✅ Component initialized successfully');
      } catch (error) {
        console.error('❌ Error initializing component:', error);
        setComponentError(`Failed to initialize: ${error}`);
      }
    };

    initializeComponent();
  }, []);

  useEffect(() => {
    loadFilteredData();
  }, [filterBy, searchTerm, dateRange]);

  const fetchVenuesData = async () => {
    try {
      const { data, error } = await SlotManagementService.getVenuesWithCourts();
      if (error) throw error;
      setVenues(data || []);
    } catch (error) {
      console.error('Error fetching venues:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch venues',
        variant: 'destructive'
      });
    }
  };

  const fetchAdminUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select(`
          user_id,
          role,
          profiles (
            id,
            full_name,
            email
          )
        `)
        .in('role', ['admin', 'super_admin']);

      if (error) throw error;

      const adminList = data?.map(item => ({
        id: item.user_id,
        name: item.profiles?.full_name || item.profiles?.email || 'Unknown Admin'
      })) || [];

      setAdminUsers(adminList);
    } catch (error) {
      console.error('Error fetching admin users:', error);
    }
  };

  const loadInitialData = async () => {
    try {
      console.log('🔄 Loading initial data...');
      await Promise.all([
        fetchBlockedSlots(
          undefined,
          undefined,
          format(dateRange.start, 'yyyy-MM-dd'),
          format(dateRange.end, 'yyyy-MM-dd')
        ),
        fetchStats(
          undefined,
          format(dateRange.start, 'yyyy-MM-dd'),
          format(dateRange.end, 'yyyy-MM-dd')
        )
      ]);
      console.log('✅ Initial data loaded successfully');
    } catch (error) {
      console.error('❌ Error loading initial data:', error);
      setComponentError(`Failed to load initial data: ${error}`);
    }
  };

  const loadFilteredData = async () => {
    // Use advanced filtering with search
    await fetchFilteredBlockedSlots({
      venue_ids: (filterBy.venue && filterBy.venue !== 'all_venues') ? [filterBy.venue] : undefined,
      court_ids: (filterBy.court && filterBy.court !== 'all_courts') ? [filterBy.court] : undefined,
      start_date: format(dateRange.start, 'yyyy-MM-dd'),
      end_date: format(dateRange.end, 'yyyy-MM-dd'),
      created_by_ids: (filterBy.createdBy && filterBy.createdBy !== 'all_admins') ? [filterBy.createdBy] : undefined,
      reason_filter: searchTerm || filterBy.reason || undefined,
      limit: 100
    });

    await fetchStats(
      (filterBy.venue && filterBy.venue !== 'all_venues') ? filterBy.venue : undefined,
      format(dateRange.start, 'yyyy-MM-dd'),
      format(dateRange.end, 'yyyy-MM-dd')
    );
  };

  const handleDatePresetChange = (preset: string) => {
    const now = new Date();
    let start: Date, end: Date;

    switch (preset) {
      case 'today':
        start = end = now;
        break;
      case 'week':
        start = startOfWeek(now);
        end = endOfWeek(now);
        break;
      case 'month':
        start = new Date(now.getFullYear(), now.getMonth(), 1);
        end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        break;
      case 'next_week':
        start = addDays(startOfWeek(now), 7);
        end = addDays(endOfWeek(now), 7);
        break;
      case 'next_month':
        start = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        end = new Date(now.getFullYear(), now.getMonth() + 2, 0);
        break;
      default:
        return; // custom - don't change dates
    }

    setDateRange({ start, end });
    setFilterBy(prev => ({ ...prev, datePreset: preset }));
  };

  const handleBulkBlock = async () => {
    if (!selectedVenueId || selectedCourtIds.length === 0) {
      toast({
        title: 'Missing Information',
        description: 'Please select venue and courts',
        variant: 'destructive'
      });
      return;
    }

    setBulkOperationLoading(true);
    try {
      // Determine the date range based on the selected mode
      const startDate = dateMode === 'single' ? singleDate : dateRange.start;
      const endDate = dateMode === 'single' ? singleDate : dateRange.end;

      const success = await bulkBlockSlots({
        court_ids: selectedCourtIds,
        start_date: format(startDate, 'yyyy-MM-dd'),
        end_date: format(endDate, 'yyyy-MM-dd'),
        start_time: timeRange.start,
        end_time: timeRange.end,
        reason: blockReason || 'Bulk blocked by super admin',
        created_by: user?.id
      });

      if (success) {
        // Reset form
        setSelectedCourtIds([]);
        setBlockReason('');

        // Refresh data
        await loadFilteredData();
      }
    } catch (error: any) {
      console.error('Error in bulk block operation:', error);
    } finally {
      setBulkOperationLoading(false);
    }
  };

  const handleBulkUnblock = async () => {
    if (selectedSlots.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select slots to unblock',
        variant: 'destructive'
      });
      return;
    }

    setBulkOperationLoading(true);
    try {
      const success = await unblockSlots(selectedSlots);

      if (success) {
        setSelectedSlots([]);
        await loadFilteredData();
      }
    } catch (error: any) {
      console.error('Error in bulk unblock operation:', error);
    } finally {
      setBulkOperationLoading(false);
    }
  };

  const formatTime = (timeString: string) => {
    if (!timeString || typeof timeString !== 'string') {
      return 'Invalid time';
    }

    try {
      const [hours, minutes] = timeString.split(':');
      if (!hours || !minutes) {
        return 'Invalid time';
      }

      const hour = parseInt(hours, 10);
      if (isNaN(hour)) {
        return 'Invalid time';
      }

      const ampm = hour >= 12 ? 'PM' : 'AM';
      const hour12 = hour % 12 || 12;
      return `${hour12}:${minutes} ${ampm}`;
    } catch (error) {
      console.error('Error formatting time:', error, 'timeString:', timeString);
      return 'Invalid time';
    }
  };

  const selectedVenue = venues.find(v => v.id === selectedVenueId);

  if (componentError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-4">⚠️ Component Error</div>
          <p className="text-gray-600">{componentError}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-emerald-600 text-white rounded"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading slot management...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Notifications */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Super Admin Slot Management</h1>
          <p className="text-gray-600 mt-1">
            Comprehensive slot availability management across all venues
          </p>
        </div>
        <div className="flex items-center gap-4">
          <SlotManagementNotifications
            venueIds={(filterBy.venue && filterBy.venue !== 'all_venues') ? [filterBy.venue] : undefined}
          />
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Real-time Updates Active
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Blocked Slots</CardTitle>
            <Ban className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total_blocked_slots || 0}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Venues Affected</CardTitle>
            <Building className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.venues_with_blocks || 0}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Courts Affected</CardTitle>
            <MapPin className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.courts_with_blocks || 0}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Date Range</CardTitle>
            <CalendarIcon className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-sm">
              {format(dateRange.start, 'MMM dd')} - {format(dateRange.end, 'MMM dd')}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="manage" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="manage">Manage Slots</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Operations</TabsTrigger>
          <TabsTrigger value="overview">Overview</TabsTrigger>
        </TabsList>
        
        <TabsContent value="manage" className="space-y-4">
          {/* Search and Filters */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Search & Filter
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  className="text-emerald-600"
                >
                  {showAdvancedFilters ? 'Simple' : 'Advanced'} Filters
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Basic Filters */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="search">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="Search venues, courts, reasons..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="venue-filter">Venue</Label>
                  <Select value={filterBy.venue} onValueChange={(value) => setFilterBy(prev => ({ ...prev, venue: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="All venues" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all_venues">All venues</SelectItem>
                      {venues.map(venue => (
                        <SelectItem key={venue.id} value={venue.id}>
                          {venue.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Date Preset</Label>
                  <Select value={filterBy.datePreset} onValueChange={handleDatePresetChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                      <SelectItem value="next_week">Next Week</SelectItem>
                      <SelectItem value="next_month">Next Month</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Actions</Label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSearchTerm('');
                        setFilterBy({ venue: 'all_venues', court: 'all_courts', reason: '', createdBy: 'all_admins', datePreset: 'week' });
                        handleDatePresetChange('week');
                      }}
                    >
                      Clear
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadFilteredData()}
                    >
                      Refresh
                    </Button>
                  </div>
                </div>
              </div>

              {/* Custom Date Range (when custom is selected) */}
              {filterBy.datePreset === 'custom' && (
                <div className="border-t pt-4">
                  <Label>Custom Date Range</Label>
                  <div className="flex gap-2 mt-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="justify-start">
                          <CalendarIcon className="h-4 w-4 mr-2" />
                          {format(dateRange.start, 'MMM dd, yyyy')} - {format(dateRange.end, 'MMM dd, yyyy')}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="range"
                          selected={{
                            from: dateRange.start,
                            to: dateRange.end
                          }}
                          onSelect={(range) => {
                            if (range?.from) {
                              setDateRange({
                                start: range.from,
                                end: range.to || range.from
                              });
                            }
                          }}
                          numberOfMonths={2}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              )}

              {/* Advanced Filters */}
              {showAdvancedFilters && (
                <div className="border-t pt-4 space-y-4">
                  <h4 className="font-medium text-gray-900">Advanced Filters</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="court-filter">Specific Court</Label>
                      <Select value={filterBy.court} onValueChange={(value) => setFilterBy(prev => ({ ...prev, court: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="All courts" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all_courts">All courts</SelectItem>
                          {venues
                            .filter(venue => filterBy.venue === 'all_venues' || venue.id === filterBy.venue)
                            .flatMap(venue => venue.courts)
                            .map(court => (
                              <SelectItem key={court.id} value={court.id}>
                                {court.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="reason-filter">Reason Contains</Label>
                      <Input
                        id="reason-filter"
                        placeholder="Filter by reason..."
                        value={filterBy.reason}
                        onChange={(e) => setFilterBy(prev => ({ ...prev, reason: e.target.value }))}
                      />
                    </div>

                    <div>
                      <Label htmlFor="admin-filter">Created By Admin</Label>
                      <Select value={filterBy.createdBy} onValueChange={(value) => setFilterBy(prev => ({ ...prev, createdBy: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="All admins" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all_admins">All admins</SelectItem>
                          {adminUsers.map(admin => (
                            <SelectItem key={admin.id} value={admin.id}>
                              {admin.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Blocked Slots List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Ban className="h-5 w-5" />
                  Blocked Slots ({blockedSlots.length})
                </CardTitle>
                <div className="flex gap-2">
                  {selectedSlots.length > 0 && (
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={handleBulkUnblock}
                      disabled={bulkOperationLoading}
                    >
                      <Unlock className="h-4 w-4 mr-2" />
                      Unblock Selected ({selectedSlots.length})
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {blockedSlots.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Ban className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No blocked slots found for the selected criteria</p>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 mb-4">
                    <Checkbox
                      checked={selectedSlots.length === blockedSlots.length}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedSlots(blockedSlots.map(slot => slot.id));
                        } else {
                          setSelectedSlots([]);
                        }
                      }}
                    />
                    <Label className="text-sm font-medium">Select All</Label>
                  </div>

                  <div className="grid gap-3">
                    {blockedSlots
                      .filter(slot => slot && slot.id && slot.date && slot.start_time && slot.end_time)
                      .map((slot) => (
                      <div
                        key={slot.id}
                        className="flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50"
                      >
                        <Checkbox
                          checked={selectedSlots.includes(slot.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedSlots(prev => [...prev, slot.id]);
                            } else {
                              setSelectedSlots(prev => prev.filter(id => id !== slot.id));
                            }
                          }}
                        />

                        <div className="flex-1 grid grid-cols-1 md:grid-cols-5 gap-4">
                          <div>
                            <div className="font-medium">{slot.venue_name}</div>
                            <div className="text-sm text-gray-500">{slot.court_name}</div>
                          </div>

                          <div>
                            <div className="font-medium">
                              {slot.date ? format(new Date(slot.date), 'MMM dd, yyyy') : 'Invalid date'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {formatTime(slot.start_time)} - {formatTime(slot.end_time)}
                            </div>
                          </div>

                          <div>
                            <Badge variant="secondary" className="text-xs">
                              {slot.reason || 'No reason'}
                            </Badge>
                          </div>

                          <div>
                            <div className="text-sm">{slot.created_by_name || 'Unknown'}</div>
                            <div className="text-xs text-gray-500">
                              {slot.created_at ? format(new Date(slot.created_at), 'MMM dd, HH:mm') : 'Unknown date'}
                            </div>
                          </div>

                          <div className="flex justify-end">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleBulkUnblock()}
                              disabled={bulkOperationLoading}
                            >
                              <Unlock className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Ban className="h-5 w-5" />
                Bulk Slot Blocking
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="bulk-venue">Select Venue</Label>
                    <Select value={selectedVenueId} onValueChange={setSelectedVenueId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a venue" />
                      </SelectTrigger>
                      <SelectContent>
                        {venues.map(venue => (
                          <SelectItem key={venue.id} value={venue.id}>
                            {venue.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedVenue && (
                    <div>
                      <Label>Select Courts</Label>
                      <div className="space-y-2 max-h-40 overflow-y-auto border rounded-md p-3">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={selectedCourtIds.length === selectedVenue.courts.length}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedCourtIds(selectedVenue.courts.map(c => c.id));
                              } else {
                                setSelectedCourtIds([]);
                              }
                            }}
                          />
                          <Label className="font-medium">Select All Courts</Label>
                        </div>
                        {selectedVenue.courts.map(court => (
                          <div key={court.id} className="flex items-center gap-2">
                            <Checkbox
                              checked={selectedCourtIds.includes(court.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedCourtIds(prev => [...prev, court.id]);
                                } else {
                                  setSelectedCourtIds(prev => prev.filter(id => id !== court.id));
                                }
                              }}
                            />
                            <Label className="text-sm">{court.name}</Label>
                          </div>
                        ))}
                      </div>
                      <p className="text-sm text-gray-500 mt-2">
                        {selectedCourtIds.length} of {selectedVenue.courts.length} courts selected
                      </p>
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="start-time">Start Time</Label>
                      <Input
                        id="start-time"
                        type="time"
                        value={timeRange.start}
                        onChange={(e) => setTimeRange(prev => ({ ...prev, start: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="end-time">End Time</Label>
                      <Input
                        id="end-time"
                        type="time"
                        value={timeRange.end}
                        onChange={(e) => setTimeRange(prev => ({ ...prev, end: e.target.value }))}
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="bulk-reason">Reason for Blocking</Label>
                    <Textarea
                      id="bulk-reason"
                      placeholder="Enter reason for blocking these slots..."
                      value={blockReason}
                      onChange={(e) => setBlockReason(e.target.value)}
                      rows={3}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  {/* Date Mode Selection */}
                  <div>
                    <Label className="text-base font-medium">Date Selection Mode</Label>
                    <RadioGroup
                      value={dateMode}
                      onValueChange={(value: 'range' | 'single') => setDateMode(value)}
                      className="flex gap-6 mt-2"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="range" id="date-range" />
                        <Label htmlFor="date-range" className="cursor-pointer">Date Range</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="single" id="single-date" />
                        <Label htmlFor="single-date" className="cursor-pointer">Single Day</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Date Selection Interface */}
                  {dateMode === 'range' ? (
                    <div>
                      <Label>Select Date Range</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className="w-full justify-start">
                            <CalendarIcon className="h-4 w-4 mr-2" />
                            {format(dateRange.start, 'MMM dd, yyyy')} - {format(dateRange.end, 'MMM dd, yyyy')}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="range"
                            selected={{
                              from: dateRange.start,
                              to: dateRange.end
                            }}
                            onSelect={(range) => {
                              if (range?.from) {
                                setDateRange({
                                  start: range.from,
                                  end: range.to || range.from
                                });
                              }
                            }}
                            numberOfMonths={2}
                            disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
                          />
                        </PopoverContent>
                      </Popover>
                      <p className="text-xs text-gray-500 mt-1">
                        Select both start and end dates for your blocking range
                      </p>
                    </div>
                  ) : (
                    <div>
                      <Label>Select Single Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className="w-full justify-start">
                            <CalendarIcon className="h-4 w-4 mr-2" />
                            {format(singleDate, 'MMM dd, yyyy')}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={singleDate}
                            onSelect={(date) => {
                              if (date) {
                                setSingleDate(date);
                              }
                            }}
                            disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
                          />
                        </PopoverContent>
                      </Popover>
                      <p className="text-xs text-gray-500 mt-1">
                        Select a specific date to block slots
                      </p>
                    </div>
                  )}

                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-yellow-800">Bulk Operation Preview</h4>
                        <div className="text-sm text-yellow-700 mt-2 space-y-1">
                          <p><strong>Venue:</strong> {selectedVenue?.name || 'None selected'}</p>
                          <p><strong>Courts:</strong> {selectedCourtIds.length} selected</p>
                          <p><strong>Date Mode:</strong> {dateMode === 'single' ? 'Single Day' : 'Date Range'}</p>
                          {dateMode === 'single' ? (
                            <p><strong>Date:</strong> {format(singleDate, 'MMM dd, yyyy')}</p>
                          ) : (
                            <p><strong>Date Range:</strong> {format(dateRange.start, 'MMM dd')} - {format(dateRange.end, 'MMM dd')}</p>
                          )}
                          <p><strong>Time:</strong> {timeRange.start} - {timeRange.end}</p>
                          <p><strong>Total Slots:</strong> {
                            dateMode === 'single'
                              ? selectedCourtIds.length
                              : selectedCourtIds.length * Math.ceil((new Date(dateRange.end).getTime() - new Date(dateRange.start).getTime()) / (1000 * 60 * 60 * 24)) + 1
                          }</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Button
                    onClick={handleBulkBlock}
                    disabled={!selectedVenueId || selectedCourtIds.length === 0 || bulkOperationLoading}
                    className="w-full"
                    size="lg"
                  >
                    {bulkOperationLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Blocking Slots...
                      </>
                    ) : (
                      <>
                        <Ban className="h-4 w-4 mr-2" />
                        Block Selected Slots
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    setDateRange({
                      start: new Date(),
                      end: addDays(new Date(), 7)
                    });
                  }}
                >
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  View This Week
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    setDateRange({
                      start: startOfWeek(new Date()),
                      end: endOfWeek(addDays(new Date(), 7))
                    });
                  }}
                >
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  View Next 2 Weeks
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    setFilterBy({ venue: '', court: '', reason: '', createdBy: '', datePreset: 'week' });
                    setSearchTerm('');
                  }}
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Clear All Filters
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Slot Blocking System</span>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Real-time Updates</span>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Enabled
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Bulk Operations</span>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Available
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SuperAdminSlotManagement;

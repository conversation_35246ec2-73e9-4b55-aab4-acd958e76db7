/**
 * 🔐 SUPER ADMIN NOTIFICATION APPROVAL INTERFACE
 * 
 * Allows super admins to:
 * - View pending notification requests
 * - Approve/reject notifications with reasons
 * - Bulk approval functionality
 * - Track approval history
 */

import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  MessageSquare, 
  Building2,
  Calendar,
  Users,
  AlertTriangle
} from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'react-hot-toast';

interface PendingNotification {
  id: string;
  venue_id: string;
  venue_name: string;
  title: string;
  message: string;
  created_by: string;
  created_at: string;
  subscriber_count: number;
  creator_email: string;
}

export function NotificationApprovalInterface() {
  const [pendingNotifications, setPendingNotifications] = useState<PendingNotification[]>([]);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [rejectModal, setRejectModal] = useState(false);
  const [rejectingId, setRejectingId] = useState<string | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchPendingNotifications();
    
    // Set up real-time subscription for new notifications
    const channel = supabase
      .channel('notification_approvals')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'notification_templates',
        filter: 'approved=eq.false'
      }, () => {
        fetchPendingNotifications();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const fetchPendingNotifications = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_templates')
        .select(`
          id,
          venue_id,
          title,
          message,
          created_by,
          created_at,
          venues!inner(name),
          profiles!inner(email)
        `)
        .eq('approved', false)
        .is('rejection_reason', null)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Get subscriber counts for each venue
      const notificationsWithCounts = await Promise.all(
        (data || []).map(async (notification: any) => {
          const { count } = await supabase
            .from('venue_subscriptions')
            .select('*', { count: 'exact', head: true })
            .eq('venue_id', notification.venue_id);

          return {
            id: notification.id,
            venue_id: notification.venue_id,
            venue_name: notification.venues.name,
            title: notification.title,
            message: notification.message,
            created_by: notification.created_by,
            created_at: notification.created_at,
            subscriber_count: count || 0,
            creator_email: notification.profiles.email
          };
        })
      );

      setPendingNotifications(notificationsWithCounts);
    } catch (error) {
      console.error('Error fetching pending notifications:', error);
      toast.error('Failed to load pending notifications');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (notificationId: string) => {
    setProcessing(true);
    try {
      const { data, error } = await supabase.rpc('approve_and_distribute_notification' as any, {
        p_template_id: notificationId,
        p_approved_by: 'super_admin'
      });

      if (error) throw error;

      toast.success(`Notification approved and sent to ${data} subscribers!`);
      fetchPendingNotifications();
    } catch (error: any) {
      console.error('Error approving notification:', error);
      toast.error('Failed to approve notification');
    } finally {
      setProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!rejectingId || !rejectionReason.trim()) return;

    setProcessing(true);
    try {
      const { error } = await supabase
        .from('notification_templates')
        .update({
          rejection_reason: rejectionReason,
          approved_by: 'super_admin',
          approved_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', rejectingId);

      if (error) throw error;

      toast.success('Notification rejected');
      setRejectModal(false);
      setRejectingId(null);
      setRejectionReason('');
      fetchPendingNotifications();
    } catch (error) {
      console.error('Error rejecting notification:', error);
      toast.error('Failed to reject notification');
    } finally {
      setProcessing(false);
    }
  };

  const handleBulkApprove = async () => {
    if (selectedNotifications.length === 0) return;

    setProcessing(true);
    try {
      const results = await Promise.all(
        selectedNotifications.map(id => 
          supabase.rpc('approve_and_distribute_notification' as any, {
            p_template_id: id,
            p_approved_by: 'super_admin'
          })
        )
      );

      const totalDelivered = results.reduce((sum, result) => sum + (result.data || 0), 0);
      toast.success(`${selectedNotifications.length} notifications approved and sent to ${totalDelivered} total subscribers!`);
      
      setSelectedNotifications([]);
      fetchPendingNotifications();
    } catch (error) {
      console.error('Error bulk approving notifications:', error);
      toast.error('Failed to approve some notifications');
    } finally {
      setProcessing(false);
    }
  };

  const toggleSelectNotification = (id: string) => {
    setSelectedNotifications(prev => 
      prev.includes(id) 
        ? prev.filter(nId => nId !== id)
        : [...prev, id]
    );
  };

  const selectAllNotifications = () => {
    setSelectedNotifications(
      selectedNotifications.length === pendingNotifications.length 
        ? [] 
        : pendingNotifications.map(n => n.id)
    );
  };

  if (loading) {
    return (
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">Loading Pending Notifications...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="animate-pulse bg-gray-800/50 rounded-lg p-4 h-32"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Pending Notification Approvals
              {pendingNotifications.length > 0 && (
                <Badge className="bg-yellow-600 text-white">
                  {pendingNotifications.length}
                </Badge>
              )}
            </CardTitle>
            
            {selectedNotifications.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-400">
                  {selectedNotifications.length} selected
                </span>
                <Button
                  onClick={handleBulkApprove}
                  disabled={processing}
                  className="bg-green-600 hover:bg-green-700 text-white"
                  size="sm"
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Approve Selected
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        
        <CardContent>
          {pendingNotifications.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <p className="text-gray-400">No pending notifications</p>
              <p className="text-gray-500 text-sm">All notifications have been reviewed</p>
            </div>
          ) : (
            <>
              {/* Select All Checkbox */}
              <div className="flex items-center gap-2 mb-4 pb-4 border-b border-gray-700">
                <Checkbox
                  checked={selectedNotifications.length === pendingNotifications.length}
                  onCheckedChange={selectAllNotifications}
                  className="border-gray-600"
                />
                <span className="text-sm text-gray-400">
                  Select all notifications
                </span>
              </div>

              {/* Notifications List */}
              <div className="space-y-4">
                {pendingNotifications.map(notification => (
                  <Card key={notification.id} className="bg-gray-800/50 border-gray-700">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={selectedNotifications.includes(notification.id)}
                          onCheckedChange={() => toggleSelectNotification(notification.id)}
                          className="border-gray-600 mt-1"
                        />
                        
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <h4 className="text-white font-medium">{notification.title}</h4>
                              <div className="flex items-center gap-4 text-sm text-gray-400 mt-1">
                                <div className="flex items-center gap-1">
                                  <Building2 className="w-3 h-3" />
                                  {notification.venue_name}
                                </div>
                                <div className="flex items-center gap-1">
                                  <Users className="w-3 h-3" />
                                  {notification.subscriber_count} subscribers
                                </div>
                                <div className="flex items-center gap-1">
                                  <Calendar className="w-3 h-3" />
                                  {format(new Date(notification.created_at), 'MMM dd, yyyy HH:mm')}
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <p className="text-gray-300 mb-3">{notification.message}</p>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">
                              Created by: {notification.creator_email}
                            </span>
                            
                            <div className="flex items-center gap-2">
                              <Button
                                onClick={() => {
                                  setRejectingId(notification.id);
                                  setRejectModal(true);
                                }}
                                disabled={processing}
                                variant="outline"
                                size="sm"
                                className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                              >
                                <XCircle className="w-3 h-3 mr-1" />
                                Reject
                              </Button>
                              <Button
                                onClick={() => handleApprove(notification.id)}
                                disabled={processing}
                                className="bg-green-600 hover:bg-green-700 text-white"
                                size="sm"
                              >
                                <CheckCircle className="w-3 h-3 mr-1" />
                                Approve & Send
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Rejection Modal */}
      <Dialog open={rejectModal} onOpenChange={setRejectModal}>
        <DialogContent className="bg-gray-900 border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-white flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-red-400" />
              Reject Notification
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <p className="text-gray-400">
              Please provide a reason for rejecting this notification. This will be sent to the venue admin.
            </p>
            <Textarea
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder="Enter rejection reason..."
              className="bg-gray-800 border-gray-600 text-white"
              rows={4}
            />
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setRejectModal(false)}
              className="border-gray-600 text-gray-300"
            >
              Cancel
            </Button>
            <Button
              onClick={handleReject}
              disabled={!rejectionReason.trim() || processing}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              <XCircle className="w-4 h-4 mr-1" />
              Reject Notification
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

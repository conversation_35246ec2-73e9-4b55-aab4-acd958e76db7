import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import VenueSelector from '../VenueSelector';

const mockVenues = [
  { id: '1', name: 'Venue A' },
  { id: '2', name: 'Venue B' },
  { id: '3', name: 'Venue C' }
];

const defaultProps = {
  venues: mockVenues,
  selectedVenueId: 'all',
  onVenueChange: jest.fn(),
  userRole: 'admin' as const,
  showAllOption: true,
  variant: 'desktop' as const
};

describe('VenueSelector', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders venue selector with label', () => {
    render(<VenueSelector {...defaultProps} label="Test Venue" />);
    expect(screen.getByText('Test Venue')).toBeInTheDocument();
  });

  it('shows "All Venues" option for super admin', () => {
    render(<VenueSelector {...defaultProps} userRole="super_admin" />);
    
    // Click to open the dropdown
    const trigger = screen.getByRole('combobox');
    fireEvent.click(trigger);
    
    expect(screen.getByText('All Venues')).toBeInTheDocument();
  });

  it('shows "All Venues" option when multiple venues exist', () => {
    render(<VenueSelector {...defaultProps} userRole="admin" />);
    
    // Click to open the dropdown
    const trigger = screen.getByRole('combobox');
    fireEvent.click(trigger);
    
    expect(screen.getByText('All Venues')).toBeInTheDocument();
  });

  it('does not show "All Venues" option when showAllOption is false', () => {
    render(<VenueSelector {...defaultProps} showAllOption={false} />);
    
    // Click to open the dropdown
    const trigger = screen.getByRole('combobox');
    fireEvent.click(trigger);
    
    expect(screen.queryByText('All Venues')).not.toBeInTheDocument();
  });

  it('renders all venue options', () => {
    render(<VenueSelector {...defaultProps} />);
    
    // Click to open the dropdown
    const trigger = screen.getByRole('combobox');
    fireEvent.click(trigger);
    
    mockVenues.forEach(venue => {
      expect(screen.getByText(venue.name)).toBeInTheDocument();
    });
  });

  it('applies mobile styling when variant is mobile', () => {
    const { container } = render(<VenueSelector {...defaultProps} variant="mobile" />);
    
    // Check for mobile-specific classes
    const trigger = container.querySelector('[class*="bg-navy-900"]');
    expect(trigger).toBeInTheDocument();
  });

  it('applies desktop styling when variant is desktop', () => {
    const { container } = render(<VenueSelector {...defaultProps} variant="desktop" />);
    
    // Check for desktop-specific classes
    const trigger = container.querySelector('[class*="bg-white"]');
    expect(trigger).toBeInTheDocument();
  });

  it('calls onVenueChange when a venue is selected', () => {
    const mockOnVenueChange = jest.fn();
    render(<VenueSelector {...defaultProps} onVenueChange={mockOnVenueChange} />);
    
    // Click to open the dropdown
    const trigger = screen.getByRole('combobox');
    fireEvent.click(trigger);
    
    // Click on a venue option
    const venueOption = screen.getByText('Venue A');
    fireEvent.click(venueOption);
    
    expect(mockOnVenueChange).toHaveBeenCalledWith('1');
  });
});

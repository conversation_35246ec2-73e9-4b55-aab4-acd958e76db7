/**
 * 📚 NOTIFICATION TEMPLATE LIBRARY
 * 
 * Provides predefined notification templates for venue admins:
 * - Browse templates by category
 * - Use templates with dynamic variables
 * - Preview and customize before sending
 */

import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import {
  FileText,
  Sparkles,
  Calendar,
  Wrench,
  Gift,
  Cloud,
  Star,
  Copy,
  Edit3
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface NotificationTemplate {
  id: string;
  category: string;
  title: string;
  message: string;
  variables: string[];
  usage_count: number;
}

interface TemplateLibraryProps {
  onUseTemplate: (title: string, message: string) => void;
  venueName: string;
}

const categoryIcons: Record<string, React.ReactNode> = {
  promotional: <Gift className="w-4 h-4" />,
  maintenance: <Wrench className="w-4 h-4" />,
  event: <Calendar className="w-4 h-4" />,
  weather: <Cloud className="w-4 h-4" />,
  new_feature: <Sparkles className="w-4 h-4" />,
  holiday: <Star className="w-4 h-4" />
};

const categoryColors: Record<string, string> = {
  promotional: 'bg-green-600',
  maintenance: 'bg-orange-600',
  event: 'bg-blue-600',
  weather: 'bg-gray-600',
  new_feature: 'bg-purple-600',
  holiday: 'bg-yellow-600'
};

export function NotificationTemplateLibrary({ onUseTemplate, venueName }: TemplateLibraryProps) {
  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<NotificationTemplate | null>(null);
  const [customizeModal, setCustomizeModal] = useState(false);
  const [customTitle, setCustomTitle] = useState('');
  const [customMessage, setCustomMessage] = useState('');
  const [variableValues, setVariableValues] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_template_library')
        .select('*')
        .eq('is_active', true)
        .order('usage_count', { ascending: false });

      if (error) throw error;
      setTemplates(data || []);
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const categories = ['all', ...Array.from(new Set(templates.map(t => t.category)))];
  const filteredTemplates = selectedCategory === 'all' 
    ? templates 
    : templates.filter(t => t.category === selectedCategory);

  const handleUseTemplate = (template: NotificationTemplate) => {
    setSelectedTemplate(template);
    setCustomTitle(template.title);
    setCustomMessage(template.message);
    
    // Initialize variable values with defaults
    const defaultValues: Record<string, string> = {};
    template.variables.forEach(variable => {
      switch (variable) {
        case 'venue_name':
          defaultValues[variable] = venueName;
          break;
        case 'date':
          defaultValues[variable] = new Date().toLocaleDateString();
          break;
        case 'discount':
          defaultValues[variable] = '20';
          break;
        case 'day':
          defaultValues[variable] = 'weekend';
          break;
        default:
          defaultValues[variable] = `[${variable}]`;
      }
    });
    setVariableValues(defaultValues);
    setCustomizeModal(true);
  };

  const handleCustomizeAndUse = () => {
    let finalTitle = customTitle;
    let finalMessage = customMessage;

    // Replace variables in title and message
    Object.entries(variableValues).forEach(([variable, value]) => {
      const placeholder = `{${variable}}`;
      finalTitle = finalTitle.replace(new RegExp(placeholder, 'g'), value);
      finalMessage = finalMessage.replace(new RegExp(placeholder, 'g'), value);
    });

    onUseTemplate(finalTitle, finalMessage);
    setCustomizeModal(false);
    
    // Update usage count
    if (selectedTemplate) {
      updateUsageCount(selectedTemplate.id);
    }
    
    toast.success('Template applied successfully!');
  };

  const updateUsageCount = async (templateId: string) => {
    try {
      await supabase
        .from('notification_template_library')
        .update({ usage_count: supabase.sql`usage_count + 1` })
        .eq('id', templateId);
    } catch (error) {
      console.error('Error updating usage count:', error);
    }
  };

  const getCategoryDisplayName = (category: string) => {
    return category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">Loading Templates...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="animate-pulse bg-gray-800/50 rounded-lg p-4 h-32"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Notification Templates
          </CardTitle>
          <p className="text-gray-400 text-sm">
            Choose from pre-built templates to quickly create engaging notifications
          </p>
        </CardHeader>
        <CardContent>
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2 mb-6">
            {categories.map(category => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className={`${
                  selectedCategory === category 
                    ? 'bg-emerald-600 hover:bg-emerald-700' 
                    : 'border-gray-600 text-gray-300 hover:bg-gray-800'
                }`}
              >
                {category === 'all' ? 'All' : getCategoryDisplayName(category)}
              </Button>
            ))}
          </div>

          {/* Templates Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredTemplates.map(template => (
              <Card key={template.id} className="bg-gray-800/50 border-gray-700 hover:border-emerald-600/50 transition-colors">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Badge className={`${categoryColors[template.category]} text-white`}>
                        {categoryIcons[template.category]}
                        <span className="ml-1">{getCategoryDisplayName(template.category)}</span>
                      </Badge>
                    </div>
                    <span className="text-xs text-gray-500">
                      Used {template.usage_count} times
                    </span>
                  </div>
                  
                  <h4 className="text-white font-medium mb-2">{template.title}</h4>
                  <p className="text-gray-400 text-sm mb-3 line-clamp-2">
                    {template.message}
                  </p>
                  
                  {template.variables.length > 0 && (
                    <div className="mb-3">
                      <p className="text-xs text-gray-500 mb-1">Variables:</p>
                      <div className="flex flex-wrap gap-1">
                        {template.variables.map(variable => (
                          <Badge key={variable} variant="outline" className="text-xs border-gray-600 text-gray-400">
                            {variable}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <Button
                    onClick={() => handleUseTemplate(template)}
                    className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                    size="sm"
                  >
                    <Edit3 className="w-3 h-3 mr-1" />
                    Use Template
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredTemplates.length === 0 && (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400">No templates found for this category</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Customize Template Modal */}
      <Dialog open={customizeModal} onOpenChange={setCustomizeModal}>
        <DialogContent className="bg-gray-900 border-gray-700 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-white">Customize Template</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Title</label>
              <Input
                value={customTitle}
                onChange={(e) => setCustomTitle(e.target.value)}
                className="bg-gray-800 border-gray-600 text-white"
                placeholder="Notification title"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Message</label>
              <Textarea
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                className="bg-gray-800 border-gray-600 text-white"
                rows={4}
                placeholder="Notification message"
              />
            </div>
            
            {selectedTemplate && selectedTemplate.variables.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Variables</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {selectedTemplate.variables.map(variable => (
                    <div key={variable}>
                      <label className="block text-xs text-gray-400 mb-1">
                        {variable.replace('_', ' ')}
                      </label>
                      <Input
                        value={variableValues[variable] || ''}
                        onChange={(e) => setVariableValues(prev => ({
                          ...prev,
                          [variable]: e.target.value
                        }))}
                        className="bg-gray-800 border-gray-600 text-white text-sm"
                        placeholder={`Enter ${variable}`}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setCustomizeModal(false)}
              className="border-gray-600 text-gray-300"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCustomizeAndUse}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              <Copy className="w-4 h-4 mr-1" />
              Use Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

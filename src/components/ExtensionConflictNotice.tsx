/**
 * Component to display guidance when browser extension conflicts are detected
 */

import React, { useState, useEffect } from 'react';
import { AlertTriangle, X, ExternalLink, Shield, Chrome, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { detectProblematicExtensions } from '@/utils/extensionSafeNavigation';

interface ExtensionConflictNoticeProps {
  onDismiss?: () => void;
  autoShow?: boolean;
}

export const ExtensionConflictNotice: React.FC<ExtensionConflictNoticeProps> = ({
  onDismiss,
  autoShow = true
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  const [detectedExtensions, setDetectedExtensions] = useState<string[]>([]);

  useEffect(() => {
    // Check if user has already dismissed this notice
    const dismissed = localStorage.getItem('grid2play-extension-notice-dismissed');
    if (dismissed) {
      setIsDismissed(true);
      return;
    }

    // Detect problematic extensions
    const extensions = detectProblematicExtensions();
    setDetectedExtensions(extensions);

    // Show notice if extensions are detected and autoShow is enabled
    if (autoShow && extensions.length > 0) {
      setIsVisible(true);
    }
  }, [autoShow]);

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    localStorage.setItem('grid2play-extension-notice-dismissed', 'true');
    onDismiss?.();
  };

  const handleDismissTemporary = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  if (!isVisible || isDismissed || detectedExtensions.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg p-4">
        <div className="flex items-start">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">
              Browser Extension Detected
            </h3>
            <p className="text-sm text-yellow-700 mb-3">
              We've detected browser extensions that may interfere with Grid2Play's functionality, 
              particularly during sign-in/sign-out operations.
            </p>
            
            <div className="space-y-2 mb-4">
              <h4 className="text-xs font-medium text-yellow-800">If you experience issues:</h4>
              <ul className="text-xs text-yellow-700 space-y-1">
                <li>• Try using an incognito/private browsing window</li>
                <li>• Temporarily disable browser extensions</li>
                <li>• Clear your browser cache and cookies</li>
                <li>• Try a different browser</li>
              </ul>
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              <Button
                size="sm"
                variant="outline"
                className="text-xs border-yellow-300 text-yellow-700 hover:bg-yellow-100"
                onClick={() => window.open('https://support.google.com/chrome/answer/187443', '_blank')}
              >
                <Chrome className="h-3 w-3 mr-1" />
                Chrome Help
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="text-xs border-yellow-300 text-yellow-700 hover:bg-yellow-100"
                onClick={() => window.open('https://support.mozilla.org/en-US/kb/disable-or-remove-add-ons', '_blank')}
              >
                <Globe className="h-3 w-3 mr-1" />
                Firefox Help
              </Button>
            </div>

            <div className="flex justify-between items-center">
              <Button
                size="sm"
                variant="ghost"
                className="text-xs text-yellow-600 hover:text-yellow-800"
                onClick={handleDismissTemporary}
              >
                Hide for now
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="text-xs text-yellow-600 hover:text-yellow-800"
                onClick={handleDismiss}
              >
                Don't show again
              </Button>
            </div>
          </div>
          <Button
            size="sm"
            variant="ghost"
            className="p-1 h-auto text-yellow-600 hover:text-yellow-800"
            onClick={handleDismissTemporary}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

/**
 * Hook to manually show the extension conflict notice
 */
export const useExtensionConflictNotice = () => {
  const [showNotice, setShowNotice] = useState(false);

  const showExtensionNotice = () => {
    const extensions = detectProblematicExtensions();
    if (extensions.length > 0) {
      setShowNotice(true);
    }
  };

  const hideExtensionNotice = () => {
    setShowNotice(false);
  };

  return {
    showNotice,
    showExtensionNotice,
    hideExtensionNotice,
    ExtensionNotice: () => (
      <ExtensionConflictNotice
        onDismiss={hideExtensionNotice}
        autoShow={false}
      />
    )
  };
};

/**
 * Lightweight component that can be added to any page to show extension warnings
 */
export const ExtensionSafetyMonitor: React.FC = () => {
  return <ExtensionConflictNotice autoShow={true} />;
};

import React, { useState } from 'react';
import { Calendar, Download, Clock, MapPin, User, CreditCard } from 'lucide-react';

interface BookingDetails {
  booking_reference: string;
  guest_name: string;
  guest_phone: string;
  venue_name: string;
  venue_location: string;
  court_name: string;
  sport_name: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  total_price: string;
  payment_reference?: string;
}

interface CalendarUrls {
  google: string;
  outlook: string;
  yahoo: string;
  office365: string;
}

interface CalendarIntegrationProps {
  bookingReference: string;
  onCalendarAdd?: (platform: string) => void;
}

const CalendarIntegration: React.FC<CalendarIntegrationProps> = ({
  bookingReference,
  onCalendarAdd
}) => {
  const [calendarUrls, setCalendarUrls] = useState<CalendarUrls | null>(null);
  const [bookingDetails, setBookingDetails] = useState<BookingDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch calendar URLs and booking details
  const fetchCalendarData = async () => {
    if (calendarUrls) return; // Already loaded

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/generate-calendar-event', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          booking_reference: bookingReference,
          action: 'urls'
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to generate calendar links');
      }

      setCalendarUrls(data.calendar_urls);
      setBookingDetails(data.booking_details);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load calendar integration');
    } finally {
      setLoading(false);
    }
  };

  // Download .ics file
  const downloadICSFile = async () => {
    try {
      const response = await fetch('/api/generate-calendar-event', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          booking_reference: bookingReference,
          action: 'ics'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate calendar file');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${bookingReference}.ics`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      onCalendarAdd?.('download');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to download calendar file');
    }
  };

  // Handle calendar platform click
  const handleCalendarClick = (platform: string, url: string) => {
    window.open(url, '_blank');
    onCalendarAdd?.(platform);
  };

  // Format time for display
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  // Format date for display
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-IN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
      <div className="flex items-center gap-2 mb-4">
        <Calendar className="h-5 w-5 text-emerald-600" />
        <h3 className="text-lg font-semibold text-gray-900">Add to Calendar</h3>
      </div>

      <p className="text-gray-600 mb-4">
        Never miss your sports booking! Add this event to your calendar with automatic reminders.
      </p>

      {/* Booking Summary */}
      {bookingDetails && (
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="font-medium">{formatDate(bookingDetails.booking_date)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span>{formatTime(bookingDetails.start_time)} - {formatTime(bookingDetails.end_time)}</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-gray-500" />
              <span>{bookingDetails.venue_name}</span>
            </div>
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-500" />
              <span>{bookingDetails.guest_name}</span>
            </div>
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-gray-500" />
              <span>₹{bookingDetails.total_price}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs bg-emerald-100 text-emerald-800 px-2 py-1 rounded">
                {bookingDetails.sport_name} - {bookingDetails.court_name}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Calendar Buttons */}
      <div className="space-y-3">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
          </div>
        ) : error ? (
          <div className="text-red-600 text-sm bg-red-50 p-3 rounded-lg">
            {error}
          </div>
        ) : !calendarUrls ? (
          <button
            onClick={fetchCalendarData}
            className="w-full bg-emerald-600 text-white py-3 px-4 rounded-lg hover:bg-emerald-700 transition-colors flex items-center justify-center gap-2"
          >
            <Calendar className="h-4 w-4" />
            Load Calendar Options
          </button>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <button
                onClick={() => handleCalendarClick('google', calendarUrls.google)}
                className="flex items-center justify-center gap-2 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Calendar className="h-4 w-4" />
                Google Calendar
              </button>

              <button
                onClick={() => handleCalendarClick('outlook', calendarUrls.outlook)}
                className="flex items-center justify-center gap-2 bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-600 transition-colors"
              >
                <Calendar className="h-4 w-4" />
                Outlook
              </button>

              <button
                onClick={() => handleCalendarClick('yahoo', calendarUrls.yahoo)}
                className="flex items-center justify-center gap-2 bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors"
              >
                <Calendar className="h-4 w-4" />
                Yahoo Calendar
              </button>

              <button
                onClick={() => handleCalendarClick('office365', calendarUrls.office365)}
                className="flex items-center justify-center gap-2 bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors"
              >
                <Calendar className="h-4 w-4" />
                Office 365
              </button>
            </div>

            <button
              onClick={downloadICSFile}
              className="w-full flex items-center justify-center gap-2 bg-emerald-600 text-white py-3 px-4 rounded-lg hover:bg-emerald-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              Download Calendar File (.ics)
            </button>

            <div className="text-xs text-gray-500 text-center mt-3">
              ⏰ Includes automatic reminders: 1 hour and 15 minutes before your booking
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CalendarIntegration;

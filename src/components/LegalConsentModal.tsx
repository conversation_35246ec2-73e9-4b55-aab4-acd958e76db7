import React, { useState } from 'react';
import { X, FileText, Shield, ExternalLink, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';

interface LegalConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConsent: () => void;
}

const LegalConsentModal: React.FC<LegalConsentModalProps> = ({
  isOpen,
  onClose,
  onConsent
}) => {
  const [loading, setLoading] = useState(false);

  const handleConsent = async () => {
    // No checkbox validation needed - single button accepts all terms
    setLoading(true);

    try {
      toast({
        title: "Legal agreements accepted",
        description: "Your account has been activated successfully. Welcome to Grid२Play!",
      });

      // Call the parent component's consent handler which will handle the database operations
      onConsent();
    } catch (error) {
      console.error('Error processing consent:', error);
      toast({
        title: "Error",
        description: "There was an error processing your consent. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[100] flex items-end sm:items-center justify-center p-2 sm:p-4">
      <div className="bg-navy-dark border border-emerald-600/30 rounded-t-2xl sm:rounded-2xl w-full max-w-2xl max-h-[95vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-700">
          <div className="flex items-center gap-2 sm:gap-3">
            <FileText className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-400" />
            <h2 className="text-lg sm:text-xl font-bold text-white">Legal Agreements</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 sm:p-6">
          <div className="mb-4 sm:mb-6">
            <div className="flex items-start gap-2 sm:gap-3 mb-3 sm:mb-4">
              <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-emerald-400 mt-0.5" />
              <div>
                <h3 className="font-semibold text-white mb-1 text-sm sm:text-base">Welcome to Grid२Play!</h3>
                <p className="text-xs sm:text-sm text-gray-300">
                  To provide you with the best sports booking experience, we need your agreement to our legal terms.
                </p>
              </div>
            </div>

            <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3 sm:p-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="w-4 h-4 text-blue-400 mt-0.5" />
                <div className="text-xs sm:text-sm">
                  <p className="text-blue-300 font-medium mb-1">Required for Account Activation</p>
                  <p className="text-gray-300">
                    These agreements are mandatory to use Grid२Play services.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Legal Documents */}
          <div className="space-y-4 mb-6">
            {/* Privacy Policy */}
            <div className="bg-navy-light rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Shield className="w-5 h-5 text-emerald-400" />
                  <h3 className="font-semibold text-white">Privacy Policy</h3>
                </div>
                <a 
                  href="/privacy" 
                  target="_blank" 
                  className="text-emerald-400 hover:text-emerald-300 flex items-center gap-1 text-sm"
                >
                  Read Full Policy <ExternalLink className="w-3 h-3" />
                </a>
              </div>
              <p className="text-sm text-gray-300 mb-3">
                Explains how we collect, use, and protect your personal information, including booking data, 
                payment information, and communication preferences.
              </p>
              <div className="bg-navy rounded p-3 text-xs text-gray-400 mb-3">
                <strong>Key Points:</strong> We protect your data with encryption, never sell your information, 
                and you can access, update, or delete your data at any time.
              </div>
              <div className="py-2">
                <span className="text-sm sm:text-base font-bold" style={{ color: '#FFFFFF' }}>
                  By clicking "I Accept" below, you agree to our{' '}
                  <a
                    href="/privacy"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-emerald-400 hover:text-emerald-300 underline inline-flex items-center gap-1"
                  >
                    Privacy Policy
                    <ExternalLink className="w-3 h-3" />
                  </a>
                </span>
              </div>
            </div>

            {/* Terms of Service */}
            <div className="bg-navy-light rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <FileText className="w-5 h-5 text-blue-400" />
                  <h3 className="font-semibold text-white">Terms of Service</h3>
                </div>
                <a 
                  href="/terms" 
                  target="_blank" 
                  className="text-emerald-400 hover:text-emerald-300 flex items-center gap-1 text-sm"
                >
                  Read Full Terms <ExternalLink className="w-3 h-3" />
                </a>
              </div>
              <p className="text-sm text-gray-300 mb-3">
                Outlines the rules and guidelines for using Grid२Play, including booking policies, 
                payment terms, cancellation rules, and user responsibilities.
              </p>
              <div className="bg-navy rounded p-3 text-xs text-gray-400 mb-3">
                <strong>Key Points:</strong> Fair booking policies, secure payments via Razorpay, 
                flexible cancellation options, and sports activity safety guidelines.
              </div>
              <div className="py-2">
                <span className="text-sm sm:text-base font-bold" style={{ color: '#FFFFFF' }}>
                  By clicking "I Accept" below, you agree to our{' '}
                  <a
                    href="/terms"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-emerald-400 hover:text-emerald-300 underline inline-flex items-center gap-1"
                  >
                    Terms of Service
                    <ExternalLink className="w-3 h-3" />
                  </a>
                </span>
              </div>
            </div>


          </div>

          {/* Data Rights Information */}
          <div className="bg-emerald-900/20 border border-emerald-600/30 rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-emerald-300 mb-2">Your Data Rights</h4>
            <div className="text-sm text-gray-300 space-y-1">
              <p>• <strong>Access:</strong> View all your personal data we have</p>
              <p>• <strong>Update:</strong> Correct any inaccurate information</p>
              <p>• <strong>Delete:</strong> Request removal of your account and data</p>
              <p>• <strong>Withdraw:</strong> Change your consent preferences anytime</p>
            </div>
            <p className="text-xs text-gray-400 mt-2">
              Contact us at <a href="mailto:<EMAIL>" className="text-emerald-400 underline"><EMAIL></a> to exercise these rights.
            </p>
          </div>

          {/* Action Button */}
          <div className="flex flex-col gap-3">
            <Button
              onClick={handleConsent}
              disabled={loading}
              className="bg-emerald-600 hover:bg-emerald-700 text-white w-full min-h-[44px] text-sm sm:text-base font-bold"
            >
              {loading ? 'Processing...' : 'I Accept - Activate My Account!'}
            </Button>
            <Button
              onClick={onClose}
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-700 w-full min-h-[44px] text-sm sm:text-base"
            >
              Cancel
            </Button>
          </div>

          {/* Footer Note */}
          <div className="mt-4 text-xs text-gray-400 text-center">
            By agreeing, you confirm that you have completed age verification and understand our terms.
            You can review and modify your consent preferences in your account settings.
          </div>
        </div>
      </div>
    </div>
  );
};

export default LegalConsentModal;

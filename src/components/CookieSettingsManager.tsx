import React, { useState, useEffect } from 'react';
import CookieConsentModal from './CookieConsentModal';

interface CookiePreferences {
  necessary: boolean;
  functional: boolean;
  analytics: boolean;
  marketing: boolean;
}

const CookieSettingsManager: React.FC = () => {
  const [showCookieModal, setShowCookieModal] = useState(false);

  useEffect(() => {
    // Listen for custom event from footer cookie settings button
    const handleOpenCookieSettings = () => {
      setShowCookieModal(true);
    };

    window.addEventListener('openCookieSettings', handleOpenCookieSettings);

    return () => {
      window.removeEventListener('openCookieSettings', handleOpenCookieSettings);
    };
  }, []);

  const handleCookieConsent = (preferences: CookiePreferences) => {
    setShowCookieModal(false);
    
    // Apply cookie preferences to enable/disable tracking
    applyCookiePreferences(preferences);
  };

  const applyCookiePreferences = (preferences: CookiePreferences) => {
    // Enable/disable analytics based on preferences
    if (preferences.analytics) {
      console.log('Analytics cookies enabled');
    } else {
      console.log('Analytics cookies disabled');
    }

    // Enable/disable marketing based on preferences
    if (preferences.marketing) {
      console.log('Marketing cookies enabled');
    } else {
      console.log('Marketing cookies disabled');
    }

    // Functional cookies
    if (preferences.functional) {
      console.log('Functional cookies enabled');
    } else {
      console.log('Functional cookies disabled');
    }
  };

  return (
    <CookieConsentModal
      isOpen={showCookieModal}
      onClose={() => setShowCookieModal(false)}
      onSave={handleCookieConsent}
    />
  );
};

export default CookieSettingsManager;

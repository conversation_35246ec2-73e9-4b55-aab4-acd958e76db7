import React, { useState } from 'react';
import { X, Calendar, Shield, AlertTriangle, Users, Mail, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';

interface AgeVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVerified: () => void;
}

const AgeVerificationModal: React.FC<AgeVerificationModalProps> = ({
  isOpen,
  onClose,
  onVerified
}) => {
  const { user } = useAuth();
  const [dateOfBirth, setDateOfBirth] = useState('');
  const [loading, setLoading] = useState(false);
  const [age, setAge] = useState<number | null>(null);

  const calculateAge = (birthDate: string): number => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const handleDateChange = (date: string) => {
    setDateOfBirth(date);
    if (date) {
      const calculatedAge = calculateAge(date);
      setAge(calculatedAge);
    }
  };

  const handleVerification = async () => {
    if (!dateOfBirth) {
      toast({
        title: "Date of birth required",
        description: "Please enter your date of birth to continue.",
        variant: "destructive",
      });
      return;
    }

    if (age === null) return;

    setLoading(true);
    try {
      // Get user's IP for audit trail
      const ipResponse = await fetch('https://api.ipify.org?format=json');
      const ipData = await ipResponse.json();

      // Save age verification to database
      const { error } = await supabase
        .from('age_verifications')
        .insert({
          user_id: user?.id,
          date_of_birth: dateOfBirth,
          age_at_verification: age,
          verification_method: 'self_declared',
          ip_address: ipData.ip
        });

      if (error) throw error;

      // Record consent for age verification
      if (user) {
        await supabase.rpc('record_user_consent', {
          p_user_id: user.id,
          p_consent_type: 'age_verification',
          p_consent_version: '1.0',
          p_consent_given: true,
          p_ip_address: ipData.ip,
          p_user_agent: navigator.userAgent,
          p_consent_method: 'age_verification_modal'
        });
      }

      toast({
        title: "Age verification complete",
        description: "Your age has been verified successfully. You can now use all Grid२Play features.",
      });

      onVerified();
      onClose();
    } catch (error) {
      console.error('Error saving age verification:', error);
      toast({
        title: "Verification failed",
        description: "There was an error processing your age verification. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[100] flex items-end sm:items-center justify-center p-2 sm:p-4">
      <div className="bg-navy-dark border border-emerald-600/30 rounded-t-2xl sm:rounded-2xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-700">
          <div className="flex items-center gap-2 sm:gap-3">
            <Calendar className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-400" />
            <h2 className="text-lg sm:text-xl font-bold text-white">Age Verification</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 sm:p-6">
          <div className="mb-4 sm:mb-6">
            <div className="flex items-start gap-2 sm:gap-3 mb-3 sm:mb-4">
              <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-emerald-400 mt-0.5" />
              <div>
                <h3 className="font-semibold text-white mb-1 text-sm sm:text-base">Why do we need this?</h3>
                <p className="text-xs sm:text-sm text-gray-300">
                  To comply with Indian data protection laws, we need to verify your age.
                </p>
              </div>
            </div>

            <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4">
              <div className="flex items-start gap-2">
                <Users className="w-4 h-4 text-blue-400 mt-0.5" />
                <div className="text-xs sm:text-sm">
                  <p className="text-blue-300 font-medium mb-1">Age Verification</p>
                  <ul className="text-gray-300 space-y-1">
                    <li>• <strong>All users:</strong> Full access to all Grid२Play features</li>
                    <li>• <strong>Legal compliance:</strong> Required for data protection laws</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Date of Birth Input */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Date of Birth
            </label>
            <input
              type="date"
              value={dateOfBirth}
              onChange={(e) => handleDateChange(e.target.value)}
              max={new Date().toISOString().split('T')[0]} // Cannot select future dates
              className="w-full px-3 py-3 bg-navy border border-gray-600 rounded-lg text-white focus:border-emerald-500 focus:outline-none min-h-[44px]"
              required
            />
            {age !== null && (
              <p className="text-xs sm:text-sm text-gray-400 mt-1">
                Age: {age} years old
              </p>
            )}
          </div>



          {/* Privacy Notice */}
          <div className="bg-emerald-900/20 border border-emerald-600/30 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
            <div className="flex items-start gap-2">
              <Shield className="w-4 h-4 text-emerald-400 mt-0.5" />
              <div className="text-xs sm:text-sm">
                <p className="text-emerald-300 font-medium mb-1">Privacy Protection</p>
                <p className="text-gray-300">
                  Your date of birth is used only for age verification and compliance purposes.
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-3">
            <Button
              onClick={handleVerification}
              disabled={loading || !dateOfBirth}
              className="bg-emerald-600 hover:bg-emerald-700 text-white w-full min-h-[44px] text-sm sm:text-base"
            >
              {loading ? 'Verifying...' : 'Verify Age'}
            </Button>
            <Button
              onClick={onClose}
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-700 w-full min-h-[44px] text-sm sm:text-base"
            >
              Cancel
            </Button>
          </div>

          {/* Contact Support */}
          <div className="mt-4 sm:mt-6 text-center">
            <p className="text-xs text-gray-400 mb-2">
              Questions about age verification?
            </p>
            <div className="flex justify-center gap-4 text-xs">
              <a
                href="mailto:<EMAIL>"
                className="text-emerald-400 hover:text-emerald-300 flex items-center gap-1 min-h-[44px] px-2"
              >
                <Mail className="w-3 h-3" />
                Email Support
              </a>
              <a
                href="tel:+919211848599"
                className="text-emerald-400 hover:text-emerald-300 flex items-center gap-1 min-h-[44px] px-2"
              >
                <Phone className="w-3 h-3" />
                Call Us
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgeVerificationModal;

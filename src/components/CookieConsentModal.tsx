import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Settings, BarChart3, Target, Info, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';

interface CookiePreferences {
  necessary: boolean;
  functional: boolean;
  analytics: boolean;
  marketing: boolean;
}

interface CookieConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (preferences: CookiePreferences) => void;
}

const CookieConsentModal: React.FC<CookieConsentModalProps> = ({
  isOpen,
  onClose,
  onSave
}) => {
  const { user } = useAuth();
  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true, // Always true, cannot be disabled
    functional: false,
    analytics: false,
    marketing: false
  });
  const [loading, setLoading] = useState(false);

  // Load existing preferences if user is authenticated
  useEffect(() => {
    if (user && isOpen) {
      loadUserPreferences();
    }
  }, [user, isOpen]);

  const loadUserPreferences = async () => {
    try {
      const { data, error } = await supabase
        .from('cookie_preferences')
        .select('*')
        .eq('user_id', user?.id)
        .maybeSingle();

      if (!error && data) {
        setPreferences({
          necessary: true, // Always true
          functional: data.functional_cookies,
          analytics: data.analytics_cookies,
          marketing: data.marketing_cookies
        });
      }
    } catch (error) {
      console.error('Error loading cookie preferences:', error);
    }
  };

  const savePreferences = async (prefs: CookiePreferences) => {
    setLoading(true);
    try {
      // Get user's IP and user agent for audit trail
      const ipResponse = await fetch('https://api.ipify.org?format=json');
      const ipData = await ipResponse.json();
      const userAgent = navigator.userAgent;

      if (user) {
        // Save to database for authenticated users
        const { error } = await supabase
          .from('cookie_preferences')
          .upsert({
            user_id: user.id,
            necessary_cookies: true, // Always true
            functional_cookies: prefs.functional,
            analytics_cookies: prefs.analytics,
            marketing_cookies: prefs.marketing,
            ip_address: ipData.ip,
            user_agent: userAgent,
            last_updated: new Date().toISOString()
          });

        if (error) throw error;

        // Record consent in audit trail
        await supabase.rpc('record_user_consent', {
          p_user_id: user.id,
          p_consent_type: 'cookie_policy',
          p_consent_version: '1.0',
          p_consent_given: true,
          p_ip_address: ipData.ip,
          p_user_agent: userAgent,
          p_consent_method: 'cookie_banner'
        });
      } else {
        // Save to localStorage for non-authenticated users
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        localStorage.setItem('grid2play_cookie_preferences', JSON.stringify({
          ...prefs,
          sessionId,
          timestamp: new Date().toISOString()
        }));

        // Save to database with session ID
        await supabase
          .from('cookie_preferences')
          .upsert({
            session_id: sessionId,
            necessary_cookies: true,
            functional_cookies: prefs.functional,
            analytics_cookies: prefs.analytics,
            marketing_cookies: prefs.marketing,
            ip_address: ipData.ip,
            user_agent: userAgent,
            last_updated: new Date().toISOString()
          });
      }

      // Set cookies based on preferences
      setCookieConsent(prefs);
      
      onSave(prefs);
      onClose();

      toast({
        title: "Cookie preferences saved",
        description: "Your cookie preferences have been updated successfully.",
      });
    } catch (error) {
      console.error('Error saving cookie preferences:', error);
      toast({
        title: "Error saving preferences",
        description: "There was an error saving your cookie preferences. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const setCookieConsent = (prefs: CookiePreferences) => {
    // Set consent cookies that other scripts can read
    const expires = new Date();
    expires.setFullYear(expires.getFullYear() + 1); // 1 year expiry

    document.cookie = `grid2play_cookie_consent=true; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
    document.cookie = `grid2play_functional=${prefs.functional}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
    document.cookie = `grid2play_analytics=${prefs.analytics}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
    document.cookie = `grid2play_marketing=${prefs.marketing}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
  };

  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      functional: true,
      analytics: true,
      marketing: true
    };
    setPreferences(allAccepted);
    savePreferences(allAccepted);
  };

  const handleAcceptNecessary = () => {
    const necessaryOnly = {
      necessary: true,
      functional: false,
      analytics: false,
      marketing: false
    };
    setPreferences(necessaryOnly);
    savePreferences(necessaryOnly);
  };

  const handleSaveCustom = () => {
    savePreferences(preferences);
  };

  const togglePreference = (key: keyof CookiePreferences) => {
    if (key === 'necessary') return; // Cannot disable necessary cookies
    
    setPreferences(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[100] flex items-end sm:items-center justify-center p-2 sm:p-4">
      <div className="bg-navy-dark border border-emerald-600/30 rounded-t-2xl sm:rounded-2xl w-full max-w-4xl max-h-[95vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-700">
          <div className="flex items-center gap-2 sm:gap-3">
            <Cookie className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-400" />
            <h2 className="text-lg sm:text-xl font-bold text-white">Cookie Preferences</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 sm:p-6">
          <div className="mb-4 sm:mb-6">
            <p className="text-gray-300 mb-3 sm:mb-4 text-sm sm:text-base">
              We use cookies to enhance your browsing experience, provide personalized content, and analyze our traffic.
              You can customize your cookie preferences below.
            </p>
            <div className="flex items-center gap-2 text-xs sm:text-sm text-emerald-400">
              <Info className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>Learn more in our </span>
              <a href="/cookie-policy" target="_blank" className="underline hover:text-emerald-300 flex items-center gap-1">
                Cookie Policy <ExternalLink className="w-3 h-3" />
              </a>
            </div>
          </div>

          {/* Cookie Categories */}
          <div className="space-y-4 mb-6">
            {/* Necessary Cookies */}
            <div className="bg-navy-light rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Shield className="w-5 h-5 text-green-400" />
                  <h3 className="font-semibold text-white">Necessary Cookies</h3>
                </div>
                <div className="bg-green-600 text-white px-2 py-1 rounded text-xs font-medium">
                  Always Active
                </div>
              </div>
              <p className="text-sm text-gray-300 mb-2">
                Essential for basic website functionality, security, and your booking experience.
              </p>
              {showDetails && (
                <div className="text-xs text-gray-400 bg-navy rounded p-2">
                  <strong>Examples:</strong> User authentication, shopping cart, security tokens, booking process
                </div>
              )}
            </div>

            {/* Functional Cookies */}
            <div className="bg-navy-light rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Settings className="w-5 h-5 text-blue-400" />
                  <h3 className="font-semibold text-white">Functional Cookies</h3>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.functional}
                    onChange={() => togglePreference('functional')}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
                </label>
              </div>
              <p className="text-sm text-gray-300 mb-2">
                Remember your preferences and provide enhanced, personalized features.
              </p>
              {showDetails && (
                <div className="text-xs text-gray-400 bg-navy rounded p-2">
                  <strong>Examples:</strong> Location preferences, favorite venues, language settings, accessibility options
                </div>
              )}
            </div>

            {/* Analytics Cookies */}
            <div className="bg-navy-light rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5 text-purple-400" />
                  <h3 className="font-semibold text-white">Analytics Cookies</h3>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.analytics}
                    onChange={() => togglePreference('analytics')}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
                </label>
              </div>
              <p className="text-sm text-gray-300 mb-2">
                Help us understand how you use our platform to improve your experience.
              </p>
              {showDetails && (
                <div className="text-xs text-gray-400 bg-navy rounded p-2">
                  <strong>Examples:</strong> Page views, session duration, popular features, error tracking
                </div>
              )}
            </div>

            {/* Marketing Cookies */}
            <div className="bg-navy-light rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Target className="w-5 h-5 text-orange-400" />
                  <h3 className="font-semibold text-white">Marketing Cookies</h3>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.marketing}
                    onChange={() => togglePreference('marketing')}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
                </label>
              </div>
              <p className="text-sm text-gray-300 mb-2">
                Show you relevant offers and recommendations based on your interests.
              </p>
              {showDetails && (
                <div className="text-xs text-gray-400 bg-navy rounded p-2">
                  <strong>Examples:</strong> Personalized venue recommendations, targeted promotions, advertising effectiveness
                </div>
              )}
            </div>
          </div>

          {/* Show/Hide Details Toggle */}
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-emerald-400 hover:text-emerald-300 text-sm mb-6 flex items-center gap-1"
          >
            {showDetails ? 'Hide' : 'Show'} cookie details
          </button>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleAcceptAll}
              disabled={loading}
              className="bg-emerald-600 hover:bg-emerald-700 text-white flex-1 min-h-[44px] text-sm sm:text-base"
            >
              Accept All Cookies
            </Button>
            <Button
              onClick={handleAcceptNecessary}
              disabled={loading}
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-700 flex-1 min-h-[44px] text-sm sm:text-base"
            >
              Accept Necessary Only
            </Button>
            <Button
              onClick={handleSaveCustom}
              disabled={loading}
              variant="outline"
              className="border-emerald-600 text-emerald-400 hover:bg-emerald-900/20 flex-1 min-h-[44px] text-sm sm:text-base"
            >
              Save My Preferences
            </Button>
          </div>

          {/* Footer Note */}
          <div className="mt-3 sm:mt-4 text-xs text-gray-400 text-center">
            You can change these preferences at any time in your account settings or by clicking the cookie icon in the footer.
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookieConsentModal;

import React, { useState, useEffect } from 'react';
import { Calendar, Shield, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';

interface AgeVerificationSettingsProps {
  className?: string;
}

const AgeVerificationSettings: React.FC<AgeVerificationSettingsProps> = ({ className = '' }) => {
  const { user } = useAuth();
  const [dateOfBirth, setDateOfBirth] = useState('');
  const [loading, setLoading] = useState(false);
  const [age, setAge] = useState<number | null>(null);
  const [isVerified, setIsVerified] = useState(false);
  const [existingVerification, setExistingVerification] = useState<any>(null);

  useEffect(() => {
    if (user) {
      checkExistingVerification();
    }
  }, [user]);

  const checkExistingVerification = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('age_verifications')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (!error && data) {
        setExistingVerification(data);
        setDateOfBirth(data.date_of_birth);
        setAge(data.age_at_verification);
        setIsVerified(true);
      }
    } catch (error) {
      console.error('Error checking age verification:', error);
    }
  };

  const calculateAge = (birthDate: string): number => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const handleDateChange = (date: string) => {
    setDateOfBirth(date);
    if (date) {
      const calculatedAge = calculateAge(date);
      setAge(calculatedAge);
    }
  };

  const handleVerification = async () => {
    if (!dateOfBirth || !user) {
      toast({
        title: "Date of birth required",
        description: "Please enter your date of birth to continue.",
        variant: "destructive",
      });
      return;
    }

    if (age === null) return;

    setLoading(true);
    try {
      // Get user's IP for audit trail
      const ipResponse = await fetch('https://api.ipify.org?format=json');
      const ipData = await ipResponse.json();

      // Save or update age verification
      const verificationData = {
        user_id: user.id,
        date_of_birth: dateOfBirth,
        age_at_verification: age,
        verification_method: 'self_declared',
        ip_address: ipData.ip
      };

      let result;
      if (existingVerification) {
        // Update existing verification
        result = await supabase
          .from('age_verifications')
          .update(verificationData)
          .eq('id', existingVerification.id);
      } else {
        // Insert new verification
        result = await supabase
          .from('age_verifications')
          .insert(verificationData);
      }

      if (result.error) throw result.error;

      // Record consent for age verification
      await supabase.rpc('record_user_consent', {
        p_user_id: user.id,
        p_consent_type: 'age_verification',
        p_consent_version: '1.0',
        p_consent_given: true,
        p_ip_address: ipData.ip,
        p_user_agent: navigator.userAgent,
        p_consent_method: 'settings_update'
      });

      setIsVerified(true);
      toast({
        title: "Age verification updated",
        description: "Your age verification has been successfully updated.",
      });
    } catch (error) {
      console.error('Error saving age verification:', error);
      toast({
        title: "Verification failed",
        description: "There was an error processing your age verification. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className={`bg-navy-light rounded-lg p-6 ${className}`}>
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Login Required</h3>
          <p className="text-gray-300">Please log in to manage your age verification settings.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-navy-light rounded-lg p-6 ${className}`}>
      <div className="flex items-center gap-3 mb-4">
        <Calendar className="w-6 h-6 text-emerald-400" />
        <h3 className="text-xl font-semibold text-white">Age Verification</h3>
        {isVerified && (
          <div className="flex items-center gap-1 text-emerald-400">
            <Check className="w-4 h-4" />
            <span className="text-sm">Verified</span>
          </div>
        )}
      </div>

      <div className="space-y-4">
        <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <Shield className="w-4 h-4 text-blue-400 mt-0.5" />
            <div className="text-sm">
              <p className="text-blue-300 font-medium mb-1">Optional Verification</p>
              <p className="text-gray-300">
                Age verification is optional and helps us provide age-appropriate content and comply with data protection laws.
              </p>
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Date of Birth
          </label>
          <input
            type="date"
            value={dateOfBirth}
            onChange={(e) => handleDateChange(e.target.value)}
            max={new Date().toISOString().split('T')[0]}
            className="w-full px-3 py-3 bg-navy border border-gray-600 rounded-lg text-white focus:border-emerald-500 focus:outline-none min-h-[44px]"
          />
          {age !== null && (
            <p className="text-sm text-gray-400 mt-1">
              Age: {age} years old
            </p>
          )}
        </div>

        <div className="flex gap-3">
          <Button
            onClick={handleVerification}
            disabled={loading || !dateOfBirth}
            className="bg-emerald-600 hover:bg-emerald-700 text-white min-h-[44px]"
          >
            {loading ? 'Updating...' : isVerified ? 'Update Verification' : 'Verify Age'}
          </Button>
          
          {isVerified && (
            <div className="flex items-center text-emerald-400 text-sm">
              <Check className="w-4 h-4 mr-1" />
              Verified on {new Date(existingVerification?.created_at).toLocaleDateString()}
            </div>
          )}
        </div>

        <div className="bg-emerald-900/20 border border-emerald-600/30 rounded-lg p-4">
          <h4 className="font-semibold text-emerald-300 mb-2">Privacy Protection</h4>
          <p className="text-sm text-gray-300">
            Your date of birth is used only for age verification and compliance purposes. 
            We follow strict data protection standards for all user information.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AgeVerificationSettings;

import React, { useState, useEffect } from 'react';
import { MapPin, Loader2, Check, X, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { useAuth } from '@/context/AuthContext';
import { locationPreferenceService, UserLocationPreference } from '@/services/location-preference';
import { googleMapsService } from '@/utils/google-maps';
import { Address } from '@/types/location';
import { toast } from '@/hooks/use-toast';

interface LocationPreferenceSectionProps {
  className?: string;
}

export const LocationPreferenceSection: React.FC<LocationPreferenceSectionProps> = ({
  className = ""
}) => {
  const { user } = useAuth();
  const [userLocationPreference, setUserLocationPreference] = useState<UserLocationPreference | null>(null);
  const [isEditingLocation, setIsEditingLocation] = useState(false);
  const [locationQuery, setLocationQuery] = useState('');
  const [locationSuggestions, setLocationSuggestions] = useState<Address[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Load user's current location preference
  useEffect(() => {
    if (user) {
      loadLocationPreference();
    }
  }, [user]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  const loadLocationPreference = async () => {
    if (!user) return;
    
    try {
      setIsLoading(true);
      const preference = await locationPreferenceService.getLocationPreference(user.id);
      setUserLocationPreference(preference);
    } catch (error) {
      console.error('Failed to load location preference:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle location search with Google Maps (matching working implementation)
  const handleLocationSearch = async (query: string) => {
    setLocationQuery(query);

    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    if (query.length < 2) {
      setLocationSuggestions([]);
      return;
    }

    // Debounce search requests (300ms delay)
    const timeout = setTimeout(async () => {
      setIsSearching(true);
      try {
        // Use the same method as the working AuroraBackgroundDemo
        const suggestions = await googleMapsService.searchAddresses(query);
        setLocationSuggestions(suggestions);

        if (import.meta.env.DEV) {
          console.log('Profile location search results:', suggestions.length);
        }
      } catch (error) {
        console.error('Location search failed:', error);
        setLocationSuggestions([]);
      } finally {
        setIsSearching(false);
      }
    }, 300);

    setSearchTimeout(timeout);
  };

  // Handle location selection
  const handleLocationSelect = async (suggestion: Address) => {
    if (!user) return;

    setIsSaving(true);
    try {
      // Get detailed place information if needed
      let coordinates = suggestion.coordinates;
      if (!coordinates && suggestion.display_name) {
        // Fallback to geocoding if coordinates not available
        const geocoded = await googleMapsService.geocodeAddress(suggestion.display_name);
        if (geocoded) {
          coordinates = {
            latitude: geocoded.latitude,
            longitude: geocoded.longitude
          };
        }
      }

      if (!coordinates) {
        throw new Error('Unable to get coordinates for selected location');
      }

      const preference = await locationPreferenceService.saveLocationFromGoogleMaps(
        user.id,
        suggestion.area || suggestion.city || suggestion.display_name,
        coordinates
      );

      setUserLocationPreference(preference);
      setIsEditingLocation(false);
      setLocationQuery('');
      setLocationSuggestions([]);

      // Trigger a custom event to notify other components about location preference change
      window.dispatchEvent(new CustomEvent('locationPreferenceChanged', {
        detail: { preference }
      }));

      toast({
        title: "Location preference saved",
        description: `Your preferred location has been set to ${preference.name}`,
      });

    } catch (error) {
      console.error('Failed to save location preference:', error);
      toast({
        title: "Failed to save location",
        description: error instanceof Error ? error.message : 'Please try again',
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle clearing location preference
  const handleClearLocation = async () => {
    if (!user) return;

    try {
      await locationPreferenceService.clearLocationPreference(user.id);
      setUserLocationPreference(null);

      // Trigger a custom event to notify other components about location preference change
      window.dispatchEvent(new CustomEvent('locationPreferenceChanged', {
        detail: { preference: null }
      }));

      toast({
        title: "Location preference cleared",
        description: "Your preferred location has been removed",
      });
    } catch (error) {
      console.error('Failed to clear location preference:', error);
      toast({
        title: "Failed to clear location",
        description: "Please try again",
        variant: "destructive"
      });
    }
  };

  // Handle detection settings toggle
  const handleDetectionToggle = async (enabled: boolean) => {
    if (!user) return;

    try {
      await locationPreferenceService.updateDetectionSettings(user.id, enabled);
      
      if (userLocationPreference) {
        setUserLocationPreference({
          ...userLocationPreference,
          detectionEnabled: enabled
        });
      }

      toast({
        title: enabled ? "Location detection enabled" : "Location detection disabled",
        description: enabled 
          ? "GPS location will be used when no preferred location is set"
          : "GPS location detection has been disabled",
      });
    } catch (error) {
      console.error('Failed to update detection settings:', error);
      toast({
        title: "Failed to update settings",
        description: "Please try again",
        variant: "destructive"
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <Card className={`backdrop-blur-sm bg-white/10 rounded-xl shadow border border-white/20 ${className}`}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-emerald-400" />
            <span className="ml-2 text-white">Loading location preferences...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`backdrop-blur-sm bg-white/10 rounded-xl shadow border border-white/20 ${className}`}>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <MapPin className="h-5 w-5 text-emerald-400 mr-2" />
          Location Preferences
        </h3>
        
        {/* Current Saved Location */}
        {userLocationPreference && !isEditingLocation ? (
          <div className="bg-emerald-900/20 border border-emerald-700/30 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <MapPin className="h-5 w-5 text-emerald-400 mr-3" />
                <div>
                  <p className="text-white font-medium">{userLocationPreference.name}</p>
                  <p className="text-emerald-300 text-sm">
                    Set on {formatDate(userLocationPreference.setAt)}
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={() => setIsEditingLocation(true)}
                  variant="outline"
                  size="sm"
                  className="text-emerald-400 border-emerald-400 hover:bg-emerald-400 hover:text-black"
                >
                  Change
                </Button>
                <Button
                  onClick={handleClearLocation}
                  variant="outline"
                  size="sm"
                  className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
                >
                  Clear
                </Button>
              </div>
            </div>
          </div>
        ) : !userLocationPreference && !isEditingLocation ? (
          <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center mb-4">
            <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-300 mb-2">No preferred location set</p>
            <p className="text-gray-400 text-sm mb-4">
              Set your preferred location to see nearby venues by default
            </p>
            <Button
              onClick={() => setIsEditingLocation(true)}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              Set Preferred Location
            </Button>
          </div>
        ) : null}

        {/* Location Search Interface */}
        {isEditingLocation && (
          <div className="space-y-4 mb-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search for your location in New Delhi..."
                value={locationQuery}
                onChange={(e) => handleLocationSearch(e.target.value)}
                className="w-full px-4 py-3 pl-12 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20"
              />
              <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-emerald-400" />
              {isSearching && (
                <Loader2 className="absolute right-4 top-1/2 transform -translate-y-1/2 w-4 h-4 animate-spin text-emerald-400" />
              )}
            </div>

            {/* Location Suggestions */}
            {locationSuggestions.length > 0 && (
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {locationSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleLocationSelect(suggestion)}
                    disabled={isSaving}
                    className="w-full text-left p-3 bg-gray-800/50 hover:bg-gray-700/50 rounded-lg border border-gray-600 text-white transition-colors disabled:opacity-50"
                  >
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 text-emerald-400 mr-2 flex-shrink-0" />
                      <div className="truncate">
                        <div className="font-medium">{suggestion.area || suggestion.city}</div>
                        <div className="text-sm text-gray-400 truncate">{suggestion.display_name}</div>
                      </div>
                      {isSaving && (
                        <Loader2 className="h-4 w-4 animate-spin text-emerald-400 ml-auto" />
                      )}
                    </div>
                  </button>
                ))}
              </div>
            )}

            {/* Cancel Button */}
            <div className="flex gap-2">
              <Button
                onClick={() => {
                  setIsEditingLocation(false);
                  setLocationQuery('');
                  setLocationSuggestions([]);
                }}
                variant="outline"
                className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Location Detection Settings */}
        <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
          <div>
            <p className="text-white font-medium">Automatic Location Detection</p>
            <p className="text-gray-400 text-sm">
              Allow GPS location detection when no preferred location is set
            </p>
          </div>
          <Switch
            checked={userLocationPreference?.detectionEnabled ?? true}
            onCheckedChange={handleDetectionToggle}
            className="data-[state=checked]:bg-emerald-600"
          />
        </div>
      </CardContent>
    </Card>
  );
};

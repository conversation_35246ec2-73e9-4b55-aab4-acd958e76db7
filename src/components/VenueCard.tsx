/**
 * 🏟️ MODERN VENUE CARD COMPONENT
 * 
 * Redesigned for Grid२Play with:
 * - Mobile-first responsive design (90vw max 340px width, 220px height)
 * - Glassmorphic dark semi-transparent background
 * - 50% top (image) / 50% bottom (content) layout
 * - Soft drop shadows and 16px border radius
 * - Accessibility-compliant contrast ratios
 * - Subtle scale animations on hover/tap
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Star, MapPin, Navigation } from 'lucide-react';
import { motion } from 'framer-motion';
import { DiscountBadge } from './DiscountBadge';
import { VenueDiscount } from '@/utils/discountUtils';

interface VenueCardProps {
  venue: {
    id: string;
    name: string;
    location: string;
    image_url?: string;
    rating: number;
    total_bookings?: number;
    distance?: number | null;
  };
  discount?: VenueDiscount;
  className?: string;
  variant?: 'default' | 'compact';
}

export function VenueCard({ venue, discount, className = '', variant = 'default' }: VenueCardProps) {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/venues/${venue.id}`);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      navigate(`/venues/${venue.id}`);
    }
  };

  return (
    <motion.div
      className={`
        venue-card group cursor-pointer overflow-hidden
        ${variant === 'compact' ? 'w-full max-w-[280px] h-[180px]' : 'w-[90vw] max-w-[340px] h-[220px]'}
        rounded-2xl
        bg-black/40 backdrop-blur-md border border-white/10
        shadow-[0_8px_32px_rgba(0,0,0,0.3)]
        hover:shadow-[0_12px_40px_rgba(46,125,50,0.4)]
        hover:border-emerald-500/30
        transition-all duration-300 ease-out
        ${className}
      `}
      onClick={handleClick}
      onKeyDown={handleKeyPress}
      role="button"
      tabIndex={0}
      aria-label={`Book ${venue.name} - Rating: ${venue.rating.toFixed(1)}, Location: ${venue.location}`}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Image Section - 50% Top */}
      <div className="relative h-1/2 overflow-hidden">
        <img
          src={venue.image_url || 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=1000'}
          alt={venue.name}
          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
          loading="lazy"
        />
        
        {/* Gradient Overlay for Text Readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
        
        {/* Discount Badge - Top Left */}
        {discount && (
          <div className="absolute top-3 left-3">
            <DiscountBadge discount={discount} />
          </div>
        )}
        
        {/* Rating Badge - Top Right */}
        <div className="absolute top-3 right-3 bg-black/60 backdrop-blur-sm rounded-full px-2 py-1 flex items-center gap-1">
          <Star className="w-3 h-3 text-yellow-400 fill-yellow-400" />
          <span className="text-white text-xs font-medium">
            {venue.rating.toFixed(1)}
          </span>
        </div>
      </div>

      {/* Content Section - 50% Bottom */}
      <div className="h-1/2 p-4 flex flex-col justify-between">
        {/* Venue Name */}
        <div>
          <h3 className="text-white font-semibold text-base leading-tight mb-2 line-clamp-1 group-hover:text-emerald-400 transition-colors duration-200">
            {venue.name}
          </h3>
          
          {/* Location with Icon */}
          <div className="flex items-center gap-1.5 mb-2">
            <MapPin className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
            <span className="text-gray-300 text-sm truncate">
              {venue.location}
            </span>
          </div>
        </div>

        {/* Bottom Row - Distance & Bookings */}
        <div className="flex items-center justify-between">
          {/* Distance */}
          {venue.distance !== null && venue.distance !== undefined && (
            <div className="flex items-center gap-1">
              <Navigation className="w-3 h-3 text-emerald-400" />
              <span className="text-emerald-300 text-xs font-medium">
                {venue.distance < 1 
                  ? `${(venue.distance * 1000).toFixed(0)}m` 
                  : `${venue.distance.toFixed(1)}km`}
              </span>
            </div>
          )}
          
          {/* Bookings Count */}
          {venue.total_bookings && (
            <span className="text-gray-400 text-xs">
              {venue.total_bookings} bookings
            </span>
          )}
        </div>
      </div>
    </motion.div>
  );
}

// Skeleton component for loading states
export function VenueCardSkeleton({ variant = 'default' }: { variant?: 'default' | 'compact' }) {
  return (
    <div className={`
      animate-pulse overflow-hidden
      ${variant === 'compact' ? 'w-full max-w-[280px] h-[180px]' : 'w-[90vw] max-w-[340px] h-[220px]'}
      rounded-2xl
      bg-black/40 backdrop-blur-md border border-white/10
    `}>
      {/* Image Skeleton */}
      <div className="h-1/2 bg-gray-800/50" />
      
      {/* Content Skeleton */}
      <div className="h-1/2 p-4 space-y-3">
        <div className="h-4 bg-gray-700/50 rounded w-3/4" />
        <div className="h-3 bg-gray-700/50 rounded w-1/2" />
        <div className="flex justify-between">
          <div className="h-3 bg-gray-700/50 rounded w-1/4" />
          <div className="h-3 bg-gray-700/50 rounded w-1/4" />
        </div>
      </div>
    </div>
  );
}

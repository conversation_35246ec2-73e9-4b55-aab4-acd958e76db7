/**
 * Error boundary specifically designed to handle authentication-related errors
 * including browser extension interference
 */

import React, { Component, ReactNode } from 'react';
import { isExtensionError } from '@/utils/extensionSafeNavigation';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, ExternalLink } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  isExtensionError: boolean;
  retryCount: number;
}

export class AuthErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      isExtensionError: false,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      isExtensionError: isExtensionError(error)
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('AuthErrorBoundary caught an error:', error, errorInfo);
    
    if (isExtensionError(error)) {
      console.warn('Extension-related error detected in authentication flow');
    }
    
    // Call optional error handler
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        isExtensionError: false,
        retryCount: prevState.retryCount + 1
      }));
    } else {
      // Max retries reached, force page reload
      window.location.reload();
    }
  };

  handleForceReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-screen bg-gradient-to-br from-emerald-900 via-emerald-800 to-emerald-900 flex items-center justify-center p-4">
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 max-w-md w-full border border-white/20">
            <div className="text-center">
              <AlertTriangle className="mx-auto h-12 w-12 text-yellow-400 mb-4" />
              
              <h2 className="text-xl font-semibold text-white mb-2">
                {this.state.isExtensionError ? 'Extension Conflict Detected' : 'Authentication Error'}
              </h2>
              
              <p className="text-gray-300 mb-6">
                {this.state.isExtensionError 
                  ? 'A browser extension is interfering with the authentication process. This is a common issue with certain browser extensions that modify navigation behavior.'
                  : 'An error occurred during the authentication process. Please try again.'
                }
              </p>

              {this.state.isExtensionError && (
                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-6">
                  <h3 className="text-sm font-medium text-yellow-400 mb-2">Recommended Solutions:</h3>
                  <ul className="text-xs text-yellow-300 space-y-1 text-left">
                    <li>• Try disabling browser extensions temporarily</li>
                    <li>• Use an incognito/private browsing window</li>
                    <li>• Clear browser cache and cookies</li>
                    <li>• Try a different browser</li>
                  </ul>
                </div>
              )}

              <div className="space-y-3">
                {this.state.retryCount < this.maxRetries ? (
                  <Button 
                    onClick={this.handleRetry}
                    className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Try Again ({this.maxRetries - this.state.retryCount} attempts left)
                  </Button>
                ) : (
                  <Button 
                    onClick={this.handleForceReload}
                    className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reload Page
                  </Button>
                )}

                <Button 
                  onClick={() => window.location.href = '/'}
                  variant="outline"
                  className="w-full border-white/20 text-white hover:bg-white/10"
                >
                  Return to Home
                </Button>

                {this.state.isExtensionError && (
                  <Button 
                    onClick={() => window.open('https://support.google.com/chrome/answer/187443', '_blank')}
                    variant="ghost"
                    className="w-full text-gray-400 hover:text-white"
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Learn How to Disable Extensions
                  </Button>
                )}
              </div>

              {import.meta.env.DEV && this.state.error && (
                <details className="mt-6 text-left">
                  <summary className="text-sm text-gray-400 cursor-pointer hover:text-white">
                    Technical Details (Development)
                  </summary>
                  <pre className="mt-2 text-xs text-red-300 bg-black/20 p-2 rounded overflow-auto max-h-32">
                    {this.state.error.message}
                    {this.state.error.stack && '\n\n' + this.state.error.stack}
                  </pre>
                </details>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component to wrap components with AuthErrorBoundary
 */
export function withAuthErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  return function WrappedComponent(props: P) {
    return (
      <AuthErrorBoundary fallback={fallback}>
        <Component {...props} />
      </AuthErrorBoundary>
    );
  };
}

/**
 * Hook to handle extension-safe operations
 */
export function useExtensionSafeOperation() {
  const handleOperation = React.useCallback(async (
    operation: () => Promise<void> | void,
    errorMessage: string = 'Operation failed'
  ) => {
    try {
      await operation();
    } catch (error) {
      if (isExtensionError(error)) {
        console.warn('Extension interference detected, operation may have partially succeeded');
        // Don't throw extension errors, just log them
        return;
      }
      
      // Re-throw non-extension errors
      console.error(errorMessage, error);
      throw error;
    }
  }, []);

  return { handleOperation };
}


import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, MapPin, ChevronDown } from 'lucide-react';
import { googleMapsService } from '@/utils/google-maps';
import { Address } from '@/types/location';

const HeroSection: React.FC = () => {
  const navigate = useNavigate();
  const [sportQuery, setSportQuery] = useState('');
  const [locationQuery, setLocationQuery] = useState('');
  const [locationSuggestions, setLocationSuggestions] = useState<Address[]>([]);
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [isLocationSearching, setIsLocationSearching] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<Address | null>(null);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Handle location search with debouncing
  const handleLocationSearch = useCallback(async (query: string) => {
    setLocationQuery(query);

    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    if (query.length < 2) {
      setLocationSuggestions([]);
      setShowLocationSuggestions(false);
      return;
    }

    // Debounce search requests
    const timeout = setTimeout(async () => {
      setIsLocationSearching(true);
      try {
        const suggestions = await googleMapsService.searchAddresses(query);
        setLocationSuggestions(suggestions);
        setShowLocationSuggestions(true);
      } catch (error) {
        console.error('Location search failed:', error);
        setLocationSuggestions([]);
      } finally {
        setIsLocationSearching(false);
      }
    }, 300);

    setSearchTimeout(timeout);
  }, [searchTimeout]);

  // Handle location selection
  const handleLocationSelect = (suggestion: Address) => {
    setSelectedLocation(suggestion);
    setLocationQuery(suggestion.area || suggestion.city || suggestion.formatted_address);
    setShowLocationSuggestions(false);
  };

  // Handle search submission
  const handleSearch = () => {
    const params = new URLSearchParams();

    if (sportQuery.trim()) {
      params.set('sport', sportQuery.trim());
    }

    if (selectedLocation) {
      params.set('lat', selectedLocation.latitude.toString());
      params.set('lng', selectedLocation.longitude.toString());
      params.set('location', encodeURIComponent(selectedLocation.area || selectedLocation.city || selectedLocation.formatted_address));
    }

    navigate(`/venues${params.toString() ? '?' + params.toString() : ''}`);
  };

  return (
    <div className="bg-gradient-to-br from-indigo-900 via-blue-900 to-navy-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
        <div className="flex flex-col items-center text-center">
          <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
            Find and Book Sports Venues <br className="hidden md:block" />
            <span className="text-indigo-400">Instantly</span>
          </h1>
          <p className="text-xl max-w-2xl mb-10 text-gray-300">
            Discover nearby sports facilities, check real-time availability, and book your next game with just a few clicks.
          </p>
          
          <div className="w-full max-w-xl bg-white/10 backdrop-blur-sm p-2 rounded-lg shadow-lg mb-8">
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search sports or venues..."
                  value={sportQuery}
                  onChange={(e) => setSportQuery(e.target.value)}
                  className="w-full bg-white/20 border-0 rounded-md py-3 pl-10 pr-4 text-white placeholder-gray-300 focus:ring-2 focus:ring-indigo-400 focus:outline-none"
                />
              </div>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MapPin className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Location"
                  value={locationQuery}
                  onChange={(e) => handleLocationSearch(e.target.value)}
                  onFocus={() => locationSuggestions.length > 0 && setShowLocationSuggestions(true)}
                  className="w-full bg-white/20 border-0 rounded-md py-3 pl-10 pr-4 text-white placeholder-gray-300 focus:ring-2 focus:ring-indigo-400 focus:outline-none"
                />

                {/* Location Suggestions Dropdown */}
                {showLocationSuggestions && locationSuggestions.length > 0 && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                    {locationSuggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleLocationSelect(suggestion)}
                        className="w-full text-left px-4 py-3 hover:bg-gray-100 text-gray-800 border-b border-gray-200 last:border-b-0"
                      >
                        <div className="font-medium">{suggestion.area || suggestion.city}</div>
                        <div className="text-sm text-gray-600">{suggestion.formatted_address}</div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
              <button
                onClick={handleSearch}
                className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white font-medium py-3 px-6 rounded-md transition duration-150 ease-in-out shadow-md hover:shadow-lg"
              >
                Search
              </button>
            </div>
          </div>
          
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <div className="flex items-center bg-white/10 px-4 py-2 rounded-full">
              <span className="mr-2">⚽</span>
              <span>Football</span>
            </div>
            <div className="flex items-center bg-white/10 px-4 py-2 rounded-full">
              <span className="mr-2">🏏</span>
              <span>Cricket</span>
            </div>
            <div className="flex items-center bg-white/10 px-4 py-2 rounded-full">
              <span className="mr-2">🏸</span>
              <span>Badminton</span>
            </div>
            <div className="flex items-center bg-white/10 px-4 py-2 rounded-full">
              <span className="mr-2">🏀</span>
              <span>Basketball</span>
            </div>
            <div className="flex items-center bg-white/10 px-4 py-2 rounded-full">
              <span className="mr-2">🎾</span>
              <span>Tennis</span>
            </div>
          </div>
        </div>
      </div>
      <div className="w-full h-24 bg-gradient-to-b from-transparent to-gray-50"></div>
    </div>
  );
};

export default HeroSection;

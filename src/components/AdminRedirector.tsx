
import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';

const AdminRedirector = () => {
  const { user, userRole } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  useEffect(() => {
    // Only proceed if user is authenticated and is admin
    if (!user || (userRole !== 'admin' && userRole !== 'super_admin')) {
      return;
    }

    const currentPath = location.pathname;
    const currentHash = location.hash;



    if (isMobile) {
      // Mobile admin routing logic

      // If already on mobile admin home, do nothing
      if (currentPath === '/admin/mobile-home') {
        return;
      }

      // If on other mobile admin routes, allow them
      if (currentPath.startsWith('/admin/') && currentPath.includes('-mobile')) {
        return;
      }

      // If on desktop admin routes or base /admin, redirect to mobile home
      if (currentPath === '/admin' || (currentPath.startsWith('/admin') && !currentPath.includes('-mobile'))) {
        navigate('/admin/mobile-home', { replace: true });
        return;
      }

      // If on non-admin routes, redirect to mobile home
      if (!currentPath.startsWith('/admin')) {
        navigate('/admin/mobile-home', { replace: true });
        return;
      }
    } else {
      // Desktop admin routing logic

      // If on mobile admin routes, redirect to desktop
      if (currentPath.startsWith('/admin/') && currentPath.includes('-mobile')) {
        navigate('/admin#dashboard', { replace: true });
        return;
      }

      // If already on desktop admin routes (except base /admin), allow them
      if (currentPath.startsWith('/admin/') && currentPath !== '/admin/') {
        return;
      }

      // If on non-admin routes, redirect to desktop admin
      if (!currentPath.startsWith('/admin')) {
        navigate('/admin#dashboard', { replace: true });
        return;
      }

      // If at exactly /admin without hash, add dashboard hash
      if (currentPath === '/admin' && !currentHash) {
        navigate('/admin#dashboard', { replace: true });
        return;
      }
    }
  }, [user, userRole, location.pathname, location.hash, navigate, isMobile]);

  return null;
};

export default AdminRedirector;

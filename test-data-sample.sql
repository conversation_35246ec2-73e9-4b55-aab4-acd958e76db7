-- Test Data Sample for Grid2Play Daily Venue Reports
-- Use this to test the get_venue_daily_summary() function and ViaSocket automation

-- Test the function with current date
SELECT 
  venue_id,
  venue_name,
  admin_phone,
  admin_name,
  report_date,
  total_bookings,
  confirmed_bookings,
  cancelled_bookings,
  gross_revenue,
  net_revenue,
  coupon_usage_count
FROM get_venue_daily_summary(CURRENT_DATE);

-- Test with a specific date (replace with actual date)
SELECT 
  venue_id,
  venue_name,
  admin_phone,
  admin_name,
  report_date,
  total_bookings,
  confirmed_bookings,
  cancelled_bookings,
  gross_revenue,
  net_revenue,
  coupon_usage_count
FROM get_venue_daily_summary('2025-01-04');

-- Expected output format for ViaSocket:
/*
[
  {
    "venue_id": "123e4567-e89b-12d3-a456-426614174000",
    "venue_name": "SportZone Arena",
    "admin_phone": "9876543210",
    "admin_name": "<PERSON>",
    "report_date": "04/01/2025",
    "total_bookings": "12",
    "confirmed_bookings": "10",
    "cancelled_bookings": "2", 
    "gross_revenue": "₹5,250",
    "net_revenue": "₹4,987",
    "coupon_usage_count": "3"
  },
  {
    "venue_id": "456e7890-e89b-12d3-a456-426614174001",
    "venue_name": "Elite Sports Club",
    "admin_phone": "9123456789",
    "admin_name": "Sarah Manager",
    "report_date": "04/01/2025",
    "total_bookings": "8",
    "confirmed_bookings": "7",
    "cancelled_bookings": "1",
    "gross_revenue": "₹3,200",
    "net_revenue": "₹3,040",
    "coupon_usage_count": "1"
  }
]
*/

-- Verify venue admin relationships
SELECT 
  v.name as venue_name,
  p.full_name as admin_name,
  p.phone as admin_phone,
  p.phone_verified
FROM venues v
JOIN venue_admins va ON v.id = va.venue_id
JOIN profiles p ON va.user_id = p.id
WHERE v.is_active = true
  AND p.phone IS NOT NULL
  AND p.phone != ''
  AND p.phone != '000000000'
ORDER BY v.name;

-- Check recent bookings for testing
SELECT 
  v.name as venue_name,
  COUNT(b.id) as booking_count,
  SUM(CASE WHEN b.status = 'confirmed' THEN b.total_price ELSE 0 END) as confirmed_revenue
FROM venues v
LEFT JOIN courts c ON v.id = c.venue_id
LEFT JOIN bookings b ON c.id = b.court_id 
WHERE b.booking_date >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY v.id, v.name
HAVING COUNT(b.id) > 0
ORDER BY booking_count DESC;

-- Check coupon usage for testing
SELECT 
  v.name as venue_name,
  COUNT(cu.id) as coupon_usage_count,
  SUM(cu.discount_amount) as total_discount
FROM venues v
LEFT JOIN courts c ON v.id = c.venue_id
LEFT JOIN bookings b ON c.id = b.court_id
LEFT JOIN coupon_usage cu ON b.id = cu.booking_id
WHERE DATE(cu.created_at) >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY v.id, v.name
HAVING COUNT(cu.id) > 0
ORDER BY coupon_usage_count DESC;

/**
 * ViaSocket Data Formatting Logic for Grid2Play Daily Venue Reports
 * This JavaScript code handles data transformation for MSG91 WhatsApp template variables
 */

// Main data processing function for ViaSocket
function processVenueReports(input) {
  try {
    // Input validation
    if (!input || !Array.isArray(input.venue_reports)) {
      console.error('Invalid input: venue_reports array not found');
      return { processed_venues: [], errors: ['Invalid input data'] };
    }

    const processed_venues = [];
    const errors = [];

    input.venue_reports.forEach((venue, index) => {
      try {
        // Process each venue record
        const processedVenue = {
          venue_id: venue.venue_id,
          venue_name: formatVenueName(venue.venue_name),
          admin_phone: formatPhoneNumber(venue.admin_phone),
          admin_name: venue.admin_name || 'Admin',
          report_date: formatReportDate(venue.report_date),
          total_bookings: formatBookingCount(venue.total_bookings),
          confirmed_bookings: formatBookingCount(venue.confirmed_bookings),
          cancelled_bookings: formatBookingCount(venue.cancelled_bookings),
          gross_revenue: formatRevenue(venue.gross_revenue),
          net_revenue: formatRevenue(venue.net_revenue),
          coupon_usage_count: formatCouponCount(venue.coupon_usage_count)
        };

        // Validate processed venue data
        if (isValidVenueData(processedVenue)) {
          processed_venues.push(processedVenue);
        } else {
          errors.push(`Invalid data for venue: ${venue.venue_name || 'Unknown'}`);
        }

      } catch (error) {
        errors.push(`Error processing venue ${index}: ${error.message}`);
      }
    });

    return {
      processed_venues: processed_venues,
      total_count: processed_venues.length,
      error_count: errors.length,
      errors: errors
    };

  } catch (error) {
    console.error('Critical error in processVenueReports:', error);
    return {
      processed_venues: [],
      total_count: 0,
      error_count: 1,
      errors: [error.message]
    };
  }
}

// Format phone number to international format
function formatPhoneNumber(phone) {
  if (!phone) return null;
  
  // Convert to string and clean
  let formatted = phone.toString().trim().replace(/\s+/g, '');
  
  // Remove any non-digit characters except +
  formatted = formatted.replace(/[^\d+]/g, '');
  
  // Handle different input formats
  if (formatted.startsWith('0')) {
    // Remove leading 0 and add +91
    formatted = '+91' + formatted.substring(1);
  } else if (formatted.startsWith('91') && formatted.length === 12) {
    // Add + prefix if missing
    formatted = '+' + formatted;
  } else if (!formatted.startsWith('+91') && formatted.length === 10) {
    // Add +91 prefix for 10-digit numbers
    formatted = '+91' + formatted;
  } else if (!formatted.startsWith('+')) {
    // Add + if missing but has country code
    formatted = '+' + formatted;
  }
  
  // Validate final format (+91 followed by 10 digits)
  const phoneRegex = /^\+91\d{10}$/;
  return phoneRegex.test(formatted) ? formatted : null;
}

// Format venue name for display
function formatVenueName(name) {
  if (!name) return 'Unknown Venue';
  
  // Trim and capitalize first letter of each word
  return name.trim()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

// Format report date to DD/MM/YYYY
function formatReportDate(date) {
  if (!date) return new Date().toLocaleDateString('en-GB');
  
  try {
    // If already in DD/MM/YYYY format, return as is
    if (typeof date === 'string' && /^\d{2}\/\d{2}\/\d{4}$/.test(date)) {
      return date;
    }
    
    // Parse and format date
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-GB'); // DD/MM/YYYY format
  } catch (error) {
    return new Date().toLocaleDateString('en-GB');
  }
}

// Format booking counts
function formatBookingCount(count) {
  if (count === null || count === undefined) return '0';
  
  // Convert to number and ensure non-negative
  const numCount = parseInt(count, 10);
  return isNaN(numCount) ? '0' : Math.max(0, numCount).toString();
}

// Format revenue with ₹ symbol
function formatRevenue(revenue) {
  if (!revenue) return '₹0';
  
  // If already formatted with ₹, return as is
  if (typeof revenue === 'string' && revenue.includes('₹')) {
    return revenue;
  }
  
  try {
    // Parse numeric value
    const numRevenue = parseFloat(revenue.toString().replace(/[^\d.]/g, ''));
    
    if (isNaN(numRevenue)) return '₹0';
    
    // Format with Indian number system (lakhs, crores)
    return '₹' + numRevenue.toLocaleString('en-IN', {
      maximumFractionDigits: 0,
      minimumFractionDigits: 0
    });
  } catch (error) {
    return '₹0';
  }
}

// Format coupon usage count
function formatCouponCount(count) {
  if (count === null || count === undefined) return '0';
  
  const numCount = parseInt(count, 10);
  return isNaN(numCount) ? '0' : Math.max(0, numCount).toString();
}

// Validate processed venue data
function isValidVenueData(venue) {
  return (
    venue.venue_name &&
    venue.admin_phone &&
    venue.admin_phone.startsWith('+91') &&
    venue.admin_phone.length === 13 &&
    venue.report_date &&
    venue.total_bookings !== undefined &&
    venue.gross_revenue !== undefined
  );
}

// ViaSocket execution wrapper
// This is the main function that ViaSocket will call
const output = processVenueReports(input);

// Example usage and testing
/*
// Test data
const testInput = {
  venue_reports: [
    {
      venue_id: "123e4567-e89b-12d3-a456-426614174000",
      venue_name: "sportzone arena",
      admin_phone: "9876543210",
      admin_name: "John Admin",
      report_date: "2025-01-04",
      total_bookings: "12",
      confirmed_bookings: "10",
      cancelled_bookings: "2",
      gross_revenue: "5250",
      net_revenue: "4987",
      coupon_usage_count: "3"
    }
  ]
};

// Expected output
{
  processed_venues: [
    {
      venue_id: "123e4567-e89b-12d3-a456-426614174000",
      venue_name: "Sportzone Arena",
      admin_phone: "+919876543210",
      admin_name: "John Admin",
      report_date: "04/01/2025",
      total_bookings: "12",
      confirmed_bookings: "10",
      cancelled_bookings: "2",
      gross_revenue: "₹5,250",
      net_revenue: "₹4,987",
      coupon_usage_count: "3"
    }
  ],
  total_count: 1,
  error_count: 0,
  errors: []
}
*/

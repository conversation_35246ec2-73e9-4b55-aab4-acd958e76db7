-- =====================================================
-- GRID२PLAY TDS IMPLEMENTATION ROLLBACK PLAN
-- =====================================================
-- Created: 2025-07-12
-- Purpose: Complete rollback strategy for TDS implementation
-- 
-- CRITICAL: Execute these commands in EXACT ORDER for safe rollback
-- =====================================================

-- =====================================================
-- STEP 1: BACKUP CURRENT FUNCTION DEFINITIONS
-- =====================================================

-- Note: Before implementing TDS, backup current function definitions
-- These functions will be modified and need rollback capability:
-- 1. get_custom_revenue_with_option_b()
-- 2. get_admin_dashboard_stats_optimized() 
-- 3. create_weekly_settlement()
-- 4. get_daily_revenue_report()

-- =====================================================
-- STEP 2: ROLLBACK DATABASE SCHEMA CHANGES
-- =====================================================

-- A. Remove TDS columns from venues table
ALTER TABLE venues DROP COLUMN IF EXISTS tds_rate;
ALTER TABLE venues DROP COLUMN IF EXISTS pan_number;
ALTER TABLE venues DROP COLUMN IF EXISTS tds_category;

-- B. Remove TDS columns from daily_earnings table
ALTER TABLE daily_earnings DROP COLUMN IF EXISTS online_tds_amount;
ALTER TABLE daily_earnings DROP COLUMN IF EXISTS tds_rate;

-- C. Remove TDS columns from settlements table  
ALTER TABLE settlements DROP COLUMN IF EXISTS total_tds_amount;
ALTER TABLE settlements DROP COLUMN IF EXISTS final_settlement_amount;
ALTER TABLE settlements DROP COLUMN IF EXISTS tds_certificate_generated;

-- =====================================================
-- STEP 3: ROLLBACK FUNCTION MODIFICATIONS
-- =====================================================

-- Note: Original function definitions will be restored from backup
-- Functions to restore:
-- 1. get_custom_revenue_with_option_b() - Remove TDS calculations
-- 2. get_admin_dashboard_stats_optimized() - Remove TDS logic
-- 3. create_weekly_settlement() - Remove TDS aggregation
-- 4. get_daily_revenue_report() - Remove TDS from reports

-- =====================================================
-- STEP 4: REMOVE TDS-SPECIFIC FUNCTIONS
-- =====================================================

-- Drop TDS calculation functions (if created)
DROP FUNCTION IF EXISTS calculate_tds_amount(DECIMAL, DECIMAL);
DROP FUNCTION IF EXISTS calculate_online_settlement_with_tds(DECIMAL, DECIMAL, DECIMAL);
DROP FUNCTION IF EXISTS get_venue_tds_details(UUID);
DROP FUNCTION IF EXISTS generate_monthly_tds_certificate(UUID, TEXT);
DROP FUNCTION IF EXISTS validate_tds_rate(DECIMAL);

-- =====================================================
-- STEP 5: ROLLBACK DATA MIGRATIONS
-- =====================================================

-- Note: If any data was migrated or updated during TDS implementation,
-- restore from backup or recalculate without TDS

-- Example: If daily_earnings were recalculated with TDS
-- UPDATE daily_earnings SET 
--   online_net_revenue = online_gross_revenue - online_platform_fees
-- WHERE online_tds_amount IS NOT NULL;

-- =====================================================
-- STEP 6: VERIFICATION QUERIES
-- =====================================================

-- Verify venues table structure (should match original)
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_name = 'venues' ORDER BY ordinal_position;

-- Verify daily_earnings table structure (should match original)  
SELECT column_name, data_type FROM information_schema.columns
WHERE table_name = 'daily_earnings' ORDER BY ordinal_position;

-- Verify settlements table structure (should match original)
SELECT column_name, data_type FROM information_schema.columns
WHERE table_name = 'settlements' ORDER BY ordinal_position;

-- Verify functions are restored
SELECT routine_name FROM information_schema.routines 
WHERE routine_name IN (
  'get_custom_revenue_with_option_b',
  'get_admin_dashboard_stats_optimized', 
  'create_weekly_settlement',
  'get_daily_revenue_report'
);

-- =====================================================
-- STEP 7: TEST ROLLBACK SUCCESS
-- =====================================================

-- Test revenue calculations work without TDS
SELECT * FROM get_custom_revenue_with_option_b(
  '2025-07-01'::date, 
  '2025-07-12'::date, 
  NULL::uuid
) LIMIT 5;

-- Test dashboard stats work without TDS
SELECT * FROM get_admin_dashboard_stats_optimized(NULL::uuid) LIMIT 5;

-- =====================================================
-- ROLLBACK COMPLETION CHECKLIST
-- =====================================================

-- [ ] All TDS columns removed from tables
-- [ ] All TDS functions dropped
-- [ ] Original functions restored from backup
-- [ ] Revenue calculations working without TDS
-- [ ] Dashboard displaying correctly
-- [ ] Settlement creation working
-- [ ] No TDS references in codebase
-- [ ] Frontend displaying original revenue structure

-- =====================================================
-- EMERGENCY ROLLBACK (If needed during implementation)
-- =====================================================

-- If TDS implementation fails mid-process, execute:
-- 1. Stop all cron jobs
-- 2. Rollback database changes (above steps)
-- 3. Restore function backups
-- 4. Restart application
-- 5. Verify system functionality

-- =====================================================
-- END OF ROLLBACK PLAN
-- =====================================================

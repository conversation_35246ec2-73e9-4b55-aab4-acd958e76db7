{"workflow_name": "Grid2Play Daily Venue Reports", "description": "Automated daily venue performance reports sent to venue administrators via MSG91 WhatsApp", "schedule": {"type": "daily", "time": "23:00", "timezone": "Asia/Kolkata", "enabled": true}, "steps": [{"step_id": 1, "name": "Fetch Venue Daily Summary", "type": "postgresql_query", "config": {"connection": "grid2play_database", "query": "SELECT venue_id, venue_name, admin_phone, admin_name, report_date, total_bookings, confirmed_bookings, cancelled_bookings, gross_revenue, net_revenue, coupon_usage_count FROM get_venue_daily_summary(CURRENT_DATE);", "timeout": 30, "retry_count": 3, "retry_delay": 300}, "output_variable": "venue_reports"}, {"step_id": 2, "name": "Process Phone Numbers", "type": "javascript_processor", "config": {"code": "const formatPhone = (phone) => { if (!phone) return null; let formatted = phone.toString().trim(); if (formatted.startsWith('0')) { formatted = '+91' + formatted.substring(1); } else if (!formatted.startsWith('+91')) { formatted = '+91' + formatted; } return formatted; }; output = input.venue_reports.map(record => ({ ...record, admin_phone: formatPhone(record.admin_phone) })).filter(record => record.admin_phone && record.admin_phone.length >= 13);"}, "input_variable": "venue_reports", "output_variable": "processed_venues"}, {"step_id": 3, "name": "Send WhatsApp Reports", "type": "iterator", "config": {"input_array": "processed_venues", "delay_between_items": 2000, "max_concurrent": 1, "continue_on_error": true}, "actions": [{"action_id": "3a", "name": "Send MSG91 WhatsApp Message", "type": "msg91_whatsapp", "config": {"integrated_number": "919211848599", "template_name": "daily_venue_report", "language_code": "en_US", "namespace": "380c0d5c_8b3e_43ac_a4a3_183fea1845af", "recipient": "{{current_item.admin_phone}}", "template_variables": {"header_1": "{{current_item.venue_name}}", "body_1": "{{current_item.venue_name}}", "body_2": "{{current_item.report_date}}", "body_3": "{{current_item.total_bookings}}", "body_4": "{{current_item.confirmed_bookings}}", "body_5": "{{current_item.cancelled_bookings}}", "body_6": "{{current_item.gross_revenue}}", "body_7": "{{current_item.net_revenue}}", "body_8": "{{current_item.coupon_usage_count}}"}, "retry_count": 2, "retry_delay": 5000}}]}, {"step_id": 4, "name": "Log Execution Summary", "type": "webhook", "config": {"url": "https://your-grid2play-domain.com/api/automation-logs", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer YOUR_API_KEY"}, "body": {"automation_type": "daily_venue_reports", "execution_date": "{{current_date}}", "total_venues": "{{processed_venues.length}}", "successful_sends": "{{step_3_success_count}}", "failed_sends": "{{step_3_error_count}}", "execution_time": "{{workflow_duration}}", "status": "completed"}}}], "error_handling": {"on_database_error": {"action": "retry", "max_retries": 3, "retry_delay": 300, "fallback": "send_admin_notification"}, "on_msg91_error": {"action": "continue", "log_error": true, "notify_admin": false}, "on_critical_error": {"action": "stop_workflow", "send_notification": true, "notification_recipients": ["<EMAIL>"]}}, "monitoring": {"success_threshold": 0.8, "alert_on_failure_rate": 0.3, "daily_summary_report": true, "log_retention_days": 30}}
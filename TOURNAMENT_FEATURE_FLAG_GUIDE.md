# Grid२Play Tournament Feature Flag Guide

## Overview

The tournament system in Grid२Play has been temporarily hidden from end users using feature flags while preserving all backend infrastructure and admin functionality. This allows for controlled rollout when the tournament system is fully ready.

## Current Status

- **User-facing tournament features**: ❌ **DISABLED** (hidden from regular users)
- **Admin tournament features**: ✅ **ENABLED** (accessible to admins for development/testing)
- **Backend infrastructure**: ✅ **PRESERVED** (all database tables, functions, and APIs intact)
- **Tournament routes**: ❌ **DISABLED** (not accessible via navigation)

## How to Enable Tournament Features

### 1. Environment Configuration

Update your `.env.local` file:

```bash
# Feature Flags
# Set to 'true' to enable tournament features for users
VITE_ENABLE_TOURNAMENTS=true
```

### 2. Restart Development Server

After changing the environment variable, restart your development server:

```bash
npm run dev
# or
yarn dev
```

### 3. Verify Tournament Features Are Enabled

When tournaments are enabled, users will see:

- **Header Navigation**: "Tournaments" link in desktop and mobile navigation
- **Homepage**: "Host & Join Tournaments" feature card in the "For Athletes" section
- **More Page**: Tournament section card with "Go to Tournaments" button
- **Tournament Routes**: All tournament pages accessible at `/tournaments/*`

## What's Hidden When Disabled

### User-Facing Elements Removed:
1. **Navigation Links**:
   - Desktop header navigation: "Tournaments" link
   - Mobile header navigation: "Tournaments" link

2. **Homepage Content**:
   - "Host & Join Tournaments" feature card
   - Tournament references in quick actions

3. **More Page**:
   - Tournament section card

4. **Routes**:
   - `/tournaments` - Tournament welcome page
   - `/tournaments/browse` - Tournament discovery
   - `/tournaments/:slug` - Tournament details
   - `/tournaments/host` - Host tournament
   - `/tournaments/organizer` - Organizer dashboard
   - `/tournaments/manage` - Tournament management
   - `/tournaments/legacy/*` - Legacy tournament routes

### What Remains Available:
1. **Admin Access**: Tournament features remain fully accessible to admin users
2. **Backend Infrastructure**: All database tables, functions, and APIs
3. **Tournament Components**: All React components preserved for future use
4. **API Endpoints**: Tournament-related API endpoints remain functional

## Implementation Details

### Feature Flag System

The feature flag system is implemented in `src/utils/featureFlags.ts`:

```typescript
export const FEATURE_FLAGS = {
  TOURNAMENTS: import.meta.env.VITE_ENABLE_TOURNAMENTS === 'true',
} as const;

export const useFeatureFlag = (feature: keyof typeof FEATURE_FLAGS): boolean => {
  return FEATURE_FLAGS[feature];
};
```

### Components Modified

1. **Header.tsx**: Tournament navigation links wrapped with feature flag
2. **Index.tsx**: Tournament feature card filtered from homepage
3. **MorePage.tsx**: Tournament section conditionally rendered
4. **App.tsx**: Tournament routes wrapped with feature flag
5. **BottomNav.tsx**: Already clean (no tournament items)

### Admin Access Preserved

Tournament features remain fully accessible to admin users through:
- Direct URL access (admins can still navigate to tournament URLs)
- Admin dashboard links
- Backend API access
- Database management tools

## Testing

### Test Tournament Features Disabled (Default)
1. Set `VITE_ENABLE_TOURNAMENTS=false` in `.env.local`
2. Restart development server
3. Verify no tournament links appear in navigation
4. Verify no tournament content on homepage
5. Verify tournament routes return 404 or redirect

### Test Tournament Features Enabled
1. Set `VITE_ENABLE_TOURNAMENTS=true` in `.env.local`
2. Restart development server
3. Verify tournament links appear in navigation
4. Verify tournament content appears on homepage
5. Verify all tournament routes are accessible

## Future Activation

When ready to launch tournaments:

1. **Update Environment**: Set `VITE_ENABLE_TOURNAMENTS=true` in production
2. **Deploy**: Deploy the updated environment configuration
3. **Verify**: Confirm all tournament features are working correctly
4. **Monitor**: Monitor user engagement and system performance

## Rollback Plan

If issues arise after enabling tournaments:

1. **Quick Disable**: Set `VITE_ENABLE_TOURNAMENTS=false`
2. **Redeploy**: Deploy the updated configuration
3. **Verify**: Confirm tournament features are hidden again

## Notes

- Feature flags are evaluated at build time, so environment changes require a restart
- Admin users always have access to tournament features regardless of the flag
- All tournament backend infrastructure remains intact and functional
- This approach allows for safe, controlled rollout of tournament features
